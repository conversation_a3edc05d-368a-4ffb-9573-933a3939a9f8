import { _ as _export_sfc, b as useUserStore, f as useRoute, a as __nuxt_component_0$2, c as useRuntimeConfig, d as useState, L as Loader, S as Snackbar, e as _sfc_main$1$1, j as useRouter, i as useCookie } from './server.mjs';
import { defineComponent, markRaw, computed, mergeProps, withCtx, createBlock, createTextVNode, openBlock, createVNode, resolveDynamicComponent, unref, ref, watch, resolveComponent, useSSRContext } from 'vue';
import { ssrRenderAttrs, ssrRenderComponent, ssrRenderClass, ssrRenderVNode, ssrRenderList, ssrRenderStyle, ssrInterpolate, ssrRenderAttr, ssrIncludeBooleanAttr, ssrLooseContain, ssrLooseEqual } from 'vue/server-renderer';
import { Cropper } from 'vue-advanced-cropper';
import { u as useAppSeoMeta } from './useAppSeoMeta-HWukB6FN.mjs';
import { useRouter as useRouter$1 } from 'vue-router';
import '../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:url';
import '@iconify/utils';
import 'node:crypto';
import 'consola';
import 'node:path';
import 'better-sqlite3';
import 'ipx';
import 'pinia';
import 'cookie';
import '@iconify/vue';
import 'tailwindcss/colors';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import 'unhead/server';
import 'devalue';
import 'unhead/plugins';
import 'unhead/utils';

const _sfc_main$7 = /* @__PURE__ */ defineComponent({
  __name: "Profile",
  __ssrInlineRender: true,
  setup(__props) {
    const userStore = useUserStore();
    const { public: { BASE_URL } } = useRuntimeConfig();
    const isLoading = useState("globalLoader", () => false);
    const hasError = ref(false);
    const fetchingProfile = ref(false);
    const showDeleteModal = ref(false);
    const deleteReason = ref("");
    const confirmDelete = ref(false);
    const isDeleting = ref(false);
    const showValidation = ref(false);
    const showCropperModal = ref(false);
    const showEditModal = ref(false);
    const editProfile = ref({
      firstname: "",
      lastname: "",
      email: "",
      phone: "",
      dob: "",
      gender: "",
      profileImage: null,
      profileImageUrl: ""
    });
    const isSaving = ref(false);
    ref(false);
    const cropperRef = ref(null);
    const snackbarRef = ref(null);
    useAppSeoMeta({
      title: "Profile | Bookslotz",
      description: "View and manage your profile details including email, phone number, date of birth, and gender on Bookslotz."
    });
    const fetchProfileData = async () => {
      if (!userStore.loggedIn || !userStore.token) return;
      fetchingProfile.value = true;
      isLoading.value = true;
      try {
        const guestId = useCookie("guestId").value;
        const body = new URLSearchParams();
        body.append("guestId", guestId || "");
        const response = await fetch(`${BASE_URL}/v1/client/auth/profile`, {
          method: "POST",
          headers: {
            "Content-Type": "application/x-www-form-urlencoded",
            token: `${userStore.token}`
          },
          body
        });
        const data = await response.json();
        if (data.status && data.status_code === 200 && data.data) {
          const c = data.data.clientData;
          const d = data.data.clientsDetails;
          const profileData = {
            firstname: c.firstname || "",
            lastname: c.lastname || "",
            email: c.email || "",
            phone: (d == null ? void 0 : d.phone) || "",
            dob: (d == null ? void 0 : d.dob) ? d.dob.slice(0, 10) : "",
            gender: (d == null ? void 0 : d.gender) || "",
            profileImageUrl: c.profileImage ? `${BASE_URL}/wp/${c.profileImage}` : ""
          };
          editProfile.value = { ...profileData, profileImage: null };
          userStore.setUser({
            email: c.email || "",
            name: `${c.firstname || ""} ${c.lastname || ""}`.trim(),
            profileFetched: true
          });
          return profileData;
        } else {
          throw new Error(data.msg || "Failed to fetch profile");
        }
      } catch (error) {
        console.error("Error fetching profile:", error);
        hasError.value = true;
      } finally {
        fetchingProfile.value = false;
        isLoading.value = false;
      }
    };
    watch(
      () => userStore.profileFetched,
      async (newValue) => {
        if (newValue && userStore.loggedIn) {
          await fetchProfileData();
        }
      }
    );
    return (_ctx, _push, _parent, _attrs) => {
      const _component_Error = resolveComponent("Error");
      const _component_NuxtLink = __nuxt_component_0$2;
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "min-h-screen bg-white flex justify-center items-start relative" }, _attrs))}>`);
      if (unref(isLoading) || fetchingProfile.value) {
        _push(ssrRenderComponent(Loader, null, null, _parent));
      } else {
        _push(`<!---->`);
      }
      _push(ssrRenderComponent(Snackbar, {
        ref_key: "snackbarRef",
        ref: snackbarRef
      }, null, _parent));
      if (hasError.value) {
        _push(ssrRenderComponent(_component_Error, { error: { statusCode: 500, message: "Something went wrong. Please try again later." } }, null, _parent));
      } else {
        _push(`<!---->`);
      }
      if (unref(userStore).loggedIn && !hasError.value && !fetchingProfile.value) {
        _push(`<div class="w-full max-w-7xl xl:pr-45"><h2 class="text-[25px] font-semibold mb-6">Profile</h2><div class="space-y-6 rounded shadow-sm border border-gray-100 p-6"><div class="flex flex-col items-center"><div class="relative w-24 h-24 mb-4">`);
        if (editProfile.value.profileImageUrl) {
          _push(`<img${ssrRenderAttr("src", editProfile.value.profileImageUrl)} class="w-24 h-24 rounded-full object-cover border-2 border-white" alt="Profile Image">`);
        } else {
          _push(`<div class="w-24 h-24 rounded-full bg-purple-200 flex items-center justify-center border-2 border-purple-200"><svg class="w-12 h-12 text-purple-600" fill="currentColor" viewBox="0 0 24 24"><path d="M12 12c2.7 0 5-2.3 5-5s-2.3-5-5-5-5 2.3-5 5 2.3 5 5 5Zm0 2c-3.3 0-10 1.7-10 5v3h20v-3c0-3.3-6.7-5-10-5Z"></path></svg></div>`);
        }
        _push(`<button class="absolute bottom-1 right-1 bg-white rounded-full p-1 shadow hover:bg-gray-100 transition cursor-pointer"><svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path d="M15.232 5.232l3.536 3.536M9 13l6.586-6.586a2 2 0 112.828 2.828L11.828 15.828a2 2 0 01-1.414.586H7v-3a2 2 0 01.586-1.414z"></path></svg></button></div><h3 class="text-[16px] text-gray-400">Profile Info</h3><p class="text-[20px] font-semibold text-black-700">${ssrInterpolate(editProfile.value.firstname)}</p></div><div class="bg-white border border-gray-200 rounded-lg p-6"><div class="space-y-4"><div class="flex flex-col sm:flex-row sm:justify-between items-center text-center sm:text-left"><span class="text-gray-500 text-[16px] mb-1 sm:mb-0">Email</span><span class="text-black-900 text-[16px]"><span>${ssrInterpolate(editProfile.value.email || "----------")}</span></span></div><div class="flex flex-col sm:flex-row sm:justify-between items-center text-center sm:text-left"><span class="text-gray-500 text-[16px] mb-1 sm:mb-0">Phone</span><span class="text-black-900 text-[16px]"><span>${ssrInterpolate(editProfile.value.phone || "----------")}</span></span></div><div class="flex flex-col sm:flex-row sm:justify-between items-center text-center sm:text-left"><span class="text-gray-500 text-[16px] mb-1 sm:mb-0">Date of birth</span><span class="text-black-900 text-[16px]"><span>${ssrInterpolate(editProfile.value.dob || "----------")}</span></span></div><div class="flex flex-col sm:flex-row sm:justify-between items-center text-center sm:text-left"><span class="text-gray-500 text-[16px] mb-1 sm:mb-0">Gender</span><span class="text-black-900 text-[16px]"><span>${ssrInterpolate(editProfile.value.gender || "----------")}</span></span></div></div></div><div class="border border-gray-200 rounded-lg p-2"><button class="flex items-center justify-center sm:justify-start gap-2 px-4 py-2 text-red-500 hover:text-red-700 cursor-pointer w-flat"><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 6h18"></path><path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path><path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path></svg> Delete account </button></div></div></div>`);
      } else if (!unref(userStore).loggedIn && !hasError.value) {
        _push(`<div class="w-full max-w-7xl text-center"><h2 class="text-[25px] font-semibold mb-6">Profile</h2><p class="text-lg text-gray-600 mb-4">Please log in to view your profile.</p>`);
        _push(ssrRenderComponent(_component_NuxtLink, {
          to: "/auth/login",
          class: "inline-block bg-[#7B27E7] text-white text-base font-medium px-6 py-2 rounded-full hover:bg-[#6a1cd6] transition-colors"
        }, {
          default: withCtx((_, _push2, _parent2, _scopeId) => {
            if (_push2) {
              _push2(` Log In `);
            } else {
              return [
                createTextVNode(" Log In ")
              ];
            }
          }),
          _: 1
        }, _parent));
        _push(`</div>`);
      } else {
        _push(`<!---->`);
      }
      if (showDeleteModal.value) {
        _push(`<div class="fixed inset-0 bg-black/50 bg-opacity-50 flex items-center justify-center p-4 z-50"><div class="bg-white rounded-lg p-6 w-full max-w-md mx-auto"><div class="flex items-center justify-between mb-4"><h3 class="text-lg font-semibold text-gray-900">Delete Account</h3><button class="text-gray-400 hover:text-gray-600"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg></button></div><div class="mb-4"><div class="flex items-start gap-3 p-3 bg-red-50 border border-red-200 rounded-lg"><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-red-500 flex-shrink-0 mt-0.5"><path d="m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z"></path><path d="M12 9v4"></path><path d="m12 17 .01 0"></path></svg><div><p class="text-sm font-medium text-red-800">Warning: This action cannot be undone</p><p class="text-sm text-red-700 mt-1"> Deleting your account will permanently remove all your data, appointments, and preferences. </p></div></div></div><div class="mb-4"><label class="block text-sm font-medium text-gray-700 mb-2"> Reason for deletion <span class="text-red-500">*</span></label><textarea placeholder="Please let us know why you&#39;re deleting your account..." class="${ssrRenderClass([{ "border-red-500": deleteReason.value.trim().length === 0 && showValidation.value }, "w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500 resize-none"])}" rows="3" maxlength="500" required>${ssrInterpolate(deleteReason.value)}</textarea><p class="text-xs text-gray-500 mt-1">${ssrInterpolate(deleteReason.value.length)}/500 characters</p>`);
        if (deleteReason.value.trim().length === 0 && showValidation.value) {
          _push(`<p class="text-xs text-red-500 mt-1"> Please provide a reason for deletion </p>`);
        } else {
          _push(`<!---->`);
        }
        _push(`</div><div class="mb-6"><label class="flex items-start gap-2 cursor-pointer"><input type="checkbox"${ssrIncludeBooleanAttr(Array.isArray(confirmDelete.value) ? ssrLooseContain(confirmDelete.value, null) : confirmDelete.value) ? " checked" : ""} class="mt-1 rounded border-gray-300 text-red-600 focus:ring-red-500"><span class="text-sm text-gray-700"> I understand that this action is permanent and cannot be undone. I want to delete my account and all associated data. </span></label></div><div class="flex gap-3"><button class="flex-1 px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg font-medium transition-colors"> Cancel </button><button${ssrIncludeBooleanAttr(!confirmDelete.value || !deleteReason.value.trim() || isDeleting.value) ? " disabled" : ""} class="flex-1 px-4 py-2 bg-red-600 text-white rounded-lg font-medium hover:bg-red-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors flex items-center justify-center gap-2">`);
        if (isDeleting.value) {
          _push(`<svg class="animate-spin h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>`);
        } else {
          _push(`<!---->`);
        }
        _push(` ${ssrInterpolate(isDeleting.value ? "Deleting..." : "Delete Account")}</button></div></div></div>`);
      } else {
        _push(`<!---->`);
      }
      if (showEditModal.value) {
        _push(`<div class="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50" stewardess="closeEditModal"><div class="bg-white rounded-2xl shadow-2xl w-full max-w-2xl mx-auto max-h-[90vh] overflow-y-auto"><div class="relative bg-gradient-to-r from-[#7B27E7] to-purple-600 rounded-t-2xl px-8 py-6"><div class="flex items-center justify-between"><div><h3 class="text-2xl font-bold text-white">Edit Profile</h3><p class="text-purple-100 mt-1">Update your personal information</p></div><button class="text-white hover:bg-white/20 rounded-full p-2 transition-all duration-200"><svg width="24" height="24" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg></button></div></div><div class="px-8 py-6"><form class="space-y-6"><div class="flex flex-col items-center mb-8"><div class="relative group"><label class="cursor-pointer block"><input type="file" accept="image/*" class="hidden"><div class="w-32 h-32 rounded-full bg-gradient-to-br from-purple-100 to-purple-200 flex items-center justify-center overflow-hidden border-4 border-white shadow-lg group-hover:shadow-xl transition-all duration-300">`);
        if (editProfile.value.profileImageUrl) {
          _push(`<img${ssrRenderAttr("src", editProfile.value.profileImageUrl)} class="w-full h-full object-cover" alt="Profile Preview">`);
        } else {
          _push(`<svg class="w-16 h-16 text-purple-600" fill="currentColor" viewBox="0 0 24 24"><path d="M12 12c2.7 0 5-2.3 5-5s-2.3-5-5-5-5 2.3-5 5 2.3 5 5 5Zm0 2c-3.3 0-10 1.7-10 5v3h20v-3c0-3.3-6.7-5-10-5Z"></path></svg>`);
        }
        _push(`</div><div class="absolute inset-0 rounded-full bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center"><div class="text-white text-center"><svg class="w-8 h-8 mx-auto mb-2" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" d="M12 4v16m8-8H4"></path></svg><span class="text-sm font-medium">Change Photo</span></div></div></label></div><p class="text-gray-500 text-sm mt-3">Click to upload a new profile picture</p></div><div class="grid grid-cols-1 md:grid-cols-2 gap-6"><div class="space-y-2"><label class="flex items-center gap-2 text-gray-700 font-semibold"><span class="w-5 h-5 rounded-full bg-[#7B27E7] flex items-center justify-center"><svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 24 24"><path d="M12 12c2.7 0 5-2.3 5-5s-2.3-5-5-5-5 2.3-5 5 2.3 5 5 5Z"></path></svg></span> First Name </label><input${ssrRenderAttr("value", editProfile.value.firstname)} class="w-full bg-[#F5F9FE] border border-gray-200 rounded-xl px-4 py-3 focus:outline-none focus:ring-2 focus:ring-[#7B27E7] focus:border-transparent transition-all duration-200" placeholder="Enter your first name" required></div><div class="space-y-2"><label class="flex items-center gap-2 text-gray-700 font-semibold"><span class="w-5 h-5 rounded-full bg-[#7B27E7] flex items-center justify-center"><svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 24 24"><path d="M12 12c2.7 0 5-2.3 5-5s-2.3-5-5-5-5 2.3-5 5 2.3 5 5 5Z"></path></svg></span> Last Name </label><input${ssrRenderAttr("value", editProfile.value.lastname)} class="w-full bg-[#F5F9FE] border border-gray-200 rounded-xl px-4 py-3 focus:outline-none focus:ring-2 focus:ring-[#7B27E7] focus:border-transparent transition-all duration-200" placeholder="Enter your last name"></div><div class="space-y-2"><label class="flex items-center gap-2 text-gray-700 font-semibold"><span class="w-5 h-5 rounded-full bg-[#7B27E7] flex items-center justify-center"><svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path></svg></span> Email Address </label><input${ssrRenderAttr("value", editProfile.value.email)} type="email" class="w-full bg-[#F5F9FE] border border-gray-200 rounded-xl px-4 py-3 focus:outline-none focus:ring-2 focus:ring-[#7B27E7] focus:border-transparent transition-all duration-200" placeholder="Enter your email address" required></div><div class="space-y-2"><label class="flex items-center gap-2 text-gray-700 font-semibold"><span class="w-5 h-5 rounded-full bg-[#7B27E7] flex items-center justify-center"><svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" d="M6.62 10.79a15.05 15.05 0 006.59 6.59l2.2-2.2a1 1 0 011.02-.24c1.12.37 2.33.57 3.57.57a1 1 0 011 1V20a1 1 0 01-1 1c-9.39 0-17-7.61-17-17a1 1 0 011-1h3.5a1 1 0 011 1c0 1.25.2 2.45.57 3.57a1 1 0 01-.25 1.02l-2.2 2.2z"></path></svg></span> Phone Number </label><input${ssrRenderAttr("value", editProfile.value.phone)} class="w-full bg-[#F5F9FE] border border-gray-200 rounded-xl px-4 py-3 focus:outline-none focus:ring-2 focus:ring-[#7B27E7] focus:border-transparent transition-all duration-200" placeholder="Enter your phone number"></div><div class="space-y-2"><label class="flex items-center gap-2 text-gray-700 font-semibold"><span class="w-5 h-5 rounded-full bg-[#7B27E7] flex items-center justify-center"><svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path></svg></span> Date of Birth </label><input${ssrRenderAttr("value", editProfile.value.dob)} type="date"${ssrRenderAttr("max", (/* @__PURE__ */ new Date()).toISOString().slice(0, 10))} class="w-full bg-[#F5F9FE] border border-gray-200 rounded-xl px-4 py-3 focus:outline-none focus:ring-2 focus:ring-[#7B27E7] focus:border-transparent transition-all duration-200"></div><div class="space-y-2"><label class="flex items-center gap-2 text-gray-700 font-semibold"><span class="w-5 h-5 rounded-full bg-[#7B27E7] flex items-center justify-center"><svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path></svg></span> Gender </label><select class="w-full bg-[#F5F9FE] border border-gray-200 rounded-xl px-4 py-3 focus:outline-none focus:ring-2 focus:ring-[#7B27E7] focus:border-transparent transition-all duration-200"><option value=""${ssrIncludeBooleanAttr(Array.isArray(editProfile.value.gender) ? ssrLooseContain(editProfile.value.gender, "") : ssrLooseEqual(editProfile.value.gender, "")) ? " selected" : ""}>Select Gender</option><option value="Male"${ssrIncludeBooleanAttr(Array.isArray(editProfile.value.gender) ? ssrLooseContain(editProfile.value.gender, "Male") : ssrLooseEqual(editProfile.value.gender, "Male")) ? " selected" : ""}>Male</option><option value="Female"${ssrIncludeBooleanAttr(Array.isArray(editProfile.value.gender) ? ssrLooseContain(editProfile.value.gender, "Female") : ssrLooseEqual(editProfile.value.gender, "Female")) ? " selected" : ""}>Female</option><option value="Other"${ssrIncludeBooleanAttr(Array.isArray(editProfile.value.gender) ? ssrLooseContain(editProfile.value.gender, "Other") : ssrLooseEqual(editProfile.value.gender, "Other")) ? " selected" : ""}>Other</option></select></div></div><div class="flex gap-4 pt-6 border-t border-gray-100"><button type="button" class="flex-1 px-6 py-3 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-xl font-semibold transition-all duration-200 hover:shadow-md"> Cancel </button><button type="submit"${ssrIncludeBooleanAttr(isSaving.value) ? " disabled" : ""} class="flex-1 px-6 py-3 bg-gradient-to-r from-[#7B27E7] to-purple-600 text-white rounded-xl font-semibold hover:from-purple-700 hover:to-purple-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-all duration-200 hover:shadow-lg flex items-center justify-center gap-2">`);
        if (isSaving.value) {
          _push(`<svg class="animate-spin h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>`);
        } else {
          _push(`<!---->`);
        }
        _push(`<span>${ssrInterpolate(isSaving.value ? "Saving Changes..." : "Save Changes")}</span></button></div></form></div></div></div>`);
      } else {
        _push(`<!---->`);
      }
      if (showCropperModal.value) {
        _push(`<div class="fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center p-4 z-[60]"><div class="bg-white rounded-2xl shadow-2xl w-full max-w-2xl mx-auto"><div class="bg-gradient-to-r from-[#7B27E7] to-purple-600 rounded-t-2xl px-6 py-4"><div class="flex items-center justify-between"><h3 class="text-xl font-bold text-white">Crop Profile Image</h3><button class="text-white hover:bg-white/20 rounded-full p-2 transition-all duration-200"><svg width="20" height="20" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg></button></div></div><div class="p-6"><div class="mb-6">`);
        _push(ssrRenderComponent(unref(Cropper), {
          ref_key: "cropperRef",
          ref: cropperRef,
          src: editProfile.value.profileImageUrl,
          "stencil-props": { aspectRatio: 1 },
          canvas: { width: 300, height: 300 },
          class: "w-full h-80 rounded-lg"
        }, null, _parent));
        _push(`</div><div class="flex items-center justify-center gap-4 mb-6"><button class="p-2 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"><svg class="w-5 h-5" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" d="M12 8V4l-4 4 4 4V8z"></path></svg></button><button class="p-2 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"><svg class="w-5 h-5" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" d="M12 8V4l4 4-4 4V8z"></path></svg></button><button class="p-2 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"><svg class="w-5 h-5" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path></svg></button></div><div class="flex gap-4"><button class="flex-1 px-6 py-3 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-xl font-semibold transition-all duration-200"> Cancel </button><button class="flex-1 px-6 py-3 bg-gradient-to-r from-[#7B27E7] to-purple-600 text-white rounded-xl font-semibold hover:from-purple-700 hover:to-purple-700 transition-all duration-200"> Apply Crop </button></div></div></div></div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div>`);
    };
  }
});
const _sfc_setup$7 = _sfc_main$7.setup;
_sfc_main$7.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("components/profile/Profile.vue");
  return _sfc_setup$7 ? _sfc_setup$7(props, ctx) : void 0;
};
const membershipDiscountPercent = 30;
const otherCharges = 20;
const _sfc_main$6 = /* @__PURE__ */ defineComponent({
  __name: "ProfileCart",
  __ssrInlineRender: true,
  setup(__props) {
    useAppSeoMeta({
      title: "Cart | Bookslotz"
    });
    useRouter$1();
    const { public: { BASE_URL } } = useRuntimeConfig();
    const isLoading = useState("globalLoader", () => false);
    const hasError = ref(false);
    const snackbarRef = ref(null);
    const salonData = ref({
      name: "Loading...",
      location: "MG Road, Mumbai",
      image: "/images/saloon-small-image.png"
    });
    const cartServices = ref([]);
    const selectedDate = ref("");
    const selectedTime = ref("");
    ref(0);
    const isCartEmpty = ref(true);
    ref(null);
    const membershipApplied = ref(false);
    const membershipData = ref(null);
    const giftCardCode = ref("");
    const appliedGiftCard = ref(null);
    const showSuccessModal = ref(false);
    const orderAmount = computed(() => {
      return cartServices.value.reduce((sum, item) => sum + item.price, 0);
    });
    const membershipDiscount = computed(() => {
      return Math.round(orderAmount.value * membershipDiscountPercent / 100);
    });
    const totalDurationMinutes = computed(() => {
      return cartServices.value.reduce((total, service) => {
        const duration = parseInt(service.duration) || 0;
        return total + duration;
      }, 0);
    });
    const formattedDuration = computed(() => {
      const totalMinutes = totalDurationMinutes.value;
      if (totalMinutes === 0) return "0 mins";
      const hours = Math.floor(totalMinutes / 60);
      const minutes = totalMinutes % 60;
      if (hours === 0) {
        return `${minutes} mins`;
      } else if (minutes === 0) {
        return hours === 1 ? "1 hour" : `${hours} hours`;
      } else {
        const hourText = hours === 1 ? "1 hour" : `${hours} hours`;
        return `${hourText} ${minutes} mins`;
      }
    });
    const giftCardDiscount = computed(() => {
      var _a;
      return ((_a = appliedGiftCard.value) == null ? void 0 : _a.discount) || 0;
    });
    const discounts = computed(() => {
      var _a;
      const discountList = [{ name: "Membership Discount", amount: membershipDiscount.value }];
      if ((_a = appliedGiftCard.value) == null ? void 0 : _a.discount) {
        discountList.push({ name: "Gift Card", amount: giftCardDiscount.value });
      }
      return discountList;
    });
    const finalTotal = computed(() => {
      return Math.max(0, orderAmount.value + otherCharges - membershipDiscount.value - giftCardDiscount.value);
    });
    const canContinue = computed(() => {
      return cartServices.value.length > 0;
    });
    return (_ctx, _push, _parent, _attrs) => {
      var _a;
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "min-h-screen bg-white relative" }, _attrs))} data-v-618051d1>`);
      if (unref(isLoading)) {
        _push(ssrRenderComponent(Loader, null, null, _parent));
      } else {
        _push(`<!---->`);
      }
      _push(ssrRenderComponent(Snackbar, {
        ref_key: "snackbarRef",
        ref: snackbarRef
      }, null, _parent));
      if (hasError.value) {
        _push(ssrRenderComponent(_sfc_main$1$1, { error: { statusCode: 500, message: "Something went wrong. Please try again later." } }, null, _parent));
      } else {
        _push(`<!---->`);
      }
      if (!hasError.value) {
        _push(`<div class="flex flex-col xl:flex-row max-w-7xl mx-auto mb-10" data-v-618051d1><div class="flex-1 lg:flex-[0.5] bg-white" data-v-618051d1><div class="mb-8" data-v-618051d1><h1 class="text-[25px] font-semibold text-gray-900" data-v-618051d1>Cart</h1></div><div class="mb-8" data-v-618051d1><h2 class="text-lg font-semibold text-gray-900 mb-4" data-v-618051d1>Enter Gift Card</h2><div class="flex flex-col sm:flex-row gap-3 items-stretch sm:items-center" data-v-618051d1><input${ssrRenderAttr("value", giftCardCode.value)} type="text" placeholder="Enter gift card code" class="flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent" data-v-618051d1><button class="px-8 py-3 bg-purple-600 text-white font-semibold rounded-lg hover:bg-purple-700 transition-colors disabled:bg-gray-300 disabled:cursor-not-allowed w-full sm:w-auto self-center sm:self-auto cursor-pointer"${ssrIncludeBooleanAttr(!giftCardCode.value) ? " disabled" : ""} data-v-618051d1> Apply </button></div>`);
        if ((_a = appliedGiftCard.value) == null ? void 0 : _a.discount) {
          _push(`<p class="text-sm text-green-600 mt-2" data-v-618051d1> Gift card applied: -\u20B9${ssrInterpolate(appliedGiftCard.value.discount)}</p>`);
        } else {
          _push(`<!---->`);
        }
        _push(`</div><div class="bg-[#F5F9FE] rounded-xl p-6 mb-8" data-v-618051d1><h2 class="flex justify-between items-center font-semibold text-lg mb-4" data-v-618051d1><span class="text-lg font-semibold text-black" data-v-618051d1>Membership Discount</span></h2>`);
        if (membershipData.value) {
          _push(`<div class="space-y-2" data-v-618051d1><p class="text-[16px] font-medium" data-v-618051d1> You&#39;ve a membership for ${ssrInterpolate(membershipData.value.salonName)} \u2013 <span class="text-orange-600 font-semibold text-[16px]" data-v-618051d1>${ssrInterpolate(membershipData.value.planName)}</span></p><p class="text-[16px]" data-v-618051d1> Enjoy an exclusive ${ssrInterpolate(membershipData.value.discountPercent)}% discount on all services. </p><ul class="list-disc list-inside mt-2" data-v-618051d1><li class="text-[16px] font-semibold text-black" data-v-618051d1> Membership Discount (${ssrInterpolate(membershipData.value.discountPercent)}%): <span class="inter-semibold font-[500]" data-v-618051d1>-\u20B9${ssrInterpolate(membershipData.value.totalDiscount.toFixed(2))}</span>`);
          if (!membershipApplied.value) {
            _push(`<button class="ml-2 px-3 py-1 text-xs bg-purple-100 text-purple-700 rounded hover:bg-purple-200 font-semibold transition-colors cursor-pointer" data-v-618051d1> Apply Now! </button>`);
          } else {
            _push(`<span class="ml-2 text-green-600 text-xs font-semibold" data-v-618051d1>Applied</span>`);
          }
          _push(`</li>`);
          if (membershipData.value.services && membershipData.value.services.length > 0) {
            _push(`<li class="mt-2" data-v-618051d1><span class="font-semibold" data-v-618051d1>Discount Breakdown</span><div class="ml-4 space-y-1 mt-2" data-v-618051d1><!--[-->`);
            ssrRenderList(membershipData.value.services, (service) => {
              _push(`<div class="flex justify-between text-xs text-gray-600 pl-2" data-v-618051d1><span class="font-[500]" data-v-618051d1>${ssrInterpolate(service.serviceName)}</span><span class="inter-semibold font-[500]" data-v-618051d1>-\u20B9${ssrInterpolate(service.memberShipDiscountAmount.toFixed(2))}</span></div>`);
            });
            _push(`<!--]--></div></li>`);
          } else {
            _push(`<!---->`);
          }
          _push(`</ul></div>`);
        } else {
          _push(`<p class="text-sm text-gray-600" data-v-618051d1>No membership available.</p>`);
        }
        _push(`</div></div><div class="w-full lg:flex-[0.5] xl:w-[400px] bg-white xl:pl-10 xl:mt-10" data-v-618051d1><div class="bg-white rounded-2xl shadow-lg border border-gray-100 p-6 space-y-6" data-v-618051d1>`);
        if (isCartEmpty.value) {
          _push(`<div class="text-center text-gray-600 text-base font-medium" data-v-618051d1> Cart is empty </div>`);
        } else {
          _push(`<!--[--><div data-v-618051d1><p class="text-[16px] font-[500] dm-sanssemibold uppercase tracking-wide mb-3" data-v-618051d1>SELECTED SALON</p><div class="flex items-center gap-3 p-3 rounded-lg bg-white border border-gray-100" data-v-618051d1><div class="w-12 h-12 rounded-lg bg-gray-200 flex items-center justify-center overflow-hidden flex-shrink-0" data-v-618051d1><img${ssrRenderAttr("src", salonData.value.image)}${ssrRenderAttr("alt", salonData.value.name)} class="w-full h-full object-cover" data-v-618051d1></div><div class="flex-1 min-w-0" data-v-618051d1><h3 class="text-lg font-semibold text-gray-900 truncate" data-v-618051d1>${ssrInterpolate(salonData.value.name)}</h3><div class="flex items-center gap-1 text-sm text-gray-500" data-v-618051d1><svg class="w-3 h-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-618051d1><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" data-v-618051d1></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" data-v-618051d1></path></svg><span class="truncate" data-v-618051d1>${ssrInterpolate(salonData.value.location)}</span></div></div></div></div>`);
          if (selectedDate.value && selectedTime.value) {
            _push(`<div class="space-y-3 sm:space-y-0 sm:flex sm:items-center sm:gap-4" data-v-618051d1><div class="flex items-center gap-2" data-v-618051d1><svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 text-gray-900" viewBox="0 0 24 24" data-v-618051d1><path fill="currentColor" d="M5 22q-.825 0-1.412-.587T3 20V6q0-.825.588-1.412T5 4h1V2h2v2h8V2h2v2h1q.825 0 1.413.588T21 6v14q0 .825-.587 1.413T19 22zm0-2h14V10H5zM5 8h14V6H5zm0 0V6z" data-v-618051d1></path></svg><span class="text-sm font-medium text-gray-700" data-v-618051d1>${ssrInterpolate(selectedDate.value)}</span></div><div class="flex items-center gap-2" data-v-618051d1><svg xmlns="http://www.w3.org/2000/svg" width="17" height="17" viewBox="0 0 24 24" data-v-618051d1><path fill="currentColor" d="M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2M12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8s8 3.58 8 8s-3.58 8-8 8" data-v-618051d1></path><path fill="currentColor" d="M12.5 7H11v6l5.25 3.15l.75-1.23l-4.5-2.67z" data-v-618051d1></path></svg><span class="text-sm font-medium text-gray-700" data-v-618051d1>${ssrInterpolate(selectedTime.value)} (${ssrInterpolate(formattedDuration.value)})</span></div></div>`);
          } else {
            _push(`<!---->`);
          }
          _push(`<div class="space-y-4" data-v-618051d1>`);
          if (cartServices.value.length > 0) {
            _push(`<div class="space-y-4" data-v-618051d1><!--[-->`);
            ssrRenderList(cartServices.value, (service) => {
              _push(`<div class="flex justify-between items-start gap-3" data-v-618051d1><div class="flex-1 min-w-0" data-v-618051d1><h4 class="text-[16px] font-[500] mb-1 truncate dm-sanssemibold" data-v-618051d1>${ssrInterpolate(service.name)}</h4><div class="flex items-center gap-2 text-sm text-gray-500" data-v-618051d1></div></div><div class="flex items-center gap-3 flex-shrink-0" data-v-618051d1><p class="text-[15px] inter-semibold font-[500]" data-v-618051d1>`);
              if (service.totalDiscount && service.totalDiscount > 0) {
                _push(`<span data-v-618051d1><span class="text-gray-400 line-through inter-semibold mr-1" data-v-618051d1>\u20B9${ssrInterpolate(service.serviceAmount)}</span><span class="text-green-600 inter-semibold" data-v-618051d1>\u20B9${ssrInterpolate(service.serviceAmountAfterDiscount)}</span></span>`);
              } else {
                _push(`<span class="inter-semibold" data-v-618051d1> \u20B9${ssrInterpolate(service.serviceAmount)}</span>`);
              }
              _push(`</p><button class="w-4 h-4 rounded-full bg-gray-700 flex items-center justify-center hover:bg-gray-800 transition-colors cursor-pointer" data-v-618051d1><svg class="w-3.5 h-3.5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2" data-v-618051d1><path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" data-v-618051d1></path></svg></button></div></div>`);
            });
            _push(`<!--]--></div>`);
          } else {
            _push(`<p class="text-sm text-gray-600" data-v-618051d1>No services or add-ons selected.</p>`);
          }
          _push(`</div><div class="border-t border-gray-200 pt-6" data-v-618051d1><h4 class="text-base font-semibold text-gray-900 mb-4" data-v-618051d1>Order Summary</h4><div class="space-y-3 bg-[rgba(245,249,254,1)] py-3 sm:py-3 px-3 sm:px-6" data-v-618051d1><div class="flex justify-between items-center text-[15px]" data-v-618051d1><span class="text-gray-600" data-v-618051d1>Order Amount</span><span class="font-[500] inter-semibold text-gray-900" data-v-618051d1>\u20B9${ssrInterpolate(orderAmount.value.toFixed(2))}</span></div>`);
          {
            _push(`<div class="flex justify-between items-center text-[15px]" data-v-618051d1><span class="text-gray-600" data-v-618051d1>Other Charges</span><span class="font-[500] inter-semibold text-gray-900" data-v-618051d1>\u20B9${ssrInterpolate(otherCharges)}</span></div>`);
          }
          _push(`<!--[-->`);
          ssrRenderList(discounts.value, (discount) => {
            _push(`<div class="flex justify-between items-center text-[15px]" data-v-618051d1><span class="text-gray-600" data-v-618051d1>${ssrInterpolate(discount.name)}</span><span class="font-[500] text-green-600 inter-semibold" data-v-618051d1>-\u20B9${ssrInterpolate(discount.amount)}</span></div>`);
          });
          _push(`<!--]--><div class="flex justify-between items-center" data-v-618051d1><span class="text-[18px] dm-sanssemibold font-[500] text-gray-900" data-v-618051d1>Total</span><span class="text-[18px] inter-semibold font-[500] text-gray-900" data-v-618051d1>\u20B9${ssrInterpolate(finalTotal.value.toFixed(2))}</span></div></div></div><button class="${ssrRenderClass([canContinue.value ? "bg-purple-600 hover:bg-purple-700" : "bg-gray-300 cursor-not-allowed", "w-full py-4 rounded-full text-white font-medium text-base transition-colors cursor-pointer"])}"${ssrIncludeBooleanAttr(!canContinue.value) ? " disabled" : ""} data-v-618051d1> Continue </button><!--]-->`);
        }
        _push(`</div></div></div>`);
      } else {
        _push(`<!---->`);
      }
      if (showSuccessModal.value) {
        _push(`<div class="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-3 sm:p-4" data-v-618051d1><div class="bg-white rounded-2xl sm:rounded-3xl w-full max-w-sm sm:max-w-lg mx-auto text-center relative overflow-hidden" style="${ssrRenderStyle({ "max-height": "90vh" })}" data-v-618051d1><div class="p-6 sm:p-8" data-v-618051d1><div class="relative mb-6 sm:mb-8 flex justify-center" data-v-618051d1><div class="absolute -top-3 sm:-top-4 -left-6 sm:-left-8 w-2 sm:w-3 h-2 sm:h-3 bg-red-400 rounded-full animate-float-1" data-v-618051d1></div><div class="absolute -top-4 sm:-top-6 left-8 sm:left-12 w-1.5 sm:w-2 h-1.5 sm:h-2 bg-orange-400 rounded-full animate-float-2" data-v-618051d1></div><div class="absolute top-1 sm:top-2 -right-6 sm:-right-8 w-3 sm:w-4 h-3 sm:h-4 bg-pink-400 rounded-full animate-float-3" data-v-618051d1></div><div class="absolute top-8 sm:top-12 -left-4 sm:-left-6 w-1.5 sm:w-2 h-1.5 sm:h-2 bg-blue-400 rounded-full animate-float-4" data-v-618051d1></div><div class="absolute -bottom-1 sm:-bottom-2 -right-3 sm:-right-4 w-2 sm:w-3 h-2 sm:h-3 bg-green-400 rounded-full animate-float-5" data-v-618051d1></div><div class="absolute -bottom-3 sm:-bottom-4 left-6 sm:left-8 w-1.5 sm:w-2 h-1.5 sm:h-2 bg-purple-400 rounded-full animate-float-6" data-v-618051d1></div><div class="relative" data-v-618051d1><div class="absolute inset-0 w-16 sm:w-20 h-16 sm:h-20 border-2 border-purple-200 rounded-full animate-pulse-ring opacity-30" data-v-618051d1></div><div class="absolute inset-0.5 sm:inset-1 w-15 sm:w-18 h-15 sm:h-18 border-2 border-purple-300 rounded-full animate-pulse-ring-delayed opacity-20" data-v-618051d1></div><div class="relative w-16 sm:w-20 h-16 sm:h-20 bg-gradient-to-br from-purple-500 to-purple-700 rounded-full flex items-center justify-center shadow-lg animate-scale-in" data-v-618051d1><svg class="8 sm:w-10 h-8 sm:h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="3" data-v-618051d1><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7" data-v-618051d1></path></svg></div></div></div><h2 class="text-xl sm:text-2xl font-bold text-gray-900 mb-2 sm:mb-3 animate-fade-in-up leading-tight" data-v-618051d1> Your Appointment Confirmed Successfully </h2><p class="text-gray-600 text-sm sm:text-base mb-6 sm:mb-8 leading-relaxed animate-fade-in-up-delayed px-2 sm:px-0" data-v-618051d1> Thank you for booking with ${ssrInterpolate(salonData.value.name)}. Your appointment is confirmed, and we&#39;ll see you soon! </p><button class="w-full py-3 sm:py-4 bg-gradient-to-r from-purple-600 to-purple-700 text-white font-semibold text-sm sm:text-base rounded-full hover:from-purple-700 hover:to-purple-800 transition-all duration-200 transform hover:scale-105 shadow-lg animate-fade-in-up-delayed cursor-pointer" data-v-618051d1> Continue </button></div></div></div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div>`);
    };
  }
});
const _sfc_setup$6 = _sfc_main$6.setup;
_sfc_main$6.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("components/profile/ProfileCart.vue");
  return _sfc_setup$6 ? _sfc_setup$6(props, ctx) : void 0;
};
const ProfileCart = /* @__PURE__ */ _export_sfc(_sfc_main$6, [["__scopeId", "data-v-618051d1"]]);
const _sfc_main$5 = /* @__PURE__ */ defineComponent({
  __name: "ProfileWishlist",
  __ssrInlineRender: true,
  setup(__props) {
    useRouter$1();
    useUserStore();
    const { public: { BASE_URL } } = useRuntimeConfig();
    const isLoading = useState("globalLoader", () => false);
    const hasError = ref(false);
    const snackbarRef = ref(null);
    ref(false);
    const wishlistItems = ref([]);
    useAppSeoMeta({
      title: "Wishlist | Bookslotz",
      description: "Save your favorite salon services and view them anytime in your personalized wishlist on Bookslotz."
    });
    return (_ctx, _push, _parent, _attrs) => {
      _push(`<div${ssrRenderAttrs(_attrs)} data-v-cda2934e>`);
      if (unref(isLoading)) {
        _push(ssrRenderComponent(Loader, null, null, _parent));
      } else {
        _push(`<!---->`);
      }
      _push(ssrRenderComponent(Snackbar, {
        ref_key: "snackbarRef",
        ref: snackbarRef
      }, null, _parent));
      if (hasError.value) {
        _push(ssrRenderComponent(_sfc_main$1$1, { error: { statusCode: 500, message: "Something went wrong. Please try again later." } }, null, _parent));
      } else {
        _push(`<!---->`);
      }
      if (!hasError.value) {
        _push(`<div data-v-cda2934e><h2 class="text-[25px] font-bold text-gray-900 mb-6" data-v-cda2934e>Wishlist</h2>`);
        if (wishlistItems.value.length) {
          _push(`<div data-v-cda2934e><div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 gap-4" data-v-cda2934e><!--[-->`);
          ssrRenderList(wishlistItems.value, (item, i) => {
            _push(`<div class="bg-white rounded-2xl overflow-hidden shadow-sm hover:shadow-lg transition-all duration-300 cursor-pointer group" data-v-cda2934e><div class="relative aspect-[4/3] overflow-hidden" data-v-cda2934e><img${ssrRenderAttr("src", item.image)}${ssrRenderAttr("alt", item.name)} class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300" data-v-cda2934e><button class="absolute bottom-3 right-3 w-8 h-8 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center shadow-sm hover:bg-white transition-all duration-200 disabled:opacity-50"${ssrIncludeBooleanAttr(item.isWishlistLoading) ? " disabled" : ""} data-v-cda2934e>`);
            if (item.isWishlistLoading) {
              _push(`<div class="w-4 h-4 border-2 border-gray-300 border-t-red-500 rounded-full animate-spin" data-v-cda2934e></div>`);
            } else {
              _push(`<svg class="w-4 h-4 text-red-500" fill="currentColor" viewBox="0 0 20 20" data-v-cda2934e><path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd" data-v-cda2934e></path></svg>`);
            }
            _push(`</button></div><div class="p-4 space-y-2" data-v-cda2934e><h3 class="font-semibold text-gray-900 text-base leading-tight line-clamp-1" data-v-cda2934e>${ssrInterpolate(item.name)}</h3><div class="flex items-center text-gray-500 text-sm" data-v-cda2934e><svg class="w-4 h-4 mr-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-cda2934e><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" data-v-cda2934e></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" data-v-cda2934e></path></svg><span class="line-clamp-1" data-v-cda2934e>${ssrInterpolate(item.location)}</span></div><div class="flex items-center space-x-1" data-v-cda2934e><svg class="w-4 h-4 text-yellow-400 fill-current" viewBox="0 0 20 20" data-v-cda2934e><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" data-v-cda2934e></path></svg><span class="font-semibold text-gray-900 text-sm" data-v-cda2934e>${ssrInterpolate(item.rating)}</span><span class="text-gray-500 text-sm" data-v-cda2934e>(${ssrInterpolate(item.reviews)})</span></div></div></div>`);
          });
          _push(`<!--]--></div></div>`);
        } else if (!unref(isLoading)) {
          _push(`<div class="text-center py-12" data-v-cda2934e><div class="text-gray-400 mb-4" data-v-cda2934e><svg class="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-cda2934e><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" data-v-cda2934e></path></svg></div><p class="text-gray-500 text-lg" data-v-cda2934e>No items in your wishlist.</p><p class="text-gray-400 text-sm mt-2" data-v-cda2934e>Start exploring salons and add your favorites here!</p></div>`);
        } else {
          _push(`<!---->`);
        }
        _push(`</div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div>`);
    };
  }
});
const _sfc_setup$5 = _sfc_main$5.setup;
_sfc_main$5.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("components/profile/ProfileWishlist.vue");
  return _sfc_setup$5 ? _sfc_setup$5(props, ctx) : void 0;
};
const ProfileWishlist = /* @__PURE__ */ _export_sfc(_sfc_main$5, [["__scopeId", "data-v-cda2934e"]]);
const _sfc_main$4 = /* @__PURE__ */ defineComponent({
  __name: "ProfileGiftCards",
  __ssrInlineRender: true,
  setup(__props) {
    useUserStore();
    const { public: { BASE_URL } } = useRuntimeConfig();
    const isLoading = useState("globalLoader", () => false);
    const hasError = ref(false);
    const snackbarRef = ref(null);
    ref(false);
    const giftCards = ref([]);
    const transactions = ref([]);
    const totalBalance = ref(0);
    const isModalOpen = ref(false);
    const selectedCard = ref(null);
    useAppSeoMeta({
      title: "Gift Cards | Bookslotz",
      description: "Explore and purchase Bookslotz gift cards \u2013 a perfect present for your loved ones to enjoy salon services."
    });
    return (_ctx, _push, _parent, _attrs) => {
      var _a, _b, _c, _d, _e, _f, _g, _h, _i, _j, _k, _l;
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "min-h-screen bg-white flex justify-center items-start relative" }, _attrs))} data-v-8c74baac>`);
      if (unref(isLoading)) {
        _push(ssrRenderComponent(Loader, null, null, _parent));
      } else {
        _push(`<!---->`);
      }
      _push(ssrRenderComponent(Snackbar, {
        ref_key: "snackbarRef",
        ref: snackbarRef
      }, null, _parent));
      if (hasError.value) {
        _push(ssrRenderComponent(_sfc_main$1$1, { error: { statusCode: 500, message: "Something went wrong. Please try again later." } }, null, _parent));
      } else {
        _push(`<!---->`);
      }
      if (!hasError.value) {
        _push(`<div class="w-full max-w-7xl xl:pr-45" data-v-8c74baac><h1 class="text-[25px] font-semibold mb-6" data-v-8c74baac>Gift Card</h1><div class="border border-gray-200 rounded-xl p-4 mb-6" data-v-8c74baac><div class="rounded-xl p-4 py-6 text-white flex flex-col mb-4" style="${ssrRenderStyle({ "background": "linear-gradient(99.75deg, #7B27E7 7.59%, #D6B6FF 126.52%)" })}" data-v-8c74baac><p class="text-[18px] dm-dans mb-1 text-center sm:text-left" data-v-8c74baac>Total Balance</p><div class="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4" data-v-8c74baac><p class="text-[35px] inter-semibold text-center sm:text-left" data-v-8c74baac>\u20B9${ssrInterpolate(totalBalance.value)}</p></div></div>`);
        if (giftCards.value.length > 0) {
          _push(`<div class="grid grid-cols-1 sm:grid-cols-2 gap-4" data-v-8c74baac><!--[-->`);
          ssrRenderList(giftCards.value, (card, index2) => {
            _push(`<div class="p-4 border border-gray-300 rounded-lg" data-v-8c74baac><div class="flex items-center gap-4" data-v-8c74baac><img${ssrRenderAttr("src", card.image || "/images/gift-card-green.png")}${ssrRenderAttr("alt", card.name)} class="w-20 h-20 rounded-md object-cover" data-v-8c74baac><div data-v-8c74baac><p class="font-medium text-[18px]" data-v-8c74baac>${ssrInterpolate(card.name)}</p><p class="inter-semibold text-[18px]" data-v-8c74baac>\u20B9${ssrInterpolate(card.amount)}</p></div></div><div class="flex justify-end mt-4" data-v-8c74baac><button class="hover:bg-gray-100 rounded-full p-1 transition-colors cursor-pointer" data-v-8c74baac><svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 text-black" fill="none" viewBox="0 0 24 24" stroke="currentColor" data-v-8c74baac><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" data-v-8c74baac></path></svg></button></div></div>`);
          });
          _push(`<!--]--></div>`);
        } else if (!unref(isLoading)) {
          _push(`<div class="text-center py-8" data-v-8c74baac><div class="text-gray-400 mb-4" data-v-8c74baac><svg class="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-8c74baac><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7" data-v-8c74baac></path></svg></div><p class="text-gray-500 text-lg" data-v-8c74baac>No gift cards available.</p><p class="text-gray-400 text-sm mt-2" data-v-8c74baac>Purchase gift cards to get started!</p></div>`);
        } else {
          _push(`<!---->`);
        }
        _push(`</div><div class="border border-gray-200 rounded-xl overflow-hidden" data-v-8c74baac><div class="overflow-x-auto" data-v-8c74baac><table class="min-w-full" data-v-8c74baac><thead class="bg-gray-50" data-v-8c74baac><tr data-v-8c74baac><th class="px-6 py-4 text-left text-[16px] font-medium text-[#494949] tracking-wider" data-v-8c74baac>Date</th><th class="px-6 py-4 text-left text-[16px] font-medium text-[#494949] tracking-wider" data-v-8c74baac>Gift Card Code</th><th class="px-6 py-4 text-left text-[16px] font-medium text-[#494949] tracking-wider" data-v-8c74baac>Saloon</th><th class="px-6 py-4 text-left text-[16px] font-medium text-[#494949] tracking-wider" data-v-8c74baac>Amount</th><th class="px-6 py-4 text-left text-[16px] font-medium text-[#494949] tracking-wider" data-v-8c74baac>Balance Amount</th></tr></thead><tbody class="bg-white divide-y divide-gray-100" data-v-8c74baac>`);
        if (transactions.value.length === 0 && !unref(isLoading)) {
          _push(`<tr data-v-8c74baac><td colspan="5" class="px-6 py-8 text-center text-gray-500" data-v-8c74baac> No transactions found </td></tr>`);
        } else {
          _push(`<!---->`);
        }
        _push(`<!--[-->`);
        ssrRenderList(transactions.value, (transaction, index2) => {
          _push(`<tr class="hover:bg-gray-50/50 transition-colors" data-v-8c74baac><td class="px-6 py-4 text-[16px] font-semibold" style="${ssrRenderStyle({ "color": "#4D0BA3" })}" data-v-8c74baac>${ssrInterpolate(transaction.date)}</td><td class="px-6 py-4 text-[16px] text-gray-900" data-v-8c74baac>${ssrInterpolate(transaction.giftCard)}</td><td class="px-6 py-4 text-[16px] text-gray-900" data-v-8c74baac>${ssrInterpolate(transaction.saloon)}</td><td class="px-6 py-4 text-[16px] font-medium text-gray-900 inter-regular" data-v-8c74baac>\u20B9${ssrInterpolate(transaction.amount)}</td><td class="px-6 py-4 text-[16px] text-gray-900 inter-regular" data-v-8c74baac>\u20B9${ssrInterpolate(transaction.balanceAmount)}</td></tr>`);
        });
        _push(`<!--]--></tbody></table></div></div>`);
        if (isModalOpen.value) {
          _push(`<div class="fixed inset-0 bg-black/50 bg-opacity-50 flex items-center justify-center z-50 p-4" data-v-8c74baac><div class="bg-white rounded-2xl w-full max-w-md mx-auto shadow-2xl transform transition-all max-h-[98vh] flex flex-col" data-v-8c74baac><div class="p-6 pb-0 flex-shrink-0" data-v-8c74baac><div class="rounded-xl p-6 text-white mb-6 relative overflow-hidden bg-gray-100" style="${ssrRenderStyle({ "min-height": "160px" })}" data-v-8c74baac>`);
          if ((_a = selectedCard.value) == null ? void 0 : _a.image) {
            _push(`<img${ssrRenderAttr("src", selectedCard.value.image)}${ssrRenderAttr("alt", (_b = selectedCard.value) == null ? void 0 : _b.name)} class="absolute inset-0 w-full h-full object-cover" style="${ssrRenderStyle({ "z-index": "0" })}" data-v-8c74baac>`);
          } else {
            _push(`<!---->`);
          }
          _push(`<div class="absolute inset-0 opacity-20 z-10 pointer-events-none" data-v-8c74baac><svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg" data-v-8c74baac><defs data-v-8c74baac><pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse" data-v-8c74baac><path d="M 20 0 L 0 0 0 20" fill="none" stroke="currentColor" stroke-width="0.5" data-v-8c74baac></path></pattern></defs><rect width="100%" height="100%" fill="url(#grid)" data-v-8c74baac></rect></svg></div><div class="relative z-20" data-v-8c74baac><h3 class="text-lg font-medium mb-6 drop-shadow" data-v-8c74baac>${ssrInterpolate((_c = selectedCard.value) == null ? void 0 : _c.name)}</h3><div class="flex justify-between items-end sm:mt-15" data-v-8c74baac><div data-v-8c74baac><div class="text-3xl font-bold mb-1 drop-shadow" data-v-8c74baac>\u20B9${ssrInterpolate((_d = selectedCard.value) == null ? void 0 : _d.amount)}</div><div class="text-sm opacity-90 drop-shadow" data-v-8c74baac>All services &amp; products</div></div><div class="text-right text-sm" data-v-8c74baac><div class="opacity-90 text-left drop-shadow" data-v-8c74baac>Expires in</div><div class="drop-shadow" data-v-8c74baac>${ssrInterpolate((_e = selectedCard.value) == null ? void 0 : _e.expiryDate)}</div></div></div></div></div></div><div class="flex-1 overflow-y-auto px-6" data-v-8c74baac><div class="mb-6" data-v-8c74baac><h4 class="text-[18px] text-[700] font-semibold mb-4" data-v-8c74baac>Card Details</h4><div class="bg-white rounded-lg p-4 space-y-3 border border-gray-200" data-v-8c74baac><div class="grid grid-cols-1 sm:grid-cols-2 sm:gap-20" data-v-8c74baac><div data-v-8c74baac><div class="text-[16px] text-[400] mb-1" data-v-8c74baac>Card Value :</div><div class="font-semibold text-[16px] text-[700]" data-v-8c74baac>\u20B9${ssrInterpolate((_f = selectedCard.value) == null ? void 0 : _f.amount)}</div></div><div data-v-8c74baac><div class="text-[16px] text-[400] mb-1" data-v-8c74baac>Purchase Date :</div><div class="font-semibold text-[16px] text-[700]" data-v-8c74baac>${ssrInterpolate((_g = selectedCard.value) == null ? void 0 : _g.purchaseDate)}</div></div></div><div class="grid grid-cols-1 sm:grid-cols-2 sm:gap-20" data-v-8c74baac><div data-v-8c74baac><div class="text-[16px] text-[400] mb-1" data-v-8c74baac>Validity :</div><div class="font-semibold text-[16px] text-[700]" data-v-8c74baac>${ssrInterpolate((_h = selectedCard.value) == null ? void 0 : _h.validity)}</div></div><div data-v-8c74baac><div class="text-[16px] text-[400] mb-1" data-v-8c74baac>Status :</div><div class="font-semibold text-[16px] text-green-600" data-v-8c74baac>${ssrInterpolate((_i = selectedCard.value) == null ? void 0 : _i.status)}</div></div></div></div></div><div class="mb-6 border-t border-gray-300" data-v-8c74baac><h4 class="text-[18px] text-[700] font-semibold mb-4 mt-4" data-v-8c74baac>Transactions</h4><div class="space-y-3" data-v-8c74baac>`);
          if (((_k = (_j = selectedCard.value) == null ? void 0 : _j.cardTransactions) == null ? void 0 : _k.length) === 0) {
            _push(`<div class="text-center py-4 text-gray-500" data-v-8c74baac> No transactions for this card </div>`);
          } else {
            _push(`<!---->`);
          }
          _push(`<!--[-->`);
          ssrRenderList((_l = selectedCard.value) == null ? void 0 : _l.cardTransactions, (txn, index2) => {
            _push(`<div class="flex justify-between items-center" data-v-8c74baac><div data-v-8c74baac><div class="text-[16px] font-semibold text-black-700" data-v-8c74baac>${ssrInterpolate(txn.date)}</div><div class="text-[16px]" data-v-8c74baac>${ssrInterpolate(txn.service)}</div></div><div class="${ssrRenderClass([txn.amount.startsWith("+") ? "text-green-600" : "text-red-600", "font-medium text-[16px] text-[500]"])}" data-v-8c74baac>${ssrInterpolate(txn.amount)}</div></div>`);
          });
          _push(`<!--]--></div></div></div><div class="p-6 pt-4 flex-shrink-0 border-t border-gray-100" data-v-8c74baac><button class="w-full bg-purple-600 hover:bg-purple-700 text-white font-medium py-3 px-4 rounded-full transition-colors cursor-pointer" data-v-8c74baac> Close </button></div></div></div>`);
        } else {
          _push(`<!---->`);
        }
        _push(`</div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div>`);
    };
  }
});
const _sfc_setup$4 = _sfc_main$4.setup;
_sfc_main$4.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("components/profile/ProfileGiftCards.vue");
  return _sfc_setup$4 ? _sfc_setup$4(props, ctx) : void 0;
};
const ProfileGiftCards = /* @__PURE__ */ _export_sfc(_sfc_main$4, [["__scopeId", "data-v-8c74baac"]]);
const _sfc_main$3 = /* @__PURE__ */ defineComponent({
  __name: "ProfileOrders",
  __ssrInlineRender: true,
  setup(__props) {
    useAppSeoMeta({
      title: "Orders | Bookslotz",
      description: "Track your past and upcoming salon appointments easily on the Orders page of Bookslotz."
    });
    useRouter();
    const appointments = [
      {
        id: "1",
        salonName: "Elite Beauty Salon",
        location: "MG Road, Mumbai",
        date: "Thursday 21 February",
        time: "10:00-11:00 AM (1hr)",
        status: "upcoming",
        services: [
          { name: "Haircut for Men", duration: "1hr - 45 mins", price: 499 },
          { name: "Beard Trim", duration: "30 mins", price: 299 }
        ],
        total: 459,
        image: "/images/saloon-small-image.png"
      },
      {
        id: "2",
        salonName: "Elite Beauty Salon",
        location: "MG Road, Mumbai",
        date: "Monday 18 February",
        time: "2:00-3:00 PM (1hr)",
        status: "completed",
        services: [
          { name: "Haircut for Men", duration: "1hr - 45 mins", price: 499 },
          { name: "Hair Wash", duration: "15 mins", price: 199 }
        ],
        total: 698,
        image: "/images/saloon-small-image.png"
      },
      {
        id: "3",
        salonName: "Elite Beauty Salon",
        location: "MG Road, Mumbai",
        date: "Thursday 21 February",
        time: "10:00-11:00 AM (1hr)",
        status: "canceled",
        services: [
          { name: "Haircut for Men", duration: "1hr - 45 mins", price: 499 },
          { name: "Shave", duration: "30 mins", price: 299 }
        ],
        total: 459,
        image: "/images/saloon-small-image.png"
      }
    ];
    const upcomingAppointments = computed(
      () => appointments.filter((appointment) => appointment.status === "upcoming")
    );
    const completedAppointments = computed(
      () => appointments.filter((appointment) => appointment.status === "completed")
    );
    const canceledAppointments = computed(
      () => appointments.filter((appointment) => appointment.status === "canceled")
    );
    return (_ctx, _push, _parent, _attrs) => {
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "min-h-screen bg-white" }, _attrs))} data-v-b5729ff7><div class="w-full max-w-7xl xl:pr-45" data-v-b5729ff7><h1 class="text-[25px] sm:text-2xl font-bold text-gray-900 mb-4 sm:mb-6 pt-4 sm:pt-0" data-v-b5729ff7>Orders</h1><div class="mb-6 sm:mb-8" data-v-b5729ff7><h2 class="text-sm sm:text-[16px] font-semibold text-gray-700 mb-3 sm:mb-4 pt-2 pb-2 border-b border-gray-300" data-v-b5729ff7> UPCOMING APPOINTMENTS (${ssrInterpolate(unref(upcomingAppointments).length)}) </h2><!--[-->`);
      ssrRenderList(unref(upcomingAppointments), (appointment) => {
        _push(`<div class="bg-white border sm:border-0 rounded-lg sm:rounded-none p-3 sm:p-0 mb-4" data-v-b5729ff7><div class="flex flex-col sm:flex-row sm:items-start gap-3 sm:gap-4" data-v-b5729ff7><div class="w-16 h-16 sm:w-20 sm:h-20 rounded-lg overflow-hidden flex-shrink-0 mx-auto sm:mx-0" data-v-b5729ff7><img${ssrRenderAttr("src", appointment.image)}${ssrRenderAttr("alt", appointment.salonName)} class="w-full h-full object-cover" data-v-b5729ff7></div><div class="flex-1" data-v-b5729ff7><div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-2 sm:mb-1" data-v-b5729ff7><div class="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 mb-2 sm:mb-0" data-v-b5729ff7><h3 class="text-base sm:text-lg font-semibold text-gray-900 text-center sm:text-left" data-v-b5729ff7>${ssrInterpolate(appointment.salonName)}</h3><span class="inline-flex items-center justify-center text-white px-3 py-1.5 sm:px-4 sm:py-2 rounded-full text-xs sm:text-[14px] font-medium self-center sm:self-auto" style="${ssrRenderStyle({ "background-color": "#1739BD" })}" data-v-b5729ff7> Upcoming </span></div><div class="flex flex-col sm:flex-row gap-2 w-full sm:w-auto" data-v-b5729ff7><button class="px-4 py-2 sm:px-6 sm:py-2.5 rounded-full text-xs sm:text-[14px] font-semibold text-[#7B27E7] bg-[rgba(74,39,231,0.1)] hover:bg-[rgba(74,39,231,0.2)] transition-colors cursor-pointer" data-v-b5729ff7> Reschedule </button><button class="px-4 py-2 sm:px-6 sm:py-2.5 text-xs sm:text-[14px] font-semibold text-[#1976D2] bg-white border border-[rgba(0,0,0,0.09)] rounded-full hover:bg-gray-50 transition-colors cursor-pointer" data-v-b5729ff7> Cancel </button></div></div><div class="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4 text-xs sm:text-sm text-gray-600 mb-2 sm:mb-1" data-v-b5729ff7><span class="flex items-center justify-center sm:justify-start gap-1" data-v-b5729ff7><svg class="w-3 h-3 sm:w-4 sm:h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-b5729ff7><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" data-v-b5729ff7></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" data-v-b5729ff7></path></svg> ${ssrInterpolate(appointment.location)}</span></div><div class="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4 text-xs sm:text-sm text-gray-600 mb-3 sm:mb-1" data-v-b5729ff7><span class="flex items-center justify-center sm:justify-start gap-1" data-v-b5729ff7><svg class="w-3 h-3 sm:w-4 sm:h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-b5729ff7><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" data-v-b5729ff7></path></svg> ${ssrInterpolate(appointment.date)}</span><span class="flex items-center justify-center sm:justify-start gap-1" data-v-b5729ff7><svg class="w-3 h-3 sm:w-4 sm:h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-b5729ff7><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" data-v-b5729ff7></path></svg> ${ssrInterpolate(appointment.time)}</span></div><div class="pt-2 sm:pt-1" data-v-b5729ff7><h4 class="font-semibold text-sm sm:text-[16px] mb-2 sm:mb-1 text-center sm:text-left" data-v-b5729ff7> Services Overview </h4><div class="space-y-2 sm:space-y-3" data-v-b5729ff7><!--[-->`);
        ssrRenderList(appointment.services, (service) => {
          _push(`<div class="flex items-center justify-between" data-v-b5729ff7><div class="flex-1" data-v-b5729ff7><p class="font-semibold text-sm sm:text-[16px]" data-v-b5729ff7>${ssrInterpolate(service.name)}</p><p class="text-xs sm:text-[15px] text-gray-500 flex items-center justify-start gap-1" data-v-b5729ff7><svg class="w-3 h-3 sm:w-4 sm:h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-b5729ff7><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" data-v-b5729ff7></path></svg> ${ssrInterpolate(service.duration)} mins </p></div><span class="font-semibold text-sm sm:text-base text-gray-900 ml-2" data-v-b5729ff7>\u20B9${ssrInterpolate(service.price)}</span></div>`);
        });
        _push(`<!--]--></div><div class="flex items-center justify-between pt-3 sm:pt-4 border-t border-gray-300 mt-3 sm:mt-4" data-v-b5729ff7><span class="font-semibold text-base sm:text-lg text-gray-900" data-v-b5729ff7>Total</span><span class="font-bold text-base sm:text-lg text-gray-900" data-v-b5729ff7>\u20B9${ssrInterpolate(appointment.total)}</span></div></div></div></div></div>`);
      });
      _push(`<!--]--></div><div class="mb-6 sm:mb-8" data-v-b5729ff7><h2 class="text-sm sm:text-[16px] font-semibold text-gray-700 mb-3 sm:mb-4 pt-2 pb-2 border-b border-gray-300" data-v-b5729ff7> COMPLETED APPOINTMENTS (${ssrInterpolate(unref(completedAppointments).length)}) </h2><!--[-->`);
      ssrRenderList(unref(completedAppointments), (appointment) => {
        _push(`<div class="bg-white border sm:border-0 rounded-lg sm:rounded-none p-3 sm:p-0 mb-4" data-v-b5729ff7><div class="flex flex-col sm:flex-row sm:items-start gap-3 sm:gap-4" data-v-b5729ff7><div class="w-16 h-16 sm:w-20 sm:h-20 rounded-lg overflow-hidden flex-shrink-0 mx-auto sm:mx-0" data-v-b5729ff7><img${ssrRenderAttr("src", appointment.image)}${ssrRenderAttr("alt", appointment.salonName)} class="w-full h-full object-cover" data-v-b5729ff7></div><div class="flex-1" data-v-b5729ff7><div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-2 sm:mb-1" data-v-b5729ff7><div class="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 mb-2 sm:mb-0" data-v-b5729ff7><h3 class="text-base sm:text-lg font-semibold text-gray-900 text-center sm:text-left" data-v-b5729ff7>${ssrInterpolate(appointment.salonName)}</h3><span class="inline-flex items-center justify-center text-white px-3 py-1.5 sm:px-4 sm:py-2 rounded-full text-xs sm:text-[14px] font-medium self-center sm:self-auto" style="${ssrRenderStyle({ "background-color": "#0B8A17" })}" data-v-b5729ff7> Completed </span></div><div class="flex flex-col sm:flex-row gap-2 w-full sm:w-auto" data-v-b5729ff7><button class="px-4 py-2 sm:px-6 sm:py-2.5 text-xs sm:text-[14px] font-semibold text-[#1976D2] bg-white border border-[rgba(0,0,0,0.09)] rounded-full hover:bg-gray-50 transition-colors cursor-pointer" data-v-b5729ff7> Write a review </button></div></div><div class="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4 text-xs sm:text-sm text-gray-600 mb-2 sm:mb-1" data-v-b5729ff7><span class="flex items-center justify-center sm:justify-start gap-1" data-v-b5729ff7><svg class="w-3 h-3 sm:w-4 sm:h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-b5729ff7><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" data-v-b5729ff7></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" data-v-b5729ff7></path></svg> ${ssrInterpolate(appointment.location)}</span></div><div class="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4 text-xs sm:text-sm text-gray-600 mb-3 sm:mb-1" data-v-b5729ff7><span class="flex items-center justify-center sm:justify-start gap-1" data-v-b5729ff7><svg class="w-3 h-3 sm:w-4 sm:h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-b5729ff7><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" data-v-b5729ff7></path></svg> ${ssrInterpolate(appointment.date)}</span><span class="flex items-center justify-center sm:justify-start gap-1" data-v-b5729ff7><svg class="w-3 h-3 sm:w-4 sm:h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-b5729ff7><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" data-v-b5729ff7></path></svg> ${ssrInterpolate(appointment.time)}</span></div><div class="pt-2 sm:pt-1" data-v-b5729ff7><h4 class="font-semibold text-sm sm:text-[16px] mb-2 sm:mb-1 text-center sm:text-left" data-v-b5729ff7> Services Overview </h4><div class="space-y-2 sm:space-y-3" data-v-b5729ff7><!--[-->`);
        ssrRenderList(appointment.services, (service) => {
          _push(`<div class="flex items-center justify-between" data-v-b5729ff7><div class="flex-1" data-v-b5729ff7><p class="font-semibold text-sm sm:text-[16px]" data-v-b5729ff7>${ssrInterpolate(service.name)}</p><p class="text-xs sm:text-[15px] text-gray-500 flex items-center justify-start gap-1" data-v-b5729ff7><svg class="w-3 h-3 sm:w-4 sm:h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-b5729ff7><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" data-v-b5729ff7></path></svg> ${ssrInterpolate(service.duration)} mins </p></div><span class="font-semibold text-sm sm:text-base text-gray-900 ml-2" data-v-b5729ff7>\u20B9${ssrInterpolate(service.price)}</span></div>`);
        });
        _push(`<!--]--></div><div class="flex items-center justify-between pt-3 sm:pt-4 border-t border-gray-300 mt-3 sm:mt-4" data-v-b5729ff7><span class="font-semibold text-base sm:text-lg text-gray-900" data-v-b5729ff7>Total</span><span class="font-bold text-base sm:text-lg text-gray-900" data-v-b5729ff7>\u20B9${ssrInterpolate(appointment.total)}</span></div></div></div></div></div>`);
      });
      _push(`<!--]--></div><div class="mb-6 sm:mb-8" data-v-b5729ff7><h2 class="text-sm sm:text-[16px] font-semibold text-gray-700 mb-3 sm:mb-4 pt-2 pb-2 border-b border-gray-300" data-v-b5729ff7> CANCELED APPOINTMENTS (${ssrInterpolate(unref(canceledAppointments).length)}) </h2><!--[-->`);
      ssrRenderList(unref(canceledAppointments), (appointment) => {
        _push(`<div class="bg-white border sm:border-0 rounded-lg sm:rounded-none p-3 sm:p-0 mb-4" data-v-b5729ff7><div class="flex flex-col sm:flex-row sm:items-start gap-3 sm:gap-4" data-v-b5729ff7><div class="w-16 h-16 sm:w-20 sm:h-20 rounded-lg overflow-hidden flex-shrink-0 mx-auto sm:mx-0" data-v-b5729ff7><img${ssrRenderAttr("src", appointment.image)}${ssrRenderAttr("alt", appointment.salonName)} class="w-full h-full object-cover" data-v-b5729ff7></div><div class="flex-1" data-v-b5729ff7><div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-2 sm:mb-1" data-v-b5729ff7><div class="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 mb-2 sm:mb-0" data-v-b5729ff7><h3 class="text-base sm:text-lg font-semibold text-gray-900 text-center sm:text-left" data-v-b5729ff7>${ssrInterpolate(appointment.salonName)}</h3><span class="inline-flex items-center justify-center text-white px-3 py-1.5 sm:px-4 sm:py-2 rounded-full text-xs sm:text-[14px] font-medium self-center sm:self-auto" style="${ssrRenderStyle({ "background-color": "#E21F1F" })}" data-v-b5729ff7> Canceled </span></div><div class="flex flex-col sm:flex-row gap-2 w-full sm:w-auto" data-v-b5729ff7><button class="px-4 py-2 sm:px-6 sm:py-2 text-xs sm:text-[14px] font-semibold text-[#E79427] bg-[rgba(231,148,39,0.1)] rounded-full hover:bg-[rgba(231,148,39,0.2)] transition-colors cursor-pointer" data-v-b5729ff7> Re-order </button><button class="px-4 py-2 sm:px-6 sm:py-2.5 text-xs sm:text-[14px] font-semibold text-[#1976D2] bg-white border border-[rgba(0,0,0,0.09)] rounded-full hover:bg-gray-50 transition-colors cursor-pointer" data-v-b5729ff7> Write a review </button></div></div><div class="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4 text-xs sm:text-sm text-gray-600 mb-2 sm:mb-1" data-v-b5729ff7><span class="flex items-center justify-center sm:justify-start gap-1" data-v-b5729ff7><svg class="w-3 h-3 sm:w-4 sm:h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-b5729ff7><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" data-v-b5729ff7></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" data-v-b5729ff7></path></svg> ${ssrInterpolate(appointment.location)}</span></div><div class="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4 text-xs sm:text-sm text-gray-600 mb-3 sm:mb-1" data-v-b5729ff7><span class="flex items-center justify-center sm:justify-start gap-1" data-v-b5729ff7><svg class="w-3 h-3 sm:w-4 sm:h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-b5729ff7><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" data-v-b5729ff7></path></svg> ${ssrInterpolate(appointment.date)}</span><span class="flex items-center justify-center sm:justify-start gap-1" data-v-b5729ff7><svg class="w-3 h-3 sm:w-4 sm:h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-b5729ff7><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" data-v-b5729ff7></path></svg> ${ssrInterpolate(appointment.time)}</span></div><div class="pt-2 sm:pt-1" data-v-b5729ff7><h4 class="font-semibold text-sm sm:text-[16px] mb-2 sm:mb-1 text-center sm:text-left" data-v-b5729ff7> Services Overview </h4><div class="space-y-2 sm:space-y-3" data-v-b5729ff7><!--[-->`);
        ssrRenderList(appointment.services, (service) => {
          _push(`<div class="flex items-center justify-between" data-v-b5729ff7><div class="flex-1" data-v-b5729ff7><p class="font-semibold text-sm sm:text-[16px]" data-v-b5729ff7>${ssrInterpolate(service.name)}</p><p class="text-xs sm:text-[15px] text-gray-500 flex items-center justify-start gap-1" data-v-b5729ff7><svg class="w-3 h-3 sm:w-4 sm:h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-b5729ff7><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" data-v-b5729ff7></path></svg> ${ssrInterpolate(service.duration)} mins </p></div><span class="font-semibold text-sm sm:text-base text-gray-900 ml-2" data-v-b5729ff7>\u20B9${ssrInterpolate(service.price)}</span></div>`);
        });
        _push(`<!--]--></div><div class="flex items-center justify-between pt-3 sm:pt-4 border-t border-gray-300 mt-3 sm:mt-4" data-v-b5729ff7><span class="font-semibold text-base sm:text-lg text-gray-900" data-v-b5729ff7>Total</span><span class="font-bold text-base sm:text-lg text-gray-900" data-v-b5729ff7>\u20B9${ssrInterpolate(appointment.total)}</span></div></div></div></div></div>`);
      });
      _push(`<!--]--></div></div></div>`);
    };
  }
});
const _sfc_setup$3 = _sfc_main$3.setup;
_sfc_main$3.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("components/profile/ProfileOrders.vue");
  return _sfc_setup$3 ? _sfc_setup$3(props, ctx) : void 0;
};
const ProfileOrders = /* @__PURE__ */ _export_sfc(_sfc_main$3, [["__scopeId", "data-v-b5729ff7"]]);
const _sfc_main$2 = /* @__PURE__ */ defineComponent({
  __name: "ProfileMemberships",
  __ssrInlineRender: true,
  setup(__props) {
    useAppSeoMeta({
      title: "Memberships | Bookslotz",
      description: "Manage your salon memberships and view transaction history with Bookslotz."
    });
    const { public: { BASE_URL } } = useRuntimeConfig();
    const isLoading = useState("globalLoader", () => false);
    const hasError = ref(false);
    const snackbarRef = ref(null);
    const isModalOpen = ref(false);
    const selectedCard = ref(null);
    const membershipCards = ref([]);
    const transactions = ref([]);
    const modalTransactions = ref([]);
    ref([]);
    return (_ctx, _push, _parent, _attrs) => {
      var _a, _b, _c, _d, _e, _f, _g, _h, _i, _j, _k;
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "min-h-screen bg-white flex justify-center items-start relative" }, _attrs))}>`);
      if (unref(isLoading)) {
        _push(ssrRenderComponent(Loader, null, null, _parent));
      } else {
        _push(`<!---->`);
      }
      _push(ssrRenderComponent(Snackbar, {
        ref_key: "snackbarRef",
        ref: snackbarRef
      }, null, _parent));
      if (hasError.value) {
        _push(ssrRenderComponent(_sfc_main$1$1, { error: { statusCode: 500, message: "Something went wrong. Please try again later." } }, null, _parent));
      } else {
        _push(`<!---->`);
      }
      if (!hasError.value) {
        _push(`<div class="w-full max-w-7xl xl:pr-45"><h1 class="text-[25px] font-semibold mb-2">Memberships</h1><h2 class="text-[18px] font-medium mb-6">Active Memberships</h2><div class="border border-gray-200 rounded-lg p-6">`);
        if (membershipCards.value.length === 0 && !unref(isLoading)) {
          _push(`<div class="text-center py-8"><div class="text-gray-400 mb-4"><svg xmlns="http://www.w3.org/2000/svg" class="w-12 h-12 mx-auto" viewBox="0 0 24 24" fill="currentColor"><path d="M20 2H4c-1.11 0-2 .89-2 2v11c0 1.11.89 2 2 2h4v5l4-2l4 2v-5h4c1.11 0 2-.89 2-2V4c0-1.11-.89-2-2-2m0 13H4v-2h16zm0-5H4V4h16z"></path></svg></div><p class="text-gray-500 text-lg">No memberships found</p></div>`);
        } else {
          _push(`<div class="grid grid-cols-1 sm:grid-cols-2 gap-4"><!--[-->`);
          ssrRenderList(membershipCards.value, (card, index2) => {
            _push(`<div class="rounded-xl p-6 text-white relative overflow-hidden" style="${ssrRenderStyle(`background: ${card.gradient}`)}"><div class="absolute top-6 right-6 flex flex-col gap-2"><button class="bg-white bg-opacity-20 hover:bg-opacity-30 text-black text-[12px] font-semibold px-6 py-2 rounded-full transition-all cursor-pointer"> Details </button></div><div class="pr-24"><h3 class="text-[20px] text-[700] font-semibold mb-4">${ssrInterpolate(card.title)}</h3><div class="space-y-1"><p class="text-[16px] text-[400] opacity-90">${ssrInterpolate(card.plan)} (${ssrInterpolate(card.status === "ACTIVE" ? "Active" : "Expired")}) </p><p class="text-[16px] text-[400] opacity-90">Uses Left : ${ssrInterpolate(card.usesLeft)}</p><p class="text-[16px] text-[400] opacity-90">${ssrInterpolate(card.status === "ACTIVE" ? "Valid Till" : "Expired on")} : ${ssrInterpolate(card.validity)}</p></div></div></div>`);
          });
          _push(`<!--]--></div>`);
        }
        _push(`</div><div class="border border-gray-200 rounded-xl overflow-hidden mt-4"><div class="overflow-x-auto"><table class="min-w-full"><thead class="bg-gray-50"><tr><th class="px-6 py-4 text-left text-[16px] font-medium text-[#494949] tracking-wider">Date</th><th class="px-6 py-4 text-left text-[16px] font-medium text-[#494949] tracking-wider">Amount</th><th class="px-6 py-4 text-left text-[16px] font-medium text-[#494949] tracking-wider">Action</th><th class="px-6 py-4 text-left text-[16px] font-medium text-[#494949] tracking-wider">Saloon</th><th class="px-6 py-4 text-left text-[16px] font-medium text-[#494949] tracking-wider">Purchase Mode</th></tr></thead><tbody class="bg-white divide-y divide-gray-100">`);
        if (transactions.value.length === 0 && !unref(isLoading)) {
          _push(`<tr><td colspan="5" class="px-6 py-8 text-center text-gray-500">No transactions found</td></tr>`);
        } else {
          _push(`<!---->`);
        }
        _push(`<!--[-->`);
        ssrRenderList(transactions.value, (transaction, index2) => {
          _push(`<tr class="hover:bg-gray-50/50 transition-colors"><td class="px-6 py-4 text-[16px] font-semibold" style="${ssrRenderStyle({ "color": "#4D0BA3" })}">${ssrInterpolate(transaction.date)}</td><td class="px-6 py-4 text-[16px] font-medium text-gray-900">${ssrInterpolate(transaction.amount)}</td><td class="px-6 py-4 text-[16px] text-gray-900">${ssrInterpolate(transaction.action)}</td><td class="px-6 py-4 text-[16px] text-gray-900">${ssrInterpolate(transaction.saloon)}</td><td class="px-6 py-4 text-[16px] text-gray-900">${ssrInterpolate(transaction.purchaseMode)}</td></tr>`);
        });
        _push(`<!--]--></tbody></table></div></div>`);
        if (isModalOpen.value) {
          _push(`<div class="fixed inset-0 bg-black/50 bg-opacity-50 flex items-center justify-center z-50"><div class="bg-white rounded-2xl w-full max-w-lg mx-auto max-h-[98vh] flex flex-col relative"><div class="p-4 sm:p-6 flex-shrink-0"><h3 class="text-[18px] font-semibold text-[700]">Membership Details</h3></div><div class="p-4 sm:p-6 space-y-6 overflow-y-auto flex-1"><div class="border border-gray-200 rounded-lg p-4 sm:p-6"><div class="grid grid-cols-1 sm:grid-cols-2 gap-y-4 gap-x-8"><div class="space-y-1"><div class="text-[12px] sm:text-[14px] text-[400] text-gray-600">Salon Name :</div><div class="text-[14px] sm:text-[16px] font-semibold break-words">${ssrInterpolate((_a = selectedCard.value) == null ? void 0 : _a.title)}</div></div><div class="space-y-1 sm:pl-10"><div class="text-[12px] sm:text-[14px] text-[400] text-gray-600">Plan :</div><div class="text-[14px] sm:text-[16px] font-semibold">${ssrInterpolate((_b = selectedCard.value) == null ? void 0 : _b.plan)}</div></div><div class="space-y-1"><div class="text-[12px] sm:text-[14px] text-[400] text-gray-600">Purchased On :</div><div class="text-[14px] sm:text-[16px] font-semibold">${ssrInterpolate((_c = selectedCard.value) == null ? void 0 : _c.purchasedOn)}</div></div><div class="space-y-1 sm:pl-10"><div class="text-[12px] sm:text-[14px] text-[400] text-gray-600">Remaining Uses :</div><div class="text-[14px] sm:text-[16px] font-semibold">${ssrInterpolate((_d = selectedCard.value) == null ? void 0 : _d.usesLeft)}</div></div><div class="space-y-1"><div class="text-[12px] sm:text-[14px] text-[400] text-gray-600">Validity :</div><div class="text-[14px] sm:text-[16px] font-semibold">${ssrInterpolate((_e = selectedCard.value) == null ? void 0 : _e.validity)}</div></div><div class="space-y-1 sm:pl-10"><div class="text-[12px] sm:text-[14px] text-[400] text-gray-600">Status :</div><div class="${ssrRenderClass([((_f = selectedCard.value) == null ? void 0 : _f.status) === "ACTIVE" ? "text-green-500" : "text-red-500", "text-[14px] sm:text-[16px] font-semibold"])}">${ssrInterpolate(((_g = selectedCard.value) == null ? void 0 : _g.status) === "ACTIVE" ? "Active" : "Expired")}</div></div><div class="space-y-1"><div class="text-[12px] sm:text-[14px] text-[400] text-gray-600">Amount Paid :</div><div class="text-[14px] sm:text-[16px] font-semibold">\u20B9${ssrInterpolate((_h = selectedCard.value) == null ? void 0 : _h.amount)}</div></div><div class="space-y-1 sm:pl-10"><div class="text-[12px] sm:text-[14px] text-[400] text-gray-600">Payment Method :</div><div class="text-[14px] sm:text-[16px] font-semibold">${ssrInterpolate((_i = selectedCard.value) == null ? void 0 : _i.paymentMethod)}</div></div><div class="col-span-1 sm:col-span-2 space-y-1"><div class="text-[12px] sm:text-[14px] text-[400] text-gray-600">Services :</div><div class="text-[14px] sm:text-[16px] font-semibold text-gray-900">${ssrInterpolate((_j = selectedCard.value) == null ? void 0 : _j.servicesCount)} Services <span class="font-normal text-gray-600 block sm:inline mt-1 sm:mt-0">(${ssrInterpolate((_k = selectedCard.value) == null ? void 0 : _k.servicesList)})</span></div></div></div></div><div class="border-t border-gray-200 pt-3 pb-6"><h4 class="text-[16px] sm:text-[18px] font-semibold text-[700] mb-4">Transactions</h4><div class="space-y-4">`);
          if (modalTransactions.value.length === 0) {
            _push(`<div class="text-center text-gray-500 py-4"> No transactions found </div>`);
          } else {
            _push(`<!---->`);
          }
          _push(`<!--[-->`);
          ssrRenderList(modalTransactions.value, (transaction, index2) => {
            _push(`<div class="flex justify-between items-start gap-4"><div class="flex-1 min-w-0"><div class="text-[14px] sm:text-[16px] font-semibold">${ssrInterpolate(transaction.date)}</div><div class="text-[14px] sm:text-[16px] text-gray-700 break-words">${ssrInterpolate(transaction.service)}</div></div><div class="text-[14px] sm:text-[16px] font-bold flex-shrink-0 text-red-500"> -\u20B9${ssrInterpolate(transaction.amount)}</div></div>`);
          });
          _push(`<!--]--></div></div></div><div class="p-4 sm:p-6 bg-white border-t border-gray-100 rounded-b-2xl flex-shrink-0"><button class="w-full bg-purple-600 hover:bg-purple-700 text-white text-[14px] sm:text-[16px] font-semibold py-3 rounded-full transition-colors cursor-pointer"> Close </button></div></div></div>`);
        } else {
          _push(`<!---->`);
        }
        _push(`</div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div>`);
    };
  }
});
const _sfc_setup$2 = _sfc_main$2.setup;
_sfc_main$2.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("components/profile/ProfileMemberships.vue");
  return _sfc_setup$2 ? _sfc_setup$2(props, ctx) : void 0;
};
const _sfc_main$1 = /* @__PURE__ */ defineComponent({
  __name: "ProfileSettings",
  __ssrInlineRender: true,
  setup(__props) {
    const userStore = useUserStore();
    const { public: { BASE_URL } } = useRuntimeConfig();
    const isLoading = useState("globalLoader", () => false);
    const hasError = ref(false);
    const fetchingProfile = ref(false);
    const profileData = ref({
      enableAlerts: false,
      enablePhoneNotifications: false,
      enableEmailNotifications: false
    });
    const snackbarRef = ref(null);
    useAppSeoMeta({
      title: "Settings | Bookslotz",
      description: "Manage your notification preferences..."
    });
    return (_ctx, _push, _parent, _attrs) => {
      const _component_Error = resolveComponent("Error");
      const _component_NuxtLink = __nuxt_component_0$2;
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "min-h-screen bg-white flex justify-center items-start relative" }, _attrs))} data-v-adf81e9c>`);
      if (unref(isLoading) || fetchingProfile.value) {
        _push(ssrRenderComponent(Loader, null, null, _parent));
      } else {
        _push(`<!---->`);
      }
      _push(ssrRenderComponent(Snackbar, {
        ref_key: "snackbarRef",
        ref: snackbarRef
      }, null, _parent));
      if (hasError.value) {
        _push(ssrRenderComponent(_component_Error, { error: { statusCode: 500, message: "Something went wrong. Please try again later." } }, null, _parent));
      } else {
        _push(`<!---->`);
      }
      if (unref(userStore).loggedIn && !hasError.value && !fetchingProfile.value) {
        _push(`<div class="w-full max-w-7xl" data-v-adf81e9c><h2 class="text-[25px] font-semibold mb-3" data-v-adf81e9c>Settings</h2><div class="border border-gray-200 rounded-lg p-6" data-v-adf81e9c><h3 class="text-[18px] font-semibold mb-1" data-v-adf81e9c>My Notifications</h3><p class="text-[16px] text-gray-500 mb-6" data-v-adf81e9c> We will send you updates about your appointments, news and offers. </p><div class="flex flex-col sm:flex-row sm:items-start justify-between gap-2 mb-4" data-v-adf81e9c><div data-v-adf81e9c><p class="text-[18px] font-semibold" data-v-adf81e9c>Text &amp; app alerts</p><p class="text-[16px] text-gray-500" data-v-adf81e9c>Receive texts based on your sender&#39;s settings</p></div><input type="checkbox" class="toggle toggle-success self-start sm:self-auto"${ssrIncludeBooleanAttr(profileData.value.enableAlerts) ? " checked" : ""} data-v-adf81e9c></div><div class="flex flex-col sm:flex-row sm:items-start justify-between gap-2 mb-4" data-v-adf81e9c><div data-v-adf81e9c><p class="text-[18px] font-semibold" data-v-adf81e9c>Email updates</p><p class="text-[16px] text-gray-500" data-v-adf81e9c> Receive appointment details, receipts, and special promotions. </p></div><input type="checkbox" class="toggle toggle-success self-start sm:self-auto"${ssrIncludeBooleanAttr(profileData.value.enableEmailNotifications) ? " checked" : ""} data-v-adf81e9c></div><div class="flex flex-col sm:flex-row sm:items-start justify-between gap-2 mb-4" data-v-adf81e9c><div data-v-adf81e9c><p class="text-[18px] font-semibold" data-v-adf81e9c>Phone notifications</p><p class="text-[16px] text-gray-500" data-v-adf81e9c> Get call reminders and urgent booking changes. </p></div><input type="checkbox" class="toggle toggle-success self-start sm:self-auto"${ssrIncludeBooleanAttr(profileData.value.enablePhoneNotifications) ? " checked" : ""} data-v-adf81e9c></div></div></div>`);
      } else if (!unref(userStore).loggedIn && !hasError.value) {
        _push(`<div class="w-full max-w-7xl text-center" data-v-adf81e9c><h2 class="text-[25px] font-semibold mb-6" data-v-adf81e9c>Settings</h2><p class="text-lg text-gray-600 mb-4" data-v-adf81e9c>Please log in to manage your settings.</p>`);
        _push(ssrRenderComponent(_component_NuxtLink, {
          to: "/auth/login",
          class: "inline-block bg-[#7B27E7] text-white text-base font-medium px-6 py-2 rounded-full hover:bg-[#6a1cd6] transition-colors"
        }, {
          default: withCtx((_, _push2, _parent2, _scopeId) => {
            if (_push2) {
              _push2(` Log In `);
            } else {
              return [
                createTextVNode(" Log In ")
              ];
            }
          }),
          _: 1
        }, _parent));
        _push(`</div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div>`);
    };
  }
});
const _sfc_setup$1 = _sfc_main$1.setup;
_sfc_main$1.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("components/profile/ProfileSettings.vue");
  return _sfc_setup$1 ? _sfc_setup$1(props, ctx) : void 0;
};
const ProfileSettings = /* @__PURE__ */ _export_sfc(_sfc_main$1, [["__scopeId", "data-v-adf81e9c"]]);
const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "index",
  __ssrInlineRender: true,
  setup(__props) {
    useUserStore();
    const tabComponents = markRaw({
      profile: _sfc_main$7,
      cart: ProfileCart,
      wishlist: ProfileWishlist,
      giftcards: ProfileGiftCards,
      orders: ProfileOrders,
      memberships: _sfc_main$2,
      settings: ProfileSettings
    });
    const route = useRoute();
    const selectedTab = computed(() => {
      const tab = route.query.tab;
      return tab && tab in tabComponents ? tab : "profile";
    });
    return (_ctx, _push, _parent, _attrs) => {
      const _component_NuxtLink = __nuxt_component_0$2;
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "min-h-screen bg-gray-40" }, _attrs))} data-v-43160719><div class="max-w-8xl mx-auto px-4 sm:px-20 py-8 mt-10" data-v-43160719><div class="flex flex-col lg:flex-row gap-6 lg:items-start" data-v-43160719><div class="lg:w-1/4 rounded-2xl border border-gray-200 shadow-sm p-6 h-auto" data-v-43160719><nav class="flex flex-col gap-2" data-v-43160719>`);
      _push(ssrRenderComponent(_component_NuxtLink, {
        to: { path: "/profile", query: { tab: "profile" } },
        class: ["relative flex items-center gap-3 px-4 py-3 rounded-lg text-gray-900 hover:bg-purple-50 transition-colors sidebar-text", { "bg-purple-100 text-purple-700": selectedTab.value === "profile" }]
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`<svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" class="${ssrRenderClass({ "text-purple-700": selectedTab.value === "profile" })}" data-v-43160719${_scopeId}><path fill="currentColor" d="M12 6c1.1 0 2 .9 2 2s-.9 2-2 2s-2-.9-2-2s.9-2 2-2m0 10c2.7 0 5.8 1.29 6 2H6c.23-.72 3.31-2 6-2m0-12C9.79 4 8 5.79 8 8s1.79 4 4 4s4-1.79 4-4s-1.79-4-4-4m0 10c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4" data-v-43160719${_scopeId}></path></svg> Profile `);
          } else {
            return [
              (openBlock(), createBlock("svg", {
                xmlns: "http://www.w3.org/2000/svg",
                width: "22",
                height: "22",
                viewBox: "0 0 24 24",
                class: { "text-purple-700": selectedTab.value === "profile" }
              }, [
                createVNode("path", {
                  fill: "currentColor",
                  d: "M12 6c1.1 0 2 .9 2 2s-.9 2-2 2s-2-.9-2-2s.9-2 2-2m0 10c2.7 0 5.8 1.29 6 2H6c.23-.72 3.31-2 6-2m0-12C9.79 4 8 5.79 8 8s1.79 4 4 4s4-1.79 4-4s-1.79-4-4-4m0 10c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4"
                })
              ], 2)),
              createTextVNode(" Profile ")
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(ssrRenderComponent(_component_NuxtLink, {
        to: { path: "/profile", query: { tab: "cart" } },
        class: ["relative flex items-center gap-3 px-4 py-3 rounded-lg text-gray-900 hover:bg-purple-50 transition-colors sidebar-text", { "bg-purple-100 text-purple-700": selectedTab.value === "cart" }]
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`<svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" class="${ssrRenderClass({ "text-purple-700": selectedTab.value === "cart" })}" data-v-43160719${_scopeId}><path fill="currentColor" d="M15.55 13c.75 0 1.41-.41 1.75-1.03l3.58-6.49A.996.996 0 0 0 20.01 4H5.21l-.94-2H1v2h2l3.6 7.59l-1.35 2.44C4.52 15.37 5.48 17 7 17h12v-2H7l1.1-2zM6.16 6h12.15l-2.76 5H8.53zM7 18c-1.1 0-1.99.9-1.99 2S5.9 22 7 22s2-.9 2-2s-.9-2-2-2m10 0c-1.1 0-1.99.9-1.99 2s.89 2 1.99 2s2-.9 2-2s-.9-2-2-2" data-v-43160719${_scopeId}></path></svg> Cart `);
          } else {
            return [
              (openBlock(), createBlock("svg", {
                xmlns: "http://www.w3.org/2000/svg",
                width: "22",
                height: "22",
                viewBox: "0 0 24 24",
                class: { "text-purple-700": selectedTab.value === "cart" }
              }, [
                createVNode("path", {
                  fill: "currentColor",
                  d: "M15.55 13c.75 0 1.41-.41 1.75-1.03l3.58-6.49A.996.996 0 0 0 20.01 4H5.21l-.94-2H1v2h2l3.6 7.59l-1.35 2.44C4.52 15.37 5.48 17 7 17h12v-2H7l1.1-2zM6.16 6h12.15l-2.76 5H8.53zM7 18c-1.1 0-1.99.9-1.99 2S5.9 22 7 22s2-.9 2-2s-.9-2-2-2m10 0c-1.1 0-1.99.9-1.99 2s.89 2 1.99 2s2-.9 2-2s-.9-2-2-2"
                })
              ], 2)),
              createTextVNode(" Cart ")
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(ssrRenderComponent(_component_NuxtLink, {
        to: { path: "/profile", query: { tab: "wishlist" } },
        class: ["text-[20px] relative flex items-center gap-3 px-4 py-3 rounded-lg text-gray-900 hover:bg-purple-50 transition-colors sidebar-text", { "bg-purple-100 text-purple-700": selectedTab.value === "wishlist" }]
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`<svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" class="${ssrRenderClass({ "text-purple-700": selectedTab.value === "wishlist" })}" data-v-43160719${_scopeId}><path fill="currentColor" d="M16.5 3c-1.74 0-3.41.81-4.5 2.09C10.91 3.81 9.24 3 7.5 3C4.42 3 2 5.42 2 8.5c0 3.78 3.4 6.86 8.55 11.54L12 21.35l1.45-1.32C18.6 15.36 22 12.28 22 8.5C22 5.42 19.58 3 16.5 3m-4.4 15.55l-.1.1l-.1-.1C7.14 14.24 4 11.39 4 8.5C4 6.5 5.5 5 7.5 5c1.54 0 3.04.99 3.57 2.36h1.87C13.46 5.99 14.96 5 16.5 5c2 0 3.5 1.5 3.5 3.5c0 2.89-3.14 5.74-7.9 10.05" data-v-43160719${_scopeId}></path></svg> Wishlist `);
          } else {
            return [
              (openBlock(), createBlock("svg", {
                xmlns: "http://www.w3.org/2000/svg",
                width: "22",
                height: "22",
                viewBox: "0 0 24 24",
                class: { "text-purple-700": selectedTab.value === "wishlist" }
              }, [
                createVNode("path", {
                  fill: "currentColor",
                  d: "M16.5 3c-1.74 0-3.41.81-4.5 2.09C10.91 3.81 9.24 3 7.5 3C4.42 3 2 5.42 2 8.5c0 3.78 3.4 6.86 8.55 11.54L12 21.35l1.45-1.32C18.6 15.36 22 12.28 22 8.5C22 5.42 19.58 3 16.5 3m-4.4 15.55l-.1.1l-.1-.1C7.14 14.24 4 11.39 4 8.5C4 6.5 5.5 5 7.5 5c1.54 0 3.04.99 3.57 2.36h1.87C13.46 5.99 14.96 5 16.5 5c2 0 3.5 1.5 3.5 3.5c0 2.89-3.14 5.74-7.9 10.05"
                })
              ], 2)),
              createTextVNode(" Wishlist ")
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(ssrRenderComponent(_component_NuxtLink, {
        to: { path: "/profile", query: { tab: "orders" } },
        class: ["relative flex items-center gap-3 px-4 py-3 rounded-lg text-gray-900 hover:bg-purple-50 transition-colors sidebar-text", { "bg-purple-100 text-purple-700": selectedTab.value === "orders" }]
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`<svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" class="${ssrRenderClass({ "text-purple-700": selectedTab.value === "orders" })}" data-v-43160719${_scopeId}><path fill="currentColor" d="M21 3H3c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h18c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2m0 16H3V5h18zM5 10h9v2H5zm0-3h9v2H5z" data-v-43160719${_scopeId}></path></svg> Orders `);
          } else {
            return [
              (openBlock(), createBlock("svg", {
                xmlns: "http://www.w3.org/2000/svg",
                width: "22",
                height: "22",
                viewBox: "0 0 24 24",
                class: { "text-purple-700": selectedTab.value === "orders" }
              }, [
                createVNode("path", {
                  fill: "currentColor",
                  d: "M21 3H3c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h18c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2m0 16H3V5h18zM5 10h9v2H5zm0-3h9v2H5z"
                })
              ], 2)),
              createTextVNode(" Orders ")
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(ssrRenderComponent(_component_NuxtLink, {
        to: { path: "/profile", query: { tab: "giftcards" } },
        class: ["relative flex items-center gap-3 px-4 py-3 rounded-lg text-gray-900 hover:bg-purple-50 transition-colors sidebar-text", { "bg-purple-100 text-purple-700": selectedTab.value === "giftcards" }]
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`<svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" class="${ssrRenderClass({ "text-purple-700": selectedTab.value === "giftcards" })}" data-v-43160719${_scopeId}><path fill="currentColor" d="M14.8 8L16 9.2L9.2 16L8 14.8zM4 4h16c1.11 0 2 .89 2 2v4a2 2 0 1 0 0 4v4c0 1.11-.89 2-2 2H4a2 2 0 0 1-2-2v-4c1.11 0 2-.89 2-2a2 2 0 0 0-2-2V6a2 2 0 0 1 2-2m0 2v2.54a3.994 3.994 0 0 1 0 6.92V18h16v-2.54a3.994 3.994 0 0 1 0-6.92V6zm5.5 2c.83 0 1.5.67 1.5 1.5S10.33 11 9.5 11S8 10.33 8 9.5S8.67 8 9.5 8m5 5c.83 0 1.5.67 1.5 1.5s-.67 1.5-1.5 1.5s-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5" data-v-43160719${_scopeId}></path></svg> Gift cards `);
          } else {
            return [
              (openBlock(), createBlock("svg", {
                xmlns: "http://www.w3.org/2000/svg",
                width: "22",
                height: "22",
                viewBox: "0 0 24 24",
                class: { "text-purple-700": selectedTab.value === "giftcards" }
              }, [
                createVNode("path", {
                  fill: "currentColor",
                  d: "M14.8 8L16 9.2L9.2 16L8 14.8zM4 4h16c1.11 0 2 .89 2 2v4a2 2 0 1 0 0 4v4c0 1.11-.89 2-2 2H4a2 2 0 0 1-2-2v-4c1.11 0 2-.89 2-2a2 2 0 0 0-2-2V6a2 2 0 0 1 2-2m0 2v2.54a3.994 3.994 0 0 1 0 6.92V18h16v-2.54a3.994 3.994 0 0 1 0-6.92V6zm5.5 2c.83 0 1.5.67 1.5 1.5S10.33 11 9.5 11S8 10.33 8 9.5S8.67 8 9.5 8m5 5c.83 0 1.5.67 1.5 1.5s-.67 1.5-1.5 1.5s-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5"
                })
              ], 2)),
              createTextVNode(" Gift cards ")
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(ssrRenderComponent(_component_NuxtLink, {
        to: { path: "/profile", query: { tab: "memberships" } },
        class: ["relative flex items-center gap-3 px-4 py-3 rounded-lg text-gray-900 hover:bg-purple-50 transition-colors sidebar-text", { "bg-purple-100 text-purple-700": selectedTab.value === "memberships" }]
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`<svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" class="${ssrRenderClass({ "text-purple-700": selectedTab.value === "memberships" })}" data-v-43160719${_scopeId}><path fill="currentColor" d="M6 20q-.425 0-.712-.288T5 19t.288-.712T6 18h12q.425 0 .713.288T19 19t-.288.713T18 20zm.7-3.5q-.725 0-1.287-.475t-.688-1.2l-1-6.35q-.05 0-.112.013T3.5 8.5q-.625 0-1.062-.437T2 7t.438-1.062T3.5 5.5t1.063.438T5 7q0 .175-.038.325t-.087.275L8 9l3.125-4.275q-.275-.2-.45-.525t-.175-.7q0-.625.438-1.063T12 2t1.063.438T13.5 3.5q0 .375-.175.7t-.45.525L16 9l3.125-1.4q-.05-.125-.088-.275T19 7q0-.625.438-1.063T20.5 5.5t1.063.438T22 7t-.437 1.063T20.5 8.5q-.05 0-.112-.012t-.113-.013l-1 6.35q-.125.725-.687 1.2T17.3 16.5zm0-2h10.6l.65-4.175l-1.15.5q-.65.275-1.325.1t-1.1-.75L12 6.9l-2.375 3.275q-.425.575-1.1.75t-1.325-.1l-1.15-.5zm5.3 0" data-v-43160719${_scopeId}></path></svg> Memberships `);
          } else {
            return [
              (openBlock(), createBlock("svg", {
                xmlns: "http://www.w3.org/2000/svg",
                width: "22",
                height: "22",
                viewBox: "0 0 24 24",
                class: { "text-purple-700": selectedTab.value === "memberships" }
              }, [
                createVNode("path", {
                  fill: "currentColor",
                  d: "M6 20q-.425 0-.712-.288T5 19t.288-.712T6 18h12q.425 0 .713.288T19 19t-.288.713T18 20zm.7-3.5q-.725 0-1.287-.475t-.688-1.2l-1-6.35q-.05 0-.112.013T3.5 8.5q-.625 0-1.062-.437T2 7t.438-1.062T3.5 5.5t1.063.438T5 7q0 .175-.038.325t-.087.275L8 9l3.125-4.275q-.275-.2-.45-.525t-.175-.7q0-.625.438-1.063T12 2t1.063.438T13.5 3.5q0 .375-.175.7t-.45.525L16 9l3.125-1.4q-.05-.125-.088-.275T19 7q0-.625.438-1.063T20.5 5.5t1.063.438T22 7t-.437 1.063T20.5 8.5q-.05 0-.112-.012t-.113-.013l-1 6.35q-.125.725-.687 1.2T17.3 16.5zm0-2h10.6l.65-4.175l-1.15.5q-.65.275-1.325.1t-1.1-.75L12 6.9l-2.375 3.275q-.425.575-1.1.75t-1.325-.1l-1.15-.5zm5.3 0"
                })
              ], 2)),
              createTextVNode(" Memberships ")
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(ssrRenderComponent(_component_NuxtLink, {
        to: { path: "/profile", query: { tab: "settings" } },
        class: ["relative flex items-center gap-3 px-4 py-3 rounded-lg text-gray-900 hover:bg-purple-50 transition-colors sidebar-text", { "bg-purple-100 text-purple-700": selectedTab.value === "settings" }]
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`<svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" class="${ssrRenderClass({ "text-purple-700": selectedTab.value === "settings" })}" data-v-43160719${_scopeId}><path fill="currentColor" d="M19.43 12.98c.04-.32.07-.64.07-.98s-.03-.66-.07-.98l2.11-1.65c.19-.15.24-.42.12-.64l-2-3.46a.5.5 0 0 0-.61-.22l-2.49 1c-.52-.4-1.08-.73-1.69-.98l-.38-2.65A.49.49 0 0 0 14 2h-4c-.25 0-.46.18-.49.42l-.38 2.65c-.61.25-1.17.59-1.69.98l-2.49-1a.6.6 0 0 0-.18-.03c-.17 0-.34.09-.43.25l-2 3.46c-.13.22-.07.49.12.64l2.11 1.65c-.04.32-.07.65-.07.98s.03.66.07.98l-2.11 1.65c-.19.15-.24.42-.12.64l2 3.46a.5.5 0 0 0 .61.22l2.49-1c.52.4 1.08.73 1.69.98l.38 2.65c.03.24.24.42.49.42h4c.25 0 .46-.18.49-.42l.38-2.65c.61-.25 1.17-.59 1.69-.98l2.49 1q.09.03.18.03c.17 0 .34-.09.43-.25l2-3.46c.12-.22.07-.49-.12-.64zm-1.98-1.71c.04.31.05.52.05.73s-.02.43-.05.73l-.14 1.13l.89.7l1.08.84l-.7 1.21l-1.27-.51l-1.04-.42l-.9.68c-.43.32-.84.56-1.25.73l-1.06.43l-.16 1.13l-.2 1.35h-1.4l-.19-1.35l-.16-1.13l-1.06-.43c-.43-.18-.83-.41-1.23-.71l-.91-.7l-1.06.43l-1.27.51l-.7-1.21l1.08-.84l.89-.7l-.14-1.13c-.03-.31-.05-.54-.05-.74s.02-.43.05-.73l.14-1.13l-.89-.7l-1.08-.84l.7-1.21l1.27.51l1.04.42l.9-.68c.43-.32.84-.56 1.25-.73l1.06-.43l.16-1.13l.2-1.35h1.39l.19 1.35l.16 1.13l1.06.43c.43.18.83.41 1.23.71l.91.7l1.06-.43l1.27-.51l.7 1.21l-1.07.85l-.89.7zM12 8c-2.21 0-4 1.79-4 4s1.79 4 4 4s4-1.79 4-4s-1.79-4-4-4m0 6c-1.1 0-2-.9-2-2s.9-2 2-2s2 .9 2 2s-.9 2-2 2" data-v-43160719${_scopeId}></path></svg> Settings `);
          } else {
            return [
              (openBlock(), createBlock("svg", {
                xmlns: "http://www.w3.org/2000/svg",
                width: "22",
                height: "22",
                viewBox: "0 0 24 24",
                class: { "text-purple-700": selectedTab.value === "settings" }
              }, [
                createVNode("path", {
                  fill: "currentColor",
                  d: "M19.43 12.98c.04-.32.07-.64.07-.98s-.03-.66-.07-.98l2.11-1.65c.19-.15.24-.42.12-.64l-2-3.46a.5.5 0 0 0-.61-.22l-2.49 1c-.52-.4-1.08-.73-1.69-.98l-.38-2.65A.49.49 0 0 0 14 2h-4c-.25 0-.46.18-.49.42l-.38 2.65c-.61.25-1.17.59-1.69.98l-2.49-1a.6.6 0 0 0-.18-.03c-.17 0-.34.09-.43.25l-2 3.46c-.13.22-.07.49.12.64l2.11 1.65c-.04.32-.07.65-.07.98s.03.66.07.98l-2.11 1.65c-.19.15-.24.42-.12.64l2 3.46a.5.5 0 0 0 .61.22l2.49-1c.52.4 1.08.73 1.69.98l.38 2.65c.03.24.24.42.49.42h4c.25 0 .46-.18.49-.42l.38-2.65c.61-.25 1.17-.59 1.69-.98l2.49 1q.09.03.18.03c.17 0 .34-.09.43-.25l2-3.46c.12-.22.07-.49-.12-.64zm-1.98-1.71c.04.31.05.52.05.73s-.02.43-.05.73l-.14 1.13l.89.7l1.08.84l-.7 1.21l-1.27-.51l-1.04-.42l-.9.68c-.43.32-.84.56-1.25.73l-1.06.43l-.16 1.13l-.2 1.35h-1.4l-.19-1.35l-.16-1.13l-1.06-.43c-.43-.18-.83-.41-1.23-.71l-.91-.7l-1.06.43l-1.27.51l-.7-1.21l1.08-.84l.89-.7l-.14-1.13c-.03-.31-.05-.54-.05-.74s.02-.43.05-.73l.14-1.13l-.89-.7l-1.08-.84l.7-1.21l1.27.51l1.04.42l.9-.68c.43-.32.84-.56 1.25-.73l1.06-.43l.16-1.13l.2-1.35h1.39l.19 1.35l.16 1.13l1.06.43c.43.18.83.41 1.23.71l.91.7l1.06-.43l1.27-.51l.7 1.21l-1.07.85l-.89.7zM12 8c-2.21 0-4 1.79-4 4s1.79 4 4 4s4-1.79 4-4s-1.79-4-4-4m0 6c-1.1 0-2-.9-2-2s.9-2 2-2s2 .9 2 2s-.9 2-2 2"
                })
              ], 2)),
              createTextVNode(" Settings ")
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`<button class="relative flex items-center gap-3 px-4 py-3 rounded-lg text-red-500 hover:bg-red-50 mt-2 transition-colors sidebar-text" data-v-43160719><svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" data-v-43160719><path fill="currentColor" d="m17 7l-1.41 1.41L18.17 11H8v2h10.17l-2.58 2.58L17 17l5-5zM4 5h8V3H4c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h8v-2H4z" data-v-43160719></path></svg> Log out </button></nav></div><div class="lg:w-3/4 bg-white lg:pl-5" data-v-43160719>`);
      ssrRenderVNode(_push, createVNode(resolveDynamicComponent(unref(tabComponents)[selectedTab.value]), null, null), _parent);
      _push(`</div></div></div></div>`);
    };
  }
});
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/profile/index.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const index = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-43160719"]]);

export { index as default };
//# sourceMappingURL=index-CPDptFex.mjs.map
