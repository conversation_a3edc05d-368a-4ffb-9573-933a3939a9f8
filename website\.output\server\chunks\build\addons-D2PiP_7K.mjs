import { defineComponent, ref, computed, mergeProps, unref, useSSRContext } from 'vue';
import { ssrRenderAttrs, ssrRenderComponent, ssrRenderList, ssrInterpolate, ssrRenderStyle, ssrRenderClass, ssrIncludeBooleanAttr, ssrRenderAttr } from 'vue/server-renderer';
import { u as useBookingStore } from './booking-D2Du41fq.mjs';
import { useRouter, useRoute } from 'vue-router';
import { u as useAppSeoMeta } from './useAppSeoMeta-HWukB6FN.mjs';
import { _ as _sfc_main$2 } from './BookingSummary-C7u1ob1e.mjs';
import { b as useUserStore, c as useRuntimeConfig, d as useState, L as Loader, S as Snackbar, e as _sfc_main$1 } from './server.mjs';
import 'pinia';
import '../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:url';
import '@iconify/utils';
import 'node:crypto';
import 'consola';
import 'node:path';
import 'better-sqlite3';
import 'ipx';
import 'cookie';
import '@iconify/vue';
import 'tailwindcss/colors';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import 'unhead/server';
import 'devalue';
import 'unhead/plugins';
import 'unhead/utils';

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "addons",
  __ssrInlineRender: true,
  setup(__props) {
    useAppSeoMeta({
      title: "Select Add-ons | Bookslotz"
    });
    const bookingStore = useBookingStore();
    const router = useRouter();
    const route = useRoute();
    const userStore = useUserStore();
    const { public: { BASE_URL } } = useRuntimeConfig();
    const isLoading = useState("globalLoader", () => false);
    const hasError = ref(false);
    const snackbarRef = ref(null);
    const bookingSummaryRef = ref(null);
    const salon = ref({
      name: "Loading...",
      location: "MG Road, Mumbai",
      image: "/images/saloon-small-image.png"
    });
    const salonSlug = computed(() => decodeURIComponent(route.query.slug || ""));
    const allAddons = ref([]);
    const cartAddonIds = ref([]);
    ref([]);
    const showAuthModal = ref(false);
    const authStep = ref("email");
    const authEmail = ref("");
    const authEmailError = ref("");
    const otpValue = ref("");
    const visualOtp = ref([]);
    const hiddenOtpInput = ref(null);
    const isOtpFocused = ref(false);
    const currentPage = ref(1);
    const totalPages = ref(1);
    const allSelectedItems = computed(() => {
      const items = [];
      bookingStore.selectedServices.forEach((service) => {
        items.push({ id: service.id.toString(), name: service.name, duration: service.duration, price: service.price, type: "service" });
      });
      bookingStore.selectedAddons.forEach((addon) => {
        items.push({ id: addon.id.toString(), name: addon.name, duration: addon.duration, price: addon.price, type: "addon" });
      });
      return items;
    });
    const totalAmount = computed(() => bookingStore.total);
    const canContinue = computed(() => {
      return bookingStore.selectedServices.length > 0 || cartAddonIds.value.length > 0;
    });
    function isAddonSelected(addonId) {
      const inCart = cartAddonIds.value.includes(addonId);
      const inStore = bookingStore.selectedAddons.some((addon) => addon.id === addonId);
      return inCart || inStore;
    }
    function removeItem(type, id) {
      if (type === "service") {
        bookingStore.removeService(Number(id));
        if (bookingStore.selectedProfessionals && bookingStore.selectedProfessionals[Number(id)]) {
          const updatedProfessionals = { ...bookingStore.selectedProfessionals };
          delete updatedProfessionals[Number(id)];
          bookingStore.setProfessionalSelection("per-service", updatedProfessionals);
        }
        removeAddonFromCart(id);
      } else if (type === "addon") {
        const updatedAddons = bookingStore.selectedAddons.filter((addon) => addon.id !== id);
        bookingStore.setSelectedAddons(updatedAddons);
        removeAddonFromCart(id);
      }
    }
    function handleContinue() {
      if (userStore.loggedIn) {
        router.push({ path: "/booking/cart", query: { slug: salonSlug.value } });
      } else {
        showAuthModal.value = true;
        if (hiddenOtpInput.value) hiddenOtpInput.value.focus();
      }
    }
    function formatPromoType(type) {
      return type.toLowerCase().split("_").map((word) => word.charAt(0).toUpperCase() + word.slice(1)).join(" ");
    }
    function paginationPages() {
      const pages = [];
      if (totalPages.value <= 7) {
        for (let i = 1; i <= totalPages.value; i++) pages.push(i);
      } else {
        if (currentPage.value <= 4) {
          pages.push(1, 2, 3, 4, 5, "...", totalPages.value);
        } else if (currentPage.value >= totalPages.value - 3) {
          pages.push(1, "...", totalPages.value - 4, totalPages.value - 3, totalPages.value - 2, totalPages.value - 1, totalPages.value);
        } else {
          pages.push(1, "...", currentPage.value - 1, currentPage.value, currentPage.value + 1, "...", totalPages.value);
        }
      }
      return pages;
    }
    return (_ctx, _push, _parent, _attrs) => {
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "min-h-screen bg-white relative" }, _attrs))}>`);
      if (unref(isLoading)) {
        _push(ssrRenderComponent(Loader, null, null, _parent));
      } else {
        _push(`<!---->`);
      }
      _push(ssrRenderComponent(Snackbar, {
        ref_key: "snackbarRef",
        ref: snackbarRef
      }, null, _parent));
      if (hasError.value) {
        _push(ssrRenderComponent(_sfc_main$1, { error: { statusCode: 500, message: "Something went wrong. Please try again later." } }, null, _parent));
      } else {
        _push(`<!---->`);
      }
      if (!hasError.value) {
        _push(`<div class="flex flex-col xl:flex-row max-w-8xl mx-auto px-4 sm:px-16 md:px-12 justify-between"><div class="flex-1 lg:flex-[0.6] px-6 lg:px-8 py-6 lg:py-8"><div class="flex items-center space-x-3 mb-8"><button class="w-10 h-10 rounded-full border border-gray-300 flex items-center justify-center group cursor-pointer"><svg xmlns="http://www.w3.org/2000/svg" class="w-7 h-7 text-purple-600 group-hover:text-purple-700 transition-colors" viewBox="0 0 24 24" fill="currentColor"><path d="m6.523 12.5l3.735 3.735q.146.146.153.344q.006.198-.153.363q-.166.166-.357.168t-.357-.162l-4.382-4.383q-.243-.242-.243-.565t.243-.566l4.382-4.382q.147-.146.347-.153q.201-.007.367.159q.16.165.162.353q.003.189-.162.354L6.523 11.5h12.38q.214 0 .358.143t.143.357t-.143.357t-.357.357z"></path></svg></button><h1 class="text-2xl font-semibold text-gray-900">Select Add-ons</h1></div><div class="mb-8"><h2 class="text-lg font-semibold text-gray-900 mb-6 text-center sm:text-left">Featured Add-ons</h2><div class="space-y-3 flex flex-col items-center sm:block">`);
        if (allAddons.value.length === 0) {
          _push(`<div class="text-center py-12 w-full"><div class="text-gray-400 mb-4"><svg class="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path></svg></div><p class="text-gray-500 text-lg">No Add-ons Available</p></div>`);
        } else {
          _push(`<!--[-->`);
          ssrRenderList(allAddons.value, (addon) => {
            _push(`<div class="flex flex-col sm:flex-row justify-between items-center p-4 rounded-lg bg-white hover:bg-gray-50 transition w-full sm:w-auto"><div class="flex-1 text-center sm:text-left"><h3 class="text-[18px] font-[500] dm-sanssemibold mb-1">${ssrInterpolate(addon.name)}</h3><div class="flex items-center gap-1 mb-2 justify-center sm:justify-start"><svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 text-black" viewBox="0 0 24 24"><path fill="currentColor" d="M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2M12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8s8 3.58 8 8s-3.58 8-8 8"></path><path fill="currentColor" d="M12.5 7H11v6l5.25 3.15l.75-1.23l-4.5-2.67z"></path></svg><span class="text-[16px] font-[500] text-gray-500">${ssrInterpolate(addon.duration)} mins</span></div><p class="text-[16px] font-[500] inter-semibold text-gray-900">`);
            if (addon.promotionApplied) {
              _push(`<!--[--><span class="line-through text-gray-400 mr-2 font-[500] inter-semibold">\u20B9${ssrInterpolate(addon.originalPrice)}</span><span class="text-green-600 font-[500] inter-semibold">\u20B9${ssrInterpolate(addon.finalPrice)}</span>`);
              if (addon.promotionApplied.type) {
                _push(`<span class="ml-2 text-xs font-semibold px-2 py-0.5 rounded-full" style="${ssrRenderStyle({ "background": "linear-gradient(90deg, #e0c3fc 0%, #8ec5fc 100%)", "color": "#5a189a" })}">${ssrInterpolate(formatPromoType(addon.promotionApplied.type))}</span>`);
              } else {
                _push(`<!---->`);
              }
              _push(`<!--]-->`);
            } else {
              _push(`<!--[--> \u20B9${ssrInterpolate(addon.price)}<!--]-->`);
            }
            _push(`</p></div><button class="${ssrRenderClass([isAddonSelected(addon._id) ? "bg-purple-600 text-white hover:bg-purple-700" : "bg-white border border-gray-300 text-gray-700 hover:bg-gray-50", "mt-4 sm:mt-0 px-10 py-2 font-semibold rounded-full transition-colors text-[16px] text-[600] cursor-pointer"])}">${ssrInterpolate(isAddonSelected(addon._id) ? "Added" : "Add")}</button></div>`);
          });
          _push(`<!--]-->`);
        }
        _push(`</div></div>`);
        if (totalPages.value > 1) {
          _push(`<div class="flex justify-center mt-8"><nav class="inline-flex rounded-md shadow-sm" aria-label="Pagination"><button${ssrIncludeBooleanAttr(currentPage.value === 1) ? " disabled" : ""} class="relative inline-flex items-center px-3 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed" aria-label="Previous"><svg class="h-4 w-4" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" d="M15 19l-7-7 7-7"></path></svg></button><!--[-->`);
          ssrRenderList(paginationPages(), (page) => {
            _push(`<!--[-->`);
            if (page !== "...") {
              _push(`<button class="${ssrRenderClass([
                "relative inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium",
                page === currentPage.value ? "z-10 bg-purple-600 text-white border-purple-600" : "bg-white text-gray-700 hover:bg-gray-50"
              ])}">${ssrInterpolate(page)}</button>`);
            } else {
              _push(`<span class="relative inline-flex items-center px-3 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-400 select-none">...</span>`);
            }
            _push(`<!--]-->`);
          });
          _push(`<!--]--><button${ssrIncludeBooleanAttr(currentPage.value === totalPages.value) ? " disabled" : ""} class="relative inline-flex items-center px-3 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed" aria-label="Next"><svg class="h-4 w-4" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" d="M9 5l7 7-7 7"></path></svg></button></nav></div>`);
        } else {
          _push(`<!---->`);
        }
        _push(`</div>`);
        _push(ssrRenderComponent(_sfc_main$2, {
          ref_key: "bookingSummaryRef",
          ref: bookingSummaryRef,
          salon: salon.value,
          "selected-services": allSelectedItems.value,
          total: totalAmount.value,
          "can-continue": canContinue.value,
          "show-professional-names": unref(bookingStore).professionalSelectionType === "per-service",
          "professional-names": unref(bookingStore).selectedProfessionals,
          "show-order-summary": false,
          onContinue: handleContinue,
          onRemoveService: removeItem
        }, null, _parent));
        if (showAuthModal.value) {
          _push(`<div class="fixed inset-0 bg-black/50 bg-opacity-50 flex items-center justify-center z-50"><div class="bg-white rounded-lg p-6 w-full max-w-md"><div class="flex justify-between items-center mb-4"><h2 class="text-xl font-semibold">${ssrInterpolate(authStep.value === "email" ? "Sign In" : "Verify OTP")}</h2><button class="text-gray-500 hover:text-gray-700"><svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg></button></div>`);
          if (authStep.value === "email") {
            _push(`<div class="space-y-4"><div><label class="block text-sm font-medium text-gray-700 mb-1">Email</label><input type="email"${ssrRenderAttr("value", authEmail.value)} autocomplete="email" placeholder="<EMAIL>" class="${ssrRenderClass([[authEmailError.value ? "border-red-500 focus:ring-red-500" : "border-gray-200 focus:ring-purple-600"], "w-full px-4 py-2 bg-gray-100 text-gray-900 border rounded-lg focus:outline-none focus:ring-2 transition"])}">`);
            if (authEmailError.value) {
              _push(`<div class="text-red-500 text-xs mt-1">${ssrInterpolate(authEmailError.value)}</div>`);
            } else {
              _push(`<!---->`);
            }
            _push(`</div><button${ssrIncludeBooleanAttr(unref(isLoading) || !!authEmailError.value || !authEmail.value.trim()) ? " disabled" : ""} class="w-full py-3 rounded-full bg-purple-600 text-white font-semibold text-base hover:bg-purple-700 transition duration-200 disabled:opacity-70 disabled:cursor-not-allowed"> Submit </button></div>`);
          } else {
            _push(`<!---->`);
          }
          if (authStep.value === "otp") {
            _push(`<div class="space-y-4"><p class="text-sm text-gray-600">Enter the OTP sent to <span class="font-semibold">${ssrInterpolate(authEmail.value)}</span></p><div class="flex justify-center gap-4 mb-6"><input type="text" maxlength="4" inputmode="numeric" pattern="[0-9]*" class="opacity-0 absolute"${ssrRenderAttr("value", otpValue.value)} autocomplete="one-time-code"><!--[-->`);
            ssrRenderList(4, (digit, index) => {
              _push(`<div class="${ssrRenderClass([[visualOtp.value[index] ? "border-purple-600" : "border-gray-400", index === visualOtp.value.length && isOtpFocused.value ? "border-purple-600" : ""], "w-10 h-10 flex items-center justify-center border-b-2 text-center text-xl cursor-pointer"])}">${ssrInterpolate(visualOtp.value[index] || "")}</div>`);
            });
            _push(`<!--]--></div><button${ssrIncludeBooleanAttr(unref(isLoading) || otpValue.value.length !== 4) ? " disabled" : ""} class="w-full py-3 rounded-full bg-purple-600 text-white font-semibold text-base hover:bg-purple-700 transition duration-200 disabled:opacity-70 disabled:cursor-not-allowed"> Verify </button><div class="text-center mt-2"><p class="text-sm text-gray-600"> Didn&#39;t receive the OTP? <button class="font-semibold text-purple-600 hover:underline ml-1 cursor-pointer"> Resend OTP </button></p></div></div>`);
          } else {
            _push(`<!---->`);
          }
          _push(`</div></div>`);
        } else {
          _push(`<!---->`);
        }
        _push(`</div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div>`);
    };
  }
});
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/booking/addons.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};

export { _sfc_main as default };
//# sourceMappingURL=addons-D2PiP_7K.mjs.map
