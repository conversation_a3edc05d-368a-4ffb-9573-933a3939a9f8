import { defineComponent, ref, computed, mergeProps, useSSRContext } from 'vue';
import { ssrRenderAttrs, ssrInterpolate, ssrRenderList, ssrRenderClass } from 'vue/server-renderer';
import { _ as _export_sfc, g as useHead } from './server.mjs';
import '../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:url';
import '@iconify/utils';
import 'node:crypto';
import 'consola';
import 'node:path';
import 'better-sqlite3';
import 'ipx';
import 'pinia';
import 'vue-router';
import 'cookie';
import '@iconify/vue';
import 'tailwindcss/colors';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import 'unhead/server';
import 'devalue';
import 'unhead/plugins';
import 'unhead/utils';

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "notifications",
  __ssrInlineRender: true,
  setup(__props) {
    const notifications2 = ref([
      {
        id: 1,
        title: "Appointment Confirmed",
        message: "Your appointment with Elite Beauty Saloon is confirmed for 20th March 2024 at 4:30 pm See you soon!",
        time: "23 min",
        isRead: false
      },
      {
        id: 2,
        title: "Payment Successful",
        message: "Your payment of \u20B91,500 for haircut and styling has been processed successfully.",
        time: "1 hour",
        isRead: false
      },
      {
        id: 3,
        title: "Service Reminder",
        message: "Don't forget your appointment tomorrow at 2:00 PM with Glamour Studio.",
        time: "2 hours",
        isRead: true
      },
      {
        id: 4,
        title: "New Offer Available",
        message: "Get 20% off on your next spa session. Offer valid until end of this month.",
        time: "1 day",
        isRead: false
      },
      {
        id: 5,
        title: "Appointment Rescheduled",
        message: "Your appointment has been rescheduled to 25th March 2024 at 3:00 PM.",
        time: "2 days",
        isRead: true
      },
      {
        id: 6,
        title: "Review Request",
        message: "How was your experience with Beautiful You Salon? Please leave a review.",
        time: "3 days",
        isRead: false
      }
    ]);
    const hasMore = ref(false);
    computed(() => {
      return notifications2.value.filter((n) => !n.isRead).length;
    });
    useHead({
      title: "Notifications",
      meta: [
        {
          name: "description",
          content: "View all your notifications and stay updated with your latest activities."
        }
      ]
    });
    return (_ctx, _push, _parent, _attrs) => {
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "min-h-screen bg-gray-50" }, _attrs))} data-v-87b55d03><div class="h-1" data-v-87b55d03></div><div class="px-6 sm:px-8 md:px-16 lg:px-20 xl:px-20 2xl:px-20 py-8" data-v-87b55d03><div class="mb-8" data-v-87b55d03><div class="flex items-center justify-between" data-v-87b55d03><div data-v-87b55d03><h1 class="text-2xl sm:text-3xl font-bold text-gray-900" data-v-87b55d03>Notifications</h1><p class="text-gray-600 mt-1" data-v-87b55d03>Stay updated with your latest activities</p></div></div></div><div class="bg-white rounded-2xl shadow-sm border border-gray-100" data-v-87b55d03><div class="px-6 py-4 border-b border-gray-100" data-v-87b55d03><div class="flex items-center justify-between" data-v-87b55d03><h2 class="text-lg font-semibold text-gray-900" data-v-87b55d03>All Notifications</h2><div class="flex items-center gap-4" data-v-87b55d03><span class="text-sm text-gray-500" data-v-87b55d03>${ssrInterpolate(notifications2.value.length)} total</span></div></div></div><div class="divide-y divide-gray-50" data-v-87b55d03><!--[-->`);
      ssrRenderList(notifications2.value, (notification) => {
        _push(`<div class="${ssrRenderClass([{ "bg-purple-50/30": !notification.isRead }, "px-6 py-6 hover:bg-gray-50 cursor-pointer transition-colors"])}" data-v-87b55d03><div class="flex items-start gap-4" data-v-87b55d03><div class="flex-shrink-0 w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center" data-v-87b55d03><svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24" data-v-87b55d03><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7" data-v-87b55d03></path></svg></div><div class="flex-1 min-w-0" data-v-87b55d03><div class="flex items-start justify-between mb-2" data-v-87b55d03><h3 class="text-base font-semibold text-gray-900 leading-tight" data-v-87b55d03>${ssrInterpolate(notification.title)}</h3><div class="flex items-center gap-2 ml-4" data-v-87b55d03><span class="text-sm text-gray-500 flex-shrink-0" data-v-87b55d03>${ssrInterpolate(notification.time)}</span>`);
        if (!notification.isRead) {
          _push(`<div class="w-2 h-2 bg-purple-600 rounded-full" data-v-87b55d03></div>`);
        } else {
          _push(`<!---->`);
        }
        _push(`</div></div><p class="text-sm text-gray-600 leading-relaxed mb-4" data-v-87b55d03>${ssrInterpolate(notification.message)}</p><div class="flex items-center gap-3" data-v-87b55d03><button class="text-purple-600 text-sm font-medium hover:text-purple-700 bg-purple-50 hover:bg-purple-100 px-3 py-1 rounded-md transition-colors" data-v-87b55d03> View </button></div></div></div></div>`);
      });
      _push(`<!--]--></div>`);
      if (notifications2.value.length === 0) {
        _push(`<div class="px-6 py-12 text-center" data-v-87b55d03><div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4" data-v-87b55d03><svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24" data-v-87b55d03><path stroke-linecap="round" stroke-linejoin="round" d="M15 17h5l-5 5v-5zm-7-10h5l-5-5v5z" data-v-87b55d03></path></svg></div><h3 class="text-lg font-medium text-gray-900 mb-2" data-v-87b55d03>No notifications yet</h3><p class="text-gray-500" data-v-87b55d03>We&#39;ll notify you when something important happens.</p></div>`);
      } else {
        _push(`<!---->`);
      }
      if (notifications2.value.length > 0 && hasMore.value) {
        _push(`<div class="px-6 py-4 border-t border-gray-100 text-center" data-v-87b55d03><button class="text-purple-600 text-sm font-medium hover:text-purple-700 px-4 py-2 rounded-lg hover:bg-purple-50 transition-colors" data-v-87b55d03> Load More Notifications </button></div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div></div></div>`);
    };
  }
});
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/notifications.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const notifications = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-87b55d03"]]);

export { notifications as default };
//# sourceMappingURL=notifications-jeega-an.mjs.map
