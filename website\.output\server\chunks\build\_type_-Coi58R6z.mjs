import { defineComponent, ref, computed, watch, mergeProps, unref, useSSRContext } from 'vue';
import { ssrRenderAttrs, ssrRenderComponent, ssrInterpolate, ssrRenderList, ssrRenderAttr, ssrRenderClass, ssrIncludeBooleanAttr } from 'vue/server-renderer';
import { useRoute, useRouter } from 'vue-router';
import { _ as _export_sfc, b as useUserStore, c as useRuntimeConfig, d as useState, L as Loader, S as Snackbar, e as _sfc_main$1, i as useCookie } from './server.mjs';
import { u as useAppSeoMeta } from './useAppSeoMeta-HWukB6FN.mjs';
import '../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:url';
import '@iconify/utils';
import 'node:crypto';
import 'consola';
import 'node:path';
import 'better-sqlite3';
import 'ipx';
import 'pinia';
import 'cookie';
import '@iconify/vue';
import 'tailwindcss/colors';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import 'unhead/server';
import 'devalue';
import 'unhead/plugins';
import 'unhead/utils';

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "[type]",
  __ssrInlineRender: true,
  setup(__props) {
    const route = useRoute();
    useRouter();
    const userStore = useUserStore();
    const { public: { BASE_URL } } = useRuntimeConfig();
    const isLoading = useState("globalLoader", () => false);
    const hasError = ref(false);
    const snackbarRef = ref(null);
    const loadingMore = ref(false);
    const sectionType = computed(() => {
      const type = route.params.type || route.query.type;
      const validTypes = ["nearby", "trending", "recommended"];
      return validTypes.includes(type) ? type : "recommended";
    });
    const currentPage = ref(1);
    const itemsPerPage = ref(8);
    const totalPages = ref(0);
    const filteredSalons = ref([]);
    const noDataMessage = ref("");
    const isComponentMounted = ref(false);
    const sectionTitle = computed(() => {
      const titles = {
        "nearby": "Nearby Salons",
        "trending": "Trending Salons",
        "recommended": "Recommended Salons"
      };
      return titles[sectionType.value] || "Salons";
    });
    const getUserLocationFromCookie = () => {
      try {
        const locationCookie = useCookie("user_location");
        if (!locationCookie.value) {
          console.warn("user_location cookie is not set");
          return null;
        }
        let locationData;
        if (typeof locationCookie.value === "object" && locationCookie.value !== null) {
          locationData = locationCookie.value;
        } else {
          const decodedLocation = decodeURIComponent(locationCookie.value);
          locationData = JSON.parse(decodedLocation);
        }
        if (typeof locationData.lat !== "number" || typeof locationData.lng !== "number") {
          console.error("Invalid lat or lng in user_location:", locationData);
          return null;
        }
        return {
          latitude: locationData.lat,
          longitude: locationData.lng,
          city: locationData.city || ""
        };
      } catch (error) {
        console.error("Error processing user_location cookie:", error);
        return null;
      }
    };
    const resetData = () => {
      filteredSalons.value = [];
      noDataMessage.value = "";
      hasError.value = false;
      currentPage.value = 1;
      totalPages.value = 0;
    };
    const getImageUrl = (coverimage) => {
      if (!coverimage) return "/icon/saloon-image.jpg";
      if (coverimage.startsWith("http")) return coverimage;
      return `${BASE_URL}/wp/${coverimage}`;
    };
    const fetchSalonsByType = async (page = 1, append = false) => {
      var _a, _b, _c, _d, _e;
      const isFirstPage = page === 1;
      if (isFirstPage) {
        isLoading.value = true;
      } else {
        loadingMore.value = true;
      }
      hasError.value = false;
      noDataMessage.value = "";
      if (!append) {
        filteredSalons.value = [];
      }
      try {
        const queryParams = new URLSearchParams();
        queryParams.set(sectionType.value, "true");
        queryParams.set("page", page.toString());
        queryParams.set("limit", itemsPerPage.value.toString());
        if (sectionType.value === "nearby") {
          const userLocation = getUserLocationFromCookie();
          if (userLocation) {
            queryParams.set("latitude", userLocation.latitude.toString());
            queryParams.set("longitude", userLocation.longitude.toString());
          } else {
            (_a = snackbarRef.value) == null ? void 0 : _a.showSnackbar("Location not available. Unable to fetch nearby salons.", 3e3);
          }
        }
        const guestId = useCookie("guestId").value;
        const headers = {
          "Content-Type": "application/json"
        };
        if (userStore.token) headers["token"] = userStore.token;
        if (guestId) headers["guestId"] = guestId;
        const response = await fetch(`${BASE_URL}/v1/client/business/salon-list?${queryParams.toString()}`, {
          method: "GET",
          headers
        });
        const data = await response.json();
        if (data.status_code === 401) {
          (_b = snackbarRef.value) == null ? void 0 : _b.showSnackbar(data.msg || "Unauthorized. Logging out...");
          userStore.logout();
          return;
        }
        if (data.status && data.status_code === 200) {
          if (((_c = data.data) == null ? void 0 : _c.businessList) && Array.isArray(data.data.businessList)) {
            const newSalons = data.data.businessList.map((salon) => ({
              id: salon._id,
              name: salon.businessname || salon.salonname || "Unknown Salon",
              location: salon.salonAddress || salon.address || "Location not available",
              rating: 4.5,
              reviews: 127,
              image: getImageUrl(salon.coverimage),
              favorited: false,
              slug: salon.slug || salon._id
            }));
            filteredSalons.value = append ? [...filteredSalons.value, ...newSalons] : newSalons;
            totalPages.value = data.data.totalPage !== void 0 ? data.data.totalPage : 1;
            if (isFirstPage && filteredSalons.value.length > 0) {
              (_d = snackbarRef.value) == null ? void 0 : _d.showSnackbar("Salons loaded successfully");
            }
          } else {
            if (isFirstPage) {
              noDataMessage.value = data.msg || `No ${sectionType.value} salons found.`;
            }
            totalPages.value = 0;
          }
        } else if (data.status_code === 400) {
          if (isFirstPage) {
            noDataMessage.value = data.msg || `No ${sectionType.value} salons found.`;
          }
          totalPages.value = 0;
        } else {
          throw new _sfc_main$1(data.msg || `Failed to load ${sectionType.value} salons`);
        }
      } catch (error) {
        console.error(`Error fetching ${sectionType.value} salons:`, error);
        hasError.value = true;
        (_e = snackbarRef.value) == null ? void 0 : _e.showSnackbar(`Failed to load ${sectionType.value} salons. Please try again.`, 3e3);
      } finally {
        if (isFirstPage) {
          isLoading.value = false;
        } else {
          loadingMore.value = false;
        }
      }
    };
    useAppSeoMeta({
      title: computed(() => `${sectionTitle.value} | Bookslotz`),
      description: computed(() => `Discover ${sectionTitle.value.toLowerCase()} on Bookslotz. Book appointments, view ratings, and more.`),
      image: "/og-image.jpg",
      url: computed(() => `https://www.Bookslotz.com${route.fullPath}`)
    });
    watch(() => route.params.type, (newType, oldType) => {
      if (newType !== oldType && isComponentMounted.value) {
        resetData();
        fetchSalonsByType(1);
      }
    });
    return (_ctx, _push, _parent, _attrs) => {
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "min-h-screen bg-white relative" }, _attrs))} data-v-9e8aca4a>`);
      if (unref(isLoading)) {
        _push(ssrRenderComponent(Loader, null, null, _parent));
      } else {
        _push(`<!---->`);
      }
      _push(ssrRenderComponent(Snackbar, {
        ref_key: "snackbarRef",
        ref: snackbarRef
      }, null, _parent));
      if (hasError.value) {
        _push(ssrRenderComponent(_sfc_main$1, { error: { statusCode: 500, message: "Something went wrong. Please try again later." } }, null, _parent));
      } else {
        _push(`<!---->`);
      }
      if (!hasError.value) {
        _push(`<section class="py-8 bg-gray-50 min-h-screen" data-v-9e8aca4a><div class="max-w-8xl mx-auto px-6 sm:px-16 md:px-20 space-y-12" data-v-9e8aca4a><div data-v-9e8aca4a><h2 class="text-2xl font-bold text-gray-900 mb-6" data-v-9e8aca4a>${ssrInterpolate(sectionTitle.value)}</h2>`);
        if (filteredSalons.value.length) {
          _push(`<div data-v-9e8aca4a><div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-4 gap-4" data-v-9e8aca4a><!--[-->`);
          ssrRenderList(filteredSalons.value, (salon, i) => {
            _push(`<div class="bg-white rounded-2xl overflow-hidden shadow-sm hover:shadow-lg transition-all duration-300 cursor-pointer group" data-v-9e8aca4a><div class="relative aspect-[4/3] overflow-hidden" data-v-9e8aca4a><img${ssrRenderAttr("src", salon.image)}${ssrRenderAttr("alt", salon.name)} class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300" data-v-9e8aca4a><button class="absolute bottom-3 right-3 w-8 h-8 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center shadow-sm hover:bg-white transition-all duration-200" data-v-9e8aca4a>`);
            if (salon.favorited) {
              _push(`<svg class="w-4 h-4 text-red-500" fill="currentColor" viewBox="0 0 20 20" data-v-9e8aca4a><path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd" data-v-9e8aca4a></path></svg>`);
            } else {
              _push(`<svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-9e8aca4a><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" data-v-9e8aca4a></path></svg>`);
            }
            _push(`</button></div><div class="p-4 space-y-2" data-v-9e8aca4a><h3 class="font-semibold text-gray-900 text-base leading-tight line-clamp-1" data-v-9e8aca4a>${ssrInterpolate(salon.name)}</h3><div class="flex items-center text-gray-500 text-sm" data-v-9e8aca4a><svg class="w-4 h-4 mr-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-9e8aca4a><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" data-v-9e8aca4a></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" data-v-9e8aca4a></path></svg><span class="line-clamp-1" data-v-9e8aca4a>${ssrInterpolate(salon.location)}</span></div><div class="flex items-center space-x-1" data-v-9e8aca4a><svg class="w-4 h-4 text-yellow-400 fill-current" viewBox="0 0 20 20" data-v-9e8aca4a><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" data-v-9e8aca4a></path></svg><span class="font-semibold text-gray-900 text-sm" data-v-9e8aca4a>${ssrInterpolate(salon.rating)}</span><span class="text-gray-500 text-sm" data-v-9e8aca4a>(${ssrInterpolate(salon.reviews)})</span></div></div></div>`);
          });
          _push(`<!--]--></div>`);
          if (totalPages.value > 1) {
            _push(`<div class="flex justify-center items-center py-4 px-6 mt-4" data-v-9e8aca4a><div class="flex space-x-1" data-v-9e8aca4a><button${ssrIncludeBooleanAttr(currentPage.value === 1) ? " disabled" : ""} class="${ssrRenderClass([currentPage.value === 1 ? "text-gray-400 cursor-not-allowed" : "text-purple-600 hover:bg-purple-50", "px-3 py-1 rounded-md border cursor-pointer"])}" data-v-9e8aca4a><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" data-v-9e8aca4a><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" data-v-9e8aca4a></path></svg></button><!--[-->`);
            ssrRenderList(totalPages.value, (page) => {
              _push(`<!--[-->`);
              if (page === 1 || page === totalPages.value || page >= currentPage.value - 1 && page <= currentPage.value + 1) {
                _push(`<button class="${ssrRenderClass([page === currentPage.value ? "bg-purple-600 text-white" : "text-purple-600 hover:bg-purple-50", "px-3 py-1 rounded-md border cursor-pointer"])}" data-v-9e8aca4a>${ssrInterpolate(page)}</button>`);
              } else if (page === 2 && currentPage.value > 3 || page === totalPages.value - 1 && currentPage.value < totalPages.value - 2) {
                _push(`<span class="px-3 py-1" data-v-9e8aca4a> ... </span>`);
              } else {
                _push(`<!---->`);
              }
              _push(`<!--]-->`);
            });
            _push(`<!--]--><button${ssrIncludeBooleanAttr(currentPage.value === totalPages.value) ? " disabled" : ""} class="${ssrRenderClass([currentPage.value === totalPages.value ? "text-gray-400 cursor-not-allowed" : "text-purple-600 hover:bg-purple-50", "px-3 py-1 rounded-md border cursor-pointer"])}" data-v-9e8aca4a><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" data-v-9e8aca4a><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" data-v-9e8aca4a></path></svg></button></div></div>`);
          } else {
            _push(`<!---->`);
          }
          if (loadingMore.value) {
            _push(`<div class="flex justify-center py-8" data-v-9e8aca4a><div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600" data-v-9e8aca4a></div></div>`);
          } else {
            _push(`<!---->`);
          }
          _push(`</div>`);
        } else {
          _push(`<div class="text-center py-12" data-v-9e8aca4a><div class="text-gray-400 mb-4" data-v-9e8aca4a><svg class="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-9e8aca4a><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" data-v-9e8aca4a></path></svg></div><p class="text-gray-500 text-lg text-center" data-v-9e8aca4a>${ssrInterpolate(noDataMessage.value || `No ${sectionType.value} salons found.`)}</p></div>`);
        }
        _push(`</div></div></section>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div>`);
    };
  }
});
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/list-salons/[type].vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const _type_ = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-9e8aca4a"]]);

export { _type_ as default };
//# sourceMappingURL=_type_-Coi58R6z.mjs.map
