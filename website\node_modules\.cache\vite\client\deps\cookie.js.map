{"version": 3, "sources": ["../../../../cookie/src/index.ts"], "sourcesContent": ["/**\n * RegExp to match cookie-name in RFC 6265 sec 4.1.1\n * This refers out to the obsoleted definition of token in RFC 2616 sec 2.2\n * which has been replaced by the token definition in RFC 7230 appendix B.\n *\n * cookie-name       = token\n * token             = 1*tchar\n * tchar             = \"!\" / \"#\" / \"$\" / \"%\" / \"&\" / \"'\" /\n *                     \"*\" / \"+\" / \"-\" / \".\" / \"^\" / \"_\" /\n *                     \"`\" / \"|\" / \"~\" / DIGIT / ALPHA\n *\n * Note: Allowing more characters - https://github.com/jshttp/cookie/issues/191\n * Allow same range as cookie value, except `=`, which delimits end of name.\n */\nconst cookieNameRegExp = /^[\\u0021-\\u003A\\u003C\\u003E-\\u007E]+$/;\n\n/**\n * RegExp to match cookie-value in RFC 6265 sec 4.1.1\n *\n * cookie-value      = *cookie-octet / ( DQUOTE *cookie-octet DQUOTE )\n * cookie-octet      = %x21 / %x23-2B / %x2D-3A / %x3C-5B / %x5D-7E\n *                     ; US-ASCII characters excluding CTLs,\n *                     ; whitespace DQUOTE, comma, semicolon,\n *                     ; and backslash\n *\n * Allowing more characters: https://github.com/jshttp/cookie/issues/191\n * Comma, backslash, and DQUOTE are not part of the parsing algorithm.\n */\nconst cookieValueRegExp = /^[\\u0021-\\u003A\\u003C-\\u007E]*$/;\n\n/**\n * RegExp to match domain-value in RFC 6265 sec 4.1.1\n *\n * domain-value      = <subdomain>\n *                     ; defined in [RFC1034], Section 3.5, as\n *                     ; enhanced by [RFC1123], Section 2.1\n * <subdomain>       = <label> | <subdomain> \".\" <label>\n * <label>           = <let-dig> [ [ <ldh-str> ] <let-dig> ]\n *                     Labels must be 63 characters or less.\n *                     'let-dig' not 'letter' in the first char, per RFC1123\n * <ldh-str>         = <let-dig-hyp> | <let-dig-hyp> <ldh-str>\n * <let-dig-hyp>     = <let-dig> | \"-\"\n * <let-dig>         = <letter> | <digit>\n * <letter>          = any one of the 52 alphabetic characters A through Z in\n *                     upper case and a through z in lower case\n * <digit>           = any one of the ten digits 0 through 9\n *\n * Keep support for leading dot: https://github.com/jshttp/cookie/issues/173\n *\n * > (Note that a leading %x2E (\".\"), if present, is ignored even though that\n * character is not permitted, but a trailing %x2E (\".\"), if present, will\n * cause the user agent to ignore the attribute.)\n */\nconst domainValueRegExp =\n  /^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i;\n\n/**\n * RegExp to match path-value in RFC 6265 sec 4.1.1\n *\n * path-value        = <any CHAR except CTLs or \";\">\n * CHAR              = %x01-7F\n *                     ; defined in RFC 5234 appendix B.1\n */\nconst pathValueRegExp = /^[\\u0020-\\u003A\\u003D-\\u007E]*$/;\n\nconst __toString = Object.prototype.toString;\n\nconst NullObject = /* @__PURE__ */ (() => {\n  const C = function () {};\n  C.prototype = Object.create(null);\n  return C;\n})() as unknown as { new (): any };\n\n/**\n * Parse options.\n */\nexport interface ParseOptions {\n  /**\n   * Specifies a function that will be used to decode a [cookie-value](https://datatracker.ietf.org/doc/html/rfc6265#section-4.1.1).\n   * Since the value of a cookie has a limited character set (and must be a simple string), this function can be used to decode\n   * a previously-encoded cookie value into a JavaScript string.\n   *\n   * The default function is the global `decodeURIComponent`, wrapped in a `try..catch`. If an error\n   * is thrown it will return the cookie's original value. If you provide your own encode/decode\n   * scheme you must ensure errors are appropriately handled.\n   *\n   * @default decode\n   */\n  decode?: (str: string) => string | undefined;\n}\n\n/**\n * Parse a cookie header.\n *\n * Parse the given cookie header string into an object\n * The object has the various cookies as keys(names) => values\n */\nexport function parse(\n  str: string,\n  options?: ParseOptions,\n): Record<string, string | undefined> {\n  const obj: Record<string, string | undefined> = new NullObject();\n  const len = str.length;\n  // RFC 6265 sec 4.1.1, RFC 2616 2.2 defines a cookie name consists of one char minimum, plus '='.\n  if (len < 2) return obj;\n\n  const dec = options?.decode || decode;\n  let index = 0;\n\n  do {\n    const eqIdx = str.indexOf(\"=\", index);\n    if (eqIdx === -1) break; // No more cookie pairs.\n\n    const colonIdx = str.indexOf(\";\", index);\n    const endIdx = colonIdx === -1 ? len : colonIdx;\n\n    if (eqIdx > endIdx) {\n      // backtrack on prior semicolon\n      index = str.lastIndexOf(\";\", eqIdx - 1) + 1;\n      continue;\n    }\n\n    const keyStartIdx = startIndex(str, index, eqIdx);\n    const keyEndIdx = endIndex(str, eqIdx, keyStartIdx);\n    const key = str.slice(keyStartIdx, keyEndIdx);\n\n    // only assign once\n    if (obj[key] === undefined) {\n      let valStartIdx = startIndex(str, eqIdx + 1, endIdx);\n      let valEndIdx = endIndex(str, endIdx, valStartIdx);\n\n      const value = dec(str.slice(valStartIdx, valEndIdx));\n      obj[key] = value;\n    }\n\n    index = endIdx + 1;\n  } while (index < len);\n\n  return obj;\n}\n\nfunction startIndex(str: string, index: number, max: number) {\n  do {\n    const code = str.charCodeAt(index);\n    if (code !== 0x20 /*   */ && code !== 0x09 /* \\t */) return index;\n  } while (++index < max);\n  return max;\n}\n\nfunction endIndex(str: string, index: number, min: number) {\n  while (index > min) {\n    const code = str.charCodeAt(--index);\n    if (code !== 0x20 /*   */ && code !== 0x09 /* \\t */) return index + 1;\n  }\n  return min;\n}\n\n/**\n * Serialize options.\n */\nexport interface SerializeOptions {\n  /**\n   * Specifies a function that will be used to encode a [cookie-value](https://datatracker.ietf.org/doc/html/rfc6265#section-4.1.1).\n   * Since value of a cookie has a limited character set (and must be a simple string), this function can be used to encode\n   * a value into a string suited for a cookie's value, and should mirror `decode` when parsing.\n   *\n   * @default encodeURIComponent\n   */\n  encode?: (str: string) => string;\n  /**\n   * Specifies the `number` (in seconds) to be the value for the [`Max-Age` `Set-Cookie` attribute](https://tools.ietf.org/html/rfc6265#section-5.2.2).\n   *\n   * The [cookie storage model specification](https://tools.ietf.org/html/rfc6265#section-5.3) states that if both `expires` and\n   * `maxAge` are set, then `maxAge` takes precedence, but it is possible not all clients by obey this,\n   * so if both are set, they should point to the same date and time.\n   */\n  maxAge?: number;\n  /**\n   * Specifies the `Date` object to be the value for the [`Expires` `Set-Cookie` attribute](https://tools.ietf.org/html/rfc6265#section-5.2.1).\n   * When no expiration is set clients consider this a \"non-persistent cookie\" and delete it the current session is over.\n   *\n   * The [cookie storage model specification](https://tools.ietf.org/html/rfc6265#section-5.3) states that if both `expires` and\n   * `maxAge` are set, then `maxAge` takes precedence, but it is possible not all clients by obey this,\n   * so if both are set, they should point to the same date and time.\n   */\n  expires?: Date;\n  /**\n   * Specifies the value for the [`Domain` `Set-Cookie` attribute](https://tools.ietf.org/html/rfc6265#section-5.2.3).\n   * When no domain is set clients consider the cookie to apply to the current domain only.\n   */\n  domain?: string;\n  /**\n   * Specifies the value for the [`Path` `Set-Cookie` attribute](https://tools.ietf.org/html/rfc6265#section-5.2.4).\n   * When no path is set, the path is considered the [\"default path\"](https://tools.ietf.org/html/rfc6265#section-5.1.4).\n   */\n  path?: string;\n  /**\n   * Enables the [`HttpOnly` `Set-Cookie` attribute](https://tools.ietf.org/html/rfc6265#section-5.2.6).\n   * When enabled, clients will not allow client-side JavaScript to see the cookie in `document.cookie`.\n   */\n  httpOnly?: boolean;\n  /**\n   * Enables the [`Secure` `Set-Cookie` attribute](https://tools.ietf.org/html/rfc6265#section-5.2.5).\n   * When enabled, clients will only send the cookie back if the browser has a HTTPS connection.\n   */\n  secure?: boolean;\n  /**\n   * Enables the [`Partitioned` `Set-Cookie` attribute](https://tools.ietf.org/html/draft-cutler-httpbis-partitioned-cookies/).\n   * When enabled, clients will only send the cookie back when the current domain _and_ top-level domain matches.\n   *\n   * This is an attribute that has not yet been fully standardized, and may change in the future.\n   * This also means clients may ignore this attribute until they understand it. More information\n   * about can be found in [the proposal](https://github.com/privacycg/CHIPS).\n   */\n  partitioned?: boolean;\n  /**\n   * Specifies the value for the [`Priority` `Set-Cookie` attribute](https://tools.ietf.org/html/draft-west-cookie-priority-00#section-4.1).\n   *\n   * - `'low'` will set the `Priority` attribute to `Low`.\n   * - `'medium'` will set the `Priority` attribute to `Medium`, the default priority when not set.\n   * - `'high'` will set the `Priority` attribute to `High`.\n   *\n   * More information about priority levels can be found in [the specification](https://tools.ietf.org/html/draft-west-cookie-priority-00#section-4.1).\n   */\n  priority?: \"low\" | \"medium\" | \"high\";\n  /**\n   * Specifies the value for the [`SameSite` `Set-Cookie` attribute](https://tools.ietf.org/html/draft-ietf-httpbis-rfc6265bis-09#section-5.4.7).\n   *\n   * - `true` will set the `SameSite` attribute to `Strict` for strict same site enforcement.\n   * - `'lax'` will set the `SameSite` attribute to `Lax` for lax same site enforcement.\n   * - `'none'` will set the `SameSite` attribute to `None` for an explicit cross-site cookie.\n   * - `'strict'` will set the `SameSite` attribute to `Strict` for strict same site enforcement.\n   *\n   * More information about enforcement levels can be found in [the specification](https://tools.ietf.org/html/draft-ietf-httpbis-rfc6265bis-09#section-5.4.7).\n   */\n  sameSite?: boolean | \"lax\" | \"strict\" | \"none\";\n}\n\n/**\n * Serialize data into a cookie header.\n *\n * Serialize a name value pair into a cookie string suitable for\n * http headers. An optional options object specifies cookie parameters.\n *\n * serialize('foo', 'bar', { httpOnly: true })\n *   => \"foo=bar; httpOnly\"\n */\nexport function serialize(\n  name: string,\n  val: string,\n  options?: SerializeOptions,\n): string {\n  const enc = options?.encode || encodeURIComponent;\n\n  if (!cookieNameRegExp.test(name)) {\n    throw new TypeError(`argument name is invalid: ${name}`);\n  }\n\n  const value = enc(val);\n\n  if (!cookieValueRegExp.test(value)) {\n    throw new TypeError(`argument val is invalid: ${val}`);\n  }\n\n  let str = name + \"=\" + value;\n  if (!options) return str;\n\n  if (options.maxAge !== undefined) {\n    if (!Number.isInteger(options.maxAge)) {\n      throw new TypeError(`option maxAge is invalid: ${options.maxAge}`);\n    }\n\n    str += \"; Max-Age=\" + options.maxAge;\n  }\n\n  if (options.domain) {\n    if (!domainValueRegExp.test(options.domain)) {\n      throw new TypeError(`option domain is invalid: ${options.domain}`);\n    }\n\n    str += \"; Domain=\" + options.domain;\n  }\n\n  if (options.path) {\n    if (!pathValueRegExp.test(options.path)) {\n      throw new TypeError(`option path is invalid: ${options.path}`);\n    }\n\n    str += \"; Path=\" + options.path;\n  }\n\n  if (options.expires) {\n    if (\n      !isDate(options.expires) ||\n      !Number.isFinite(options.expires.valueOf())\n    ) {\n      throw new TypeError(`option expires is invalid: ${options.expires}`);\n    }\n\n    str += \"; Expires=\" + options.expires.toUTCString();\n  }\n\n  if (options.httpOnly) {\n    str += \"; HttpOnly\";\n  }\n\n  if (options.secure) {\n    str += \"; Secure\";\n  }\n\n  if (options.partitioned) {\n    str += \"; Partitioned\";\n  }\n\n  if (options.priority) {\n    const priority =\n      typeof options.priority === \"string\"\n        ? options.priority.toLowerCase()\n        : undefined;\n    switch (priority) {\n      case \"low\":\n        str += \"; Priority=Low\";\n        break;\n      case \"medium\":\n        str += \"; Priority=Medium\";\n        break;\n      case \"high\":\n        str += \"; Priority=High\";\n        break;\n      default:\n        throw new TypeError(`option priority is invalid: ${options.priority}`);\n    }\n  }\n\n  if (options.sameSite) {\n    const sameSite =\n      typeof options.sameSite === \"string\"\n        ? options.sameSite.toLowerCase()\n        : options.sameSite;\n    switch (sameSite) {\n      case true:\n      case \"strict\":\n        str += \"; SameSite=Strict\";\n        break;\n      case \"lax\":\n        str += \"; SameSite=Lax\";\n        break;\n      case \"none\":\n        str += \"; SameSite=None\";\n        break;\n      default:\n        throw new TypeError(`option sameSite is invalid: ${options.sameSite}`);\n    }\n  }\n\n  return str;\n}\n\n/**\n * URL-decode string value. Optimized to skip native call when no %.\n */\nfunction decode(str: string): string {\n  if (str.indexOf(\"%\") === -1) return str;\n\n  try {\n    return decodeURIComponent(str);\n  } catch (e) {\n    return str;\n  }\n}\n\n/**\n * Determine if value is a Date.\n */\nfunction isDate(val: any): val is Date {\n  return __toString.call(val) === \"[object Date]\";\n}\n"], "mappings": ";;;;;;;;AAiGA,YAAA,QAAA;AAsJA,YAAA,YAAA;AAzOA,QAAM,mBAAmB;AAczB,QAAM,oBAAoB;AAyB1B,QAAM,oBACJ;AASF,QAAM,kBAAkB;AAExB,QAAM,aAAa,OAAO,UAAU;AAEpC,QAAM,cAA8B,MAAK;AACvC,YAAM,IAAI,WAAA;MAAa;AACvB,QAAE,YAAY,uBAAO,OAAO,IAAI;AAChC,aAAO;IACT,GAAE;AA0BF,aAAgB,MACd,KACA,SAAsB;AAEtB,YAAM,MAA0C,IAAI,WAAU;AAC9D,YAAM,MAAM,IAAI;AAEhB,UAAI,MAAM;AAAG,eAAO;AAEpB,YAAM,MAAM,SAAS,UAAU;AAC/B,UAAI,QAAQ;AAEZ,SAAG;AACD,cAAM,QAAQ,IAAI,QAAQ,KAAK,KAAK;AACpC,YAAI,UAAU;AAAI;AAElB,cAAM,WAAW,IAAI,QAAQ,KAAK,KAAK;AACvC,cAAM,SAAS,aAAa,KAAK,MAAM;AAEvC,YAAI,QAAQ,QAAQ;AAElB,kBAAQ,IAAI,YAAY,KAAK,QAAQ,CAAC,IAAI;AAC1C;QACF;AAEA,cAAM,cAAc,WAAW,KAAK,OAAO,KAAK;AAChD,cAAM,YAAY,SAAS,KAAK,OAAO,WAAW;AAClD,cAAM,MAAM,IAAI,MAAM,aAAa,SAAS;AAG5C,YAAI,IAAI,GAAG,MAAM,QAAW;AAC1B,cAAI,cAAc,WAAW,KAAK,QAAQ,GAAG,MAAM;AACnD,cAAI,YAAY,SAAS,KAAK,QAAQ,WAAW;AAEjD,gBAAM,QAAQ,IAAI,IAAI,MAAM,aAAa,SAAS,CAAC;AACnD,cAAI,GAAG,IAAI;QACb;AAEA,gBAAQ,SAAS;MACnB,SAAS,QAAQ;AAEjB,aAAO;IACT;AAEA,aAAS,WAAW,KAAa,OAAe,KAAW;AACzD,SAAG;AACD,cAAM,OAAO,IAAI,WAAW,KAAK;AACjC,YAAI,SAAS,MAAgB,SAAS;AAAe,iBAAO;MAC9D,SAAS,EAAE,QAAQ;AACnB,aAAO;IACT;AAEA,aAAS,SAAS,KAAa,OAAe,KAAW;AACvD,aAAO,QAAQ,KAAK;AAClB,cAAM,OAAO,IAAI,WAAW,EAAE,KAAK;AACnC,YAAI,SAAS,MAAgB,SAAS;AAAe,iBAAO,QAAQ;MACtE;AACA,aAAO;IACT;AA4FA,aAAgB,UACd,MACA,KACA,SAA0B;AAE1B,YAAM,MAAM,SAAS,UAAU;AAE/B,UAAI,CAAC,iBAAiB,KAAK,IAAI,GAAG;AAChC,cAAM,IAAI,UAAU,6BAA6B,IAAI,EAAE;MACzD;AAEA,YAAM,QAAQ,IAAI,GAAG;AAErB,UAAI,CAAC,kBAAkB,KAAK,KAAK,GAAG;AAClC,cAAM,IAAI,UAAU,4BAA4B,GAAG,EAAE;MACvD;AAEA,UAAI,MAAM,OAAO,MAAM;AACvB,UAAI,CAAC;AAAS,eAAO;AAErB,UAAI,QAAQ,WAAW,QAAW;AAChC,YAAI,CAAC,OAAO,UAAU,QAAQ,MAAM,GAAG;AACrC,gBAAM,IAAI,UAAU,6BAA6B,QAAQ,MAAM,EAAE;QACnE;AAEA,eAAO,eAAe,QAAQ;MAChC;AAEA,UAAI,QAAQ,QAAQ;AAClB,YAAI,CAAC,kBAAkB,KAAK,QAAQ,MAAM,GAAG;AAC3C,gBAAM,IAAI,UAAU,6BAA6B,QAAQ,MAAM,EAAE;QACnE;AAEA,eAAO,cAAc,QAAQ;MAC/B;AAEA,UAAI,QAAQ,MAAM;AAChB,YAAI,CAAC,gBAAgB,KAAK,QAAQ,IAAI,GAAG;AACvC,gBAAM,IAAI,UAAU,2BAA2B,QAAQ,IAAI,EAAE;QAC/D;AAEA,eAAO,YAAY,QAAQ;MAC7B;AAEA,UAAI,QAAQ,SAAS;AACnB,YACE,CAAC,OAAO,QAAQ,OAAO,KACvB,CAAC,OAAO,SAAS,QAAQ,QAAQ,QAAO,CAAE,GAC1C;AACA,gBAAM,IAAI,UAAU,8BAA8B,QAAQ,OAAO,EAAE;QACrE;AAEA,eAAO,eAAe,QAAQ,QAAQ,YAAW;MACnD;AAEA,UAAI,QAAQ,UAAU;AACpB,eAAO;MACT;AAEA,UAAI,QAAQ,QAAQ;AAClB,eAAO;MACT;AAEA,UAAI,QAAQ,aAAa;AACvB,eAAO;MACT;AAEA,UAAI,QAAQ,UAAU;AACpB,cAAM,WACJ,OAAO,QAAQ,aAAa,WACxB,QAAQ,SAAS,YAAW,IAC5B;AACN,gBAAQ,UAAU;UAChB,KAAK;AACH,mBAAO;AACP;UACF,KAAK;AACH,mBAAO;AACP;UACF,KAAK;AACH,mBAAO;AACP;UACF;AACE,kBAAM,IAAI,UAAU,+BAA+B,QAAQ,QAAQ,EAAE;QACzE;MACF;AAEA,UAAI,QAAQ,UAAU;AACpB,cAAM,WACJ,OAAO,QAAQ,aAAa,WACxB,QAAQ,SAAS,YAAW,IAC5B,QAAQ;AACd,gBAAQ,UAAU;UAChB,KAAK;UACL,KAAK;AACH,mBAAO;AACP;UACF,KAAK;AACH,mBAAO;AACP;UACF,KAAK;AACH,mBAAO;AACP;UACF;AACE,kBAAM,IAAI,UAAU,+BAA+B,QAAQ,QAAQ,EAAE;QACzE;MACF;AAEA,aAAO;IACT;AAKA,aAAS,OAAO,KAAW;AACzB,UAAI,IAAI,QAAQ,GAAG,MAAM;AAAI,eAAO;AAEpC,UAAI;AACF,eAAO,mBAAmB,GAAG;MAC/B,SAAS,GAAG;AACV,eAAO;MACT;IACF;AAKA,aAAS,OAAO,KAAQ;AACtB,aAAO,WAAW,KAAK,GAAG,MAAM;IAClC;;;", "names": []}