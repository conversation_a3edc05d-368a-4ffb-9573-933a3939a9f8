{"version": 3, "sources": ["../../../../mdast-util-to-hast/lib/handlers/blockquote.js", "../../../../mdast-util-to-hast/lib/handlers/break.js", "../../../../mdast-util-to-hast/lib/handlers/code.js", "../../../../mdast-util-to-hast/lib/handlers/delete.js", "../../../../mdast-util-to-hast/lib/handlers/emphasis.js", "../../../../mdast-util-to-hast/lib/handlers/footnote-reference.js", "../../../../mdast-util-to-hast/lib/handlers/heading.js", "../../../../mdast-util-to-hast/lib/handlers/html.js", "../../../../mdast-util-to-hast/lib/revert.js", "../../../../mdast-util-to-hast/lib/handlers/image-reference.js", "../../../../mdast-util-to-hast/lib/handlers/image.js", "../../../../mdast-util-to-hast/lib/handlers/inline-code.js", "../../../../mdast-util-to-hast/lib/handlers/link-reference.js", "../../../../mdast-util-to-hast/lib/handlers/link.js", "../../../../mdast-util-to-hast/lib/handlers/list-item.js", "../../../../mdast-util-to-hast/lib/handlers/list.js", "../../../../mdast-util-to-hast/lib/handlers/paragraph.js", "../../../../mdast-util-to-hast/lib/handlers/root.js", "../../../../mdast-util-to-hast/lib/handlers/strong.js", "../../../../mdast-util-to-hast/lib/handlers/table.js", "../../../../mdast-util-to-hast/lib/handlers/table-row.js", "../../../../mdast-util-to-hast/lib/handlers/table-cell.js", "../../../../trim-lines/index.js", "../../../../mdast-util-to-hast/lib/handlers/text.js", "../../../../mdast-util-to-hast/lib/handlers/thematic-break.js", "../../../../mdast-util-to-hast/lib/handlers/index.js", "../../../../mdast-util-to-hast/lib/footer.js", "../../../../mdast-util-to-hast/lib/state.js", "../../../../mdast-util-to-hast/lib/index.js", "../../../../remark-rehype/lib/index.js"], "sourcesContent": ["/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Blockquote} Blockquote\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `blockquote` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Blockquote} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function blockquote(state, node) {\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'blockquote',\n    properties: {},\n    children: state.wrap(state.all(node), true)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Text} Text\n * @typedef {import('mdast').Break} Break\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `break` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Break} node\n *   mdast node.\n * @returns {Array<Element | Text>}\n *   hast element content.\n */\nexport function hardBreak(state, node) {\n  /** @type {Element} */\n  const result = {type: 'element', tagName: 'br', properties: {}, children: []}\n  state.patch(node, result)\n  return [state.applyData(node, result), {type: 'text', value: '\\n'}]\n}\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').Code} Code\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `code` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Code} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function code(state, node) {\n  const value = node.value ? node.value + '\\n' : ''\n  /** @type {Properties} */\n  const properties = {}\n\n  if (node.lang) {\n    properties.className = ['language-' + node.lang]\n  }\n\n  // Create `<code>`.\n  /** @type {Element} */\n  let result = {\n    type: 'element',\n    tagName: 'code',\n    properties,\n    children: [{type: 'text', value}]\n  }\n\n  if (node.meta) {\n    result.data = {meta: node.meta}\n  }\n\n  state.patch(node, result)\n  result = state.applyData(node, result)\n\n  // Create `<pre>`.\n  result = {type: 'element', tagName: 'pre', properties: {}, children: [result]}\n  state.patch(node, result)\n  return result\n}\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Delete} Delete\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `delete` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Delete} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function strikethrough(state, node) {\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'del',\n    properties: {},\n    children: state.all(node)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Emphasis} Emphasis\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `emphasis` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Emphasis} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function emphasis(state, node) {\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'em',\n    properties: {},\n    children: state.all(node)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').FootnoteReference} FootnoteReference\n * @typedef {import('../state.js').State} State\n */\n\nimport {normalizeUri} from 'micromark-util-sanitize-uri'\n\n/**\n * Turn an mdast `footnoteReference` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {FootnoteReference} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function footnoteReference(state, node) {\n  const clobberPrefix =\n    typeof state.options.clobberPrefix === 'string'\n      ? state.options.clobberPrefix\n      : 'user-content-'\n  const id = String(node.identifier).toUpperCase()\n  const safeId = normalizeUri(id.toLowerCase())\n  const index = state.footnoteOrder.indexOf(id)\n  /** @type {number} */\n  let counter\n\n  let reuseCounter = state.footnoteCounts.get(id)\n\n  if (reuseCounter === undefined) {\n    reuseCounter = 0\n    state.footnoteOrder.push(id)\n    counter = state.footnoteOrder.length\n  } else {\n    counter = index + 1\n  }\n\n  reuseCounter += 1\n  state.footnoteCounts.set(id, reuseCounter)\n\n  /** @type {Element} */\n  const link = {\n    type: 'element',\n    tagName: 'a',\n    properties: {\n      href: '#' + clobberPrefix + 'fn-' + safeId,\n      id:\n        clobberPrefix +\n        'fnref-' +\n        safeId +\n        (reuseCounter > 1 ? '-' + reuseCounter : ''),\n      dataFootnoteRef: true,\n      ariaDescribedBy: ['footnote-label']\n    },\n    children: [{type: 'text', value: String(counter)}]\n  }\n  state.patch(node, link)\n\n  /** @type {Element} */\n  const sup = {\n    type: 'element',\n    tagName: 'sup',\n    properties: {},\n    children: [link]\n  }\n  state.patch(node, sup)\n  return state.applyData(node, sup)\n}\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Heading} Heading\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `heading` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Heading} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function heading(state, node) {\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'h' + node.depth,\n    properties: {},\n    children: state.all(node)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Html} Html\n * @typedef {import('../state.js').State} State\n * @typedef {import('../../index.js').Raw} Raw\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `html` node into hast (`raw` node in dangerous mode, otherwise\n * nothing).\n *\n * @param {State} state\n *   Info passed around.\n * @param {Html} node\n *   mdast node.\n * @returns {Element | Raw | undefined}\n *   hast node.\n */\nexport function html(state, node) {\n  if (state.options.allowDangerousHtml) {\n    /** @type {Raw} */\n    const result = {type: 'raw', value: node.value}\n    state.patch(node, result)\n    return state.applyData(node, result)\n  }\n\n  return undefined\n}\n", "/**\n * @typedef {import('hast').ElementContent} ElementContent\n *\n * @typedef {import('mdast').Nodes} Nodes\n * @typedef {import('mdast').Reference} Reference\n *\n * @typedef {import('./state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Return the content of a reference without definition as plain text.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Extract<Nodes, Reference>} node\n *   Reference node (image, link).\n * @returns {Array<ElementContent>}\n *   hast content.\n */\nexport function revert(state, node) {\n  const subtype = node.referenceType\n  let suffix = ']'\n\n  if (subtype === 'collapsed') {\n    suffix += '[]'\n  } else if (subtype === 'full') {\n    suffix += '[' + (node.label || node.identifier) + ']'\n  }\n\n  if (node.type === 'imageReference') {\n    return [{type: 'text', value: '![' + node.alt + suffix}]\n  }\n\n  const contents = state.all(node)\n  const head = contents[0]\n\n  if (head && head.type === 'text') {\n    head.value = '[' + head.value\n  } else {\n    contents.unshift({type: 'text', value: '['})\n  }\n\n  const tail = contents[contents.length - 1]\n\n  if (tail && tail.type === 'text') {\n    tail.value += suffix\n  } else {\n    contents.push({type: 'text', value: suffix})\n  }\n\n  return contents\n}\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').ElementContent} ElementContent\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').ImageReference} ImageReference\n * @typedef {import('../state.js').State} State\n */\n\nimport {normalizeUri} from 'micromark-util-sanitize-uri'\nimport {revert} from '../revert.js'\n\n/**\n * Turn an mdast `imageReference` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {ImageReference} node\n *   mdast node.\n * @returns {Array<ElementContent> | ElementContent}\n *   hast node.\n */\nexport function imageReference(state, node) {\n  const id = String(node.identifier).toUpperCase()\n  const definition = state.definitionById.get(id)\n\n  if (!definition) {\n    return revert(state, node)\n  }\n\n  /** @type {Properties} */\n  const properties = {src: normalizeUri(definition.url || ''), alt: node.alt}\n\n  if (definition.title !== null && definition.title !== undefined) {\n    properties.title = definition.title\n  }\n\n  /** @type {Element} */\n  const result = {type: 'element', tagName: 'img', properties, children: []}\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').Image} Image\n * @typedef {import('../state.js').State} State\n */\n\nimport {normalizeUri} from 'micromark-util-sanitize-uri'\n\n/**\n * Turn an mdast `image` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Image} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function image(state, node) {\n  /** @type {Properties} */\n  const properties = {src: normalizeUri(node.url)}\n\n  if (node.alt !== null && node.alt !== undefined) {\n    properties.alt = node.alt\n  }\n\n  if (node.title !== null && node.title !== undefined) {\n    properties.title = node.title\n  }\n\n  /** @type {Element} */\n  const result = {type: 'element', tagName: 'img', properties, children: []}\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Text} Text\n * @typedef {import('mdast').InlineCode} InlineCode\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `inlineCode` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {InlineCode} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function inlineCode(state, node) {\n  /** @type {Text} */\n  const text = {type: 'text', value: node.value.replace(/\\r?\\n|\\r/g, ' ')}\n  state.patch(node, text)\n\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'code',\n    properties: {},\n    children: [text]\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').ElementContent} ElementContent\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').LinkReference} LinkReference\n * @typedef {import('../state.js').State} State\n */\n\nimport {normalizeUri} from 'micromark-util-sanitize-uri'\nimport {revert} from '../revert.js'\n\n/**\n * Turn an mdast `linkReference` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {LinkReference} node\n *   mdast node.\n * @returns {Array<ElementContent> | ElementContent}\n *   hast node.\n */\nexport function linkReference(state, node) {\n  const id = String(node.identifier).toUpperCase()\n  const definition = state.definitionById.get(id)\n\n  if (!definition) {\n    return revert(state, node)\n  }\n\n  /** @type {Properties} */\n  const properties = {href: normalizeUri(definition.url || '')}\n\n  if (definition.title !== null && definition.title !== undefined) {\n    properties.title = definition.title\n  }\n\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'a',\n    properties,\n    children: state.all(node)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').Link} Link\n * @typedef {import('../state.js').State} State\n */\n\nimport {normalizeUri} from 'micromark-util-sanitize-uri'\n\n/**\n * Turn an mdast `link` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Link} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function link(state, node) {\n  /** @type {Properties} */\n  const properties = {href: normalizeUri(node.url)}\n\n  if (node.title !== null && node.title !== undefined) {\n    properties.title = node.title\n  }\n\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'a',\n    properties,\n    children: state.all(node)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').ElementContent} ElementContent\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').ListItem} ListItem\n * @typedef {import('mdast').Parents} Parents\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `listItem` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {ListItem} node\n *   mdast node.\n * @param {Parents | undefined} parent\n *   Parent of `node`.\n * @returns {Element}\n *   hast node.\n */\nexport function listItem(state, node, parent) {\n  const results = state.all(node)\n  const loose = parent ? listLoose(parent) : listItemLoose(node)\n  /** @type {Properties} */\n  const properties = {}\n  /** @type {Array<ElementContent>} */\n  const children = []\n\n  if (typeof node.checked === 'boolean') {\n    const head = results[0]\n    /** @type {Element} */\n    let paragraph\n\n    if (head && head.type === 'element' && head.tagName === 'p') {\n      paragraph = head\n    } else {\n      paragraph = {type: 'element', tagName: 'p', properties: {}, children: []}\n      results.unshift(paragraph)\n    }\n\n    if (paragraph.children.length > 0) {\n      paragraph.children.unshift({type: 'text', value: ' '})\n    }\n\n    paragraph.children.unshift({\n      type: 'element',\n      tagName: 'input',\n      properties: {type: 'checkbox', checked: node.checked, disabled: true},\n      children: []\n    })\n\n    // According to github-markdown-css, this class hides bullet.\n    // See: <https://github.com/sindresorhus/github-markdown-css>.\n    properties.className = ['task-list-item']\n  }\n\n  let index = -1\n\n  while (++index < results.length) {\n    const child = results[index]\n\n    // Add eols before nodes, except if this is a loose, first paragraph.\n    if (\n      loose ||\n      index !== 0 ||\n      child.type !== 'element' ||\n      child.tagName !== 'p'\n    ) {\n      children.push({type: 'text', value: '\\n'})\n    }\n\n    if (child.type === 'element' && child.tagName === 'p' && !loose) {\n      children.push(...child.children)\n    } else {\n      children.push(child)\n    }\n  }\n\n  const tail = results[results.length - 1]\n\n  // Add a final eol.\n  if (tail && (loose || tail.type !== 'element' || tail.tagName !== 'p')) {\n    children.push({type: 'text', value: '\\n'})\n  }\n\n  /** @type {Element} */\n  const result = {type: 'element', tagName: 'li', properties, children}\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n\n/**\n * @param {Parents} node\n * @return {Boolean}\n */\nfunction listLoose(node) {\n  let loose = false\n  if (node.type === 'list') {\n    loose = node.spread || false\n    const children = node.children\n    let index = -1\n\n    while (!loose && ++index < children.length) {\n      loose = listItemLoose(children[index])\n    }\n  }\n\n  return loose\n}\n\n/**\n * @param {ListItem} node\n * @return {Boolean}\n */\nfunction listItemLoose(node) {\n  const spread = node.spread\n\n  return spread === null || spread === undefined\n    ? node.children.length > 1\n    : spread\n}\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').List} List\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `list` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {List} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function list(state, node) {\n  /** @type {Properties} */\n  const properties = {}\n  const results = state.all(node)\n  let index = -1\n\n  if (typeof node.start === 'number' && node.start !== 1) {\n    properties.start = node.start\n  }\n\n  // Like GitHub, add a class for custom styling.\n  while (++index < results.length) {\n    const child = results[index]\n\n    if (\n      child.type === 'element' &&\n      child.tagName === 'li' &&\n      child.properties &&\n      Array.isArray(child.properties.className) &&\n      child.properties.className.includes('task-list-item')\n    ) {\n      properties.className = ['contains-task-list']\n      break\n    }\n  }\n\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: node.ordered ? 'ol' : 'ul',\n    properties,\n    children: state.wrap(results, true)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Paragraph} Paragraph\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `paragraph` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Paragraph} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function paragraph(state, node) {\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'p',\n    properties: {},\n    children: state.all(node)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n", "/**\n * @typedef {import('hast').Parents} HastParents\n * @typedef {import('hast').Root} HastRoot\n * @typedef {import('mdast').Root} MdastRoot\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `root` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {MdastRoot} node\n *   mdast node.\n * @returns {HastParents}\n *   hast node.\n */\nexport function root(state, node) {\n  /** @type {HastRoot} */\n  const result = {type: 'root', children: state.wrap(state.all(node))}\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Strong} Strong\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `strong` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Strong} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function strong(state, node) {\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'strong',\n    properties: {},\n    children: state.all(node)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Table} Table\n * @typedef {import('../state.js').State} State\n */\n\nimport {pointEnd, pointStart} from 'unist-util-position'\n\n/**\n * Turn an mdast `table` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Table} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function table(state, node) {\n  const rows = state.all(node)\n  const firstRow = rows.shift()\n  /** @type {Array<Element>} */\n  const tableContent = []\n\n  if (firstRow) {\n    /** @type {Element} */\n    const head = {\n      type: 'element',\n      tagName: 'thead',\n      properties: {},\n      children: state.wrap([firstRow], true)\n    }\n    state.patch(node.children[0], head)\n    tableContent.push(head)\n  }\n\n  if (rows.length > 0) {\n    /** @type {Element} */\n    const body = {\n      type: 'element',\n      tagName: 'tbody',\n      properties: {},\n      children: state.wrap(rows, true)\n    }\n\n    const start = pointStart(node.children[1])\n    const end = pointEnd(node.children[node.children.length - 1])\n    if (start && end) body.position = {start, end}\n    tableContent.push(body)\n  }\n\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'table',\n    properties: {},\n    children: state.wrap(tableContent, true)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').ElementContent} ElementContent\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').Parents} Parents\n * @typedef {import('mdast').TableRow} TableRow\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `tableRow` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {TableRow} node\n *   mdast node.\n * @param {Parents | undefined} parent\n *   Parent of `node`.\n * @returns {Element}\n *   hast node.\n */\nexport function tableRow(state, node, parent) {\n  const siblings = parent ? parent.children : undefined\n  // Generate a body row when without parent.\n  const rowIndex = siblings ? siblings.indexOf(node) : 1\n  const tagName = rowIndex === 0 ? 'th' : 'td'\n  // To do: option to use `style`?\n  const align = parent && parent.type === 'table' ? parent.align : undefined\n  const length = align ? align.length : node.children.length\n  let cellIndex = -1\n  /** @type {Array<ElementContent>} */\n  const cells = []\n\n  while (++cellIndex < length) {\n    // Note: can also be undefined.\n    const cell = node.children[cellIndex]\n    /** @type {Properties} */\n    const properties = {}\n    const alignValue = align ? align[cellIndex] : undefined\n\n    if (alignValue) {\n      properties.align = alignValue\n    }\n\n    /** @type {Element} */\n    let result = {type: 'element', tagName, properties, children: []}\n\n    if (cell) {\n      result.children = state.all(cell)\n      state.patch(cell, result)\n      result = state.applyData(cell, result)\n    }\n\n    cells.push(result)\n  }\n\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'tr',\n    properties: {},\n    children: state.wrap(cells, true)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').TableCell} TableCell\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `tableCell` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {TableCell} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function tableCell(state, node) {\n  // Note: this function is normally not called: see `table-row` for how rows\n  // and their cells are compiled.\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'td', // Assume body cell.\n    properties: {},\n    children: state.all(node)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n", "const tab = 9 /* `\\t` */\nconst space = 32 /* ` ` */\n\n/**\n * Remove initial and final spaces and tabs at the line breaks in `value`.\n * Does not trim initial and final spaces and tabs of the value itself.\n *\n * @param {string} value\n *   Value to trim.\n * @returns {string}\n *   Trimmed value.\n */\nexport function trimLines(value) {\n  const source = String(value)\n  const search = /\\r?\\n|\\r/g\n  let match = search.exec(source)\n  let last = 0\n  /** @type {Array<string>} */\n  const lines = []\n\n  while (match) {\n    lines.push(\n      trimLine(source.slice(last, match.index), last > 0, true),\n      match[0]\n    )\n\n    last = match.index + match[0].length\n    match = search.exec(source)\n  }\n\n  lines.push(trimLine(source.slice(last), last > 0, false))\n\n  return lines.join('')\n}\n\n/**\n * @param {string} value\n *   Line to trim.\n * @param {boolean} start\n *   Whether to trim the start of the line.\n * @param {boolean} end\n *   Whether to trim the end of the line.\n * @returns {string}\n *   Trimmed line.\n */\nfunction trimLine(value, start, end) {\n  let startIndex = 0\n  let endIndex = value.length\n\n  if (start) {\n    let code = value.codePointAt(startIndex)\n\n    while (code === tab || code === space) {\n      startIndex++\n      code = value.codePointAt(startIndex)\n    }\n  }\n\n  if (end) {\n    let code = value.codePointAt(endIndex - 1)\n\n    while (code === tab || code === space) {\n      endIndex--\n      code = value.codePointAt(endIndex - 1)\n    }\n  }\n\n  return endIndex > startIndex ? value.slice(startIndex, endIndex) : ''\n}\n", "/**\n * @typedef {import('hast').Element} HastElement\n * @typedef {import('hast').Text} HastText\n * @typedef {import('mdast').Text} MdastText\n * @typedef {import('../state.js').State} State\n */\n\nimport {trimLines} from 'trim-lines'\n\n/**\n * Turn an mdast `text` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {MdastText} node\n *   mdast node.\n * @returns {HastElement | HastText}\n *   hast node.\n */\nexport function text(state, node) {\n  /** @type {HastText} */\n  const result = {type: 'text', value: trimLines(String(node.value))}\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').ThematicBreak} ThematicBreak\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `thematicBreak` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {ThematicBreak} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function thematicBreak(state, node) {\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'hr',\n    properties: {},\n    children: []\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n", "import {blockquote} from './blockquote.js'\nimport {hardBreak} from './break.js'\nimport {code} from './code.js'\nimport {strikethrough} from './delete.js'\nimport {emphasis} from './emphasis.js'\nimport {footnoteReference} from './footnote-reference.js'\nimport {heading} from './heading.js'\nimport {html} from './html.js'\nimport {imageReference} from './image-reference.js'\nimport {image} from './image.js'\nimport {inlineCode} from './inline-code.js'\nimport {linkReference} from './link-reference.js'\nimport {link} from './link.js'\nimport {listItem} from './list-item.js'\nimport {list} from './list.js'\nimport {paragraph} from './paragraph.js'\nimport {root} from './root.js'\nimport {strong} from './strong.js'\nimport {table} from './table.js'\nimport {tableRow} from './table-row.js'\nimport {tableCell} from './table-cell.js'\nimport {text} from './text.js'\nimport {thematicBreak} from './thematic-break.js'\n\n/**\n * Default handlers for nodes.\n *\n * @satisfies {import('../state.js').Handlers}\n */\nexport const handlers = {\n  blockquote,\n  break: hardBreak,\n  code,\n  delete: strikethrough,\n  emphasis,\n  footnoteReference,\n  heading,\n  html,\n  imageReference,\n  image,\n  inlineCode,\n  linkReference,\n  link,\n  listItem,\n  list,\n  paragraph,\n  // @ts-expect-error: root is different, but hard to type.\n  root,\n  strong,\n  table,\n  tableCell,\n  tableRow,\n  text,\n  thematicBreak,\n  toml: ignore,\n  yaml: ignore,\n  definition: ignore,\n  footnoteDefinition: ignore\n}\n\n// Return nothing for nodes that are ignored.\nfunction ignore() {\n  return undefined\n}\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').ElementContent} ElementContent\n *\n * @typedef {import('./state.js').State} State\n */\n\n/**\n * @callback FootnoteBackContentTemplate\n *   Generate content for the backreference dynamically.\n *\n *   For the following markdown:\n *\n *   ```markdown\n *   Alpha[^micromark], bravo[^micromark], and charlie[^remark].\n *\n *   [^remark]: things about remark\n *   [^micromark]: things about micromark\n *   ```\n *\n *   This function will be called with:\n *\n *   *  `0` and `0` for the backreference from `things about micromark` to\n *      `alpha`, as it is the first used definition, and the first call to it\n *   *  `0` and `1` for the backreference from `things about micromark` to\n *      `bravo`, as it is the first used definition, and the second call to it\n *   *  `1` and `0` for the backreference from `things about remark` to\n *      `charlie`, as it is the second used definition\n * @param {number} referenceIndex\n *   Index of the definition in the order that they are first referenced,\n *   0-indexed.\n * @param {number} rereferenceIndex\n *   Index of calls to the same definition, 0-indexed.\n * @returns {Array<ElementContent> | ElementContent | string}\n *   Content for the backreference when linking back from definitions to their\n *   reference.\n *\n * @callback FootnoteBackLabelTemplate\n *   Generate a back label dynamically.\n *\n *   For the following markdown:\n *\n *   ```markdown\n *   Alpha[^micromark], bravo[^micromark], and charlie[^remark].\n *\n *   [^remark]: things about remark\n *   [^micromark]: things about micromark\n *   ```\n *\n *   This function will be called with:\n *\n *   *  `0` and `0` for the backreference from `things about micromark` to\n *      `alpha`, as it is the first used definition, and the first call to it\n *   *  `0` and `1` for the backreference from `things about micromark` to\n *      `bravo`, as it is the first used definition, and the second call to it\n *   *  `1` and `0` for the backreference from `things about remark` to\n *      `charlie`, as it is the second used definition\n * @param {number} referenceIndex\n *   Index of the definition in the order that they are first referenced,\n *   0-indexed.\n * @param {number} rereferenceIndex\n *   Index of calls to the same definition, 0-indexed.\n * @returns {string}\n *   Back label to use when linking back from definitions to their reference.\n */\n\nimport structuredClone from '@ungap/structured-clone'\nimport {normalizeUri} from 'micromark-util-sanitize-uri'\n\n/**\n * Generate the default content that GitHub uses on backreferences.\n *\n * @param {number} _\n *   Index of the definition in the order that they are first referenced,\n *   0-indexed.\n * @param {number} rereferenceIndex\n *   Index of calls to the same definition, 0-indexed.\n * @returns {Array<ElementContent>}\n *   Content.\n */\nexport function defaultFootnoteBackContent(_, rereferenceIndex) {\n  /** @type {Array<ElementContent>} */\n  const result = [{type: 'text', value: '↩'}]\n\n  if (rereferenceIndex > 1) {\n    result.push({\n      type: 'element',\n      tagName: 'sup',\n      properties: {},\n      children: [{type: 'text', value: String(rereferenceIndex)}]\n    })\n  }\n\n  return result\n}\n\n/**\n * Generate the default label that GitHub uses on backreferences.\n *\n * @param {number} referenceIndex\n *   Index of the definition in the order that they are first referenced,\n *   0-indexed.\n * @param {number} rereferenceIndex\n *   Index of calls to the same definition, 0-indexed.\n * @returns {string}\n *   Label.\n */\nexport function defaultFootnoteBackLabel(referenceIndex, rereferenceIndex) {\n  return (\n    'Back to reference ' +\n    (referenceIndex + 1) +\n    (rereferenceIndex > 1 ? '-' + rereferenceIndex : '')\n  )\n}\n\n/**\n * Generate a hast footer for called footnote definitions.\n *\n * @param {State} state\n *   Info passed around.\n * @returns {Element | undefined}\n *   `section` element or `undefined`.\n */\n// eslint-disable-next-line complexity\nexport function footer(state) {\n  const clobberPrefix =\n    typeof state.options.clobberPrefix === 'string'\n      ? state.options.clobberPrefix\n      : 'user-content-'\n  const footnoteBackContent =\n    state.options.footnoteBackContent || defaultFootnoteBackContent\n  const footnoteBackLabel =\n    state.options.footnoteBackLabel || defaultFootnoteBackLabel\n  const footnoteLabel = state.options.footnoteLabel || 'Footnotes'\n  const footnoteLabelTagName = state.options.footnoteLabelTagName || 'h2'\n  const footnoteLabelProperties = state.options.footnoteLabelProperties || {\n    className: ['sr-only']\n  }\n  /** @type {Array<ElementContent>} */\n  const listItems = []\n  let referenceIndex = -1\n\n  while (++referenceIndex < state.footnoteOrder.length) {\n    const definition = state.footnoteById.get(\n      state.footnoteOrder[referenceIndex]\n    )\n\n    if (!definition) {\n      continue\n    }\n\n    const content = state.all(definition)\n    const id = String(definition.identifier).toUpperCase()\n    const safeId = normalizeUri(id.toLowerCase())\n    let rereferenceIndex = 0\n    /** @type {Array<ElementContent>} */\n    const backReferences = []\n    const counts = state.footnoteCounts.get(id)\n\n    // eslint-disable-next-line no-unmodified-loop-condition\n    while (counts !== undefined && ++rereferenceIndex <= counts) {\n      if (backReferences.length > 0) {\n        backReferences.push({type: 'text', value: ' '})\n      }\n\n      let children =\n        typeof footnoteBackContent === 'string'\n          ? footnoteBackContent\n          : footnoteBackContent(referenceIndex, rereferenceIndex)\n\n      if (typeof children === 'string') {\n        children = {type: 'text', value: children}\n      }\n\n      backReferences.push({\n        type: 'element',\n        tagName: 'a',\n        properties: {\n          href:\n            '#' +\n            clobberPrefix +\n            'fnref-' +\n            safeId +\n            (rereferenceIndex > 1 ? '-' + rereferenceIndex : ''),\n          dataFootnoteBackref: '',\n          ariaLabel:\n            typeof footnoteBackLabel === 'string'\n              ? footnoteBackLabel\n              : footnoteBackLabel(referenceIndex, rereferenceIndex),\n          className: ['data-footnote-backref']\n        },\n        children: Array.isArray(children) ? children : [children]\n      })\n    }\n\n    const tail = content[content.length - 1]\n\n    if (tail && tail.type === 'element' && tail.tagName === 'p') {\n      const tailTail = tail.children[tail.children.length - 1]\n      if (tailTail && tailTail.type === 'text') {\n        tailTail.value += ' '\n      } else {\n        tail.children.push({type: 'text', value: ' '})\n      }\n\n      tail.children.push(...backReferences)\n    } else {\n      content.push(...backReferences)\n    }\n\n    /** @type {Element} */\n    const listItem = {\n      type: 'element',\n      tagName: 'li',\n      properties: {id: clobberPrefix + 'fn-' + safeId},\n      children: state.wrap(content, true)\n    }\n\n    state.patch(definition, listItem)\n\n    listItems.push(listItem)\n  }\n\n  if (listItems.length === 0) {\n    return\n  }\n\n  return {\n    type: 'element',\n    tagName: 'section',\n    properties: {dataFootnotes: true, className: ['footnotes']},\n    children: [\n      {\n        type: 'element',\n        tagName: footnoteLabelTagName,\n        properties: {\n          ...structuredClone(footnoteLabelProperties),\n          id: 'footnote-label'\n        },\n        children: [{type: 'text', value: footnoteLabel}]\n      },\n      {type: 'text', value: '\\n'},\n      {\n        type: 'element',\n        tagName: 'ol',\n        properties: {},\n        children: state.wrap(listItems, true)\n      },\n      {type: 'text', value: '\\n'}\n    ]\n  }\n}\n", "/**\n * @typedef {import('hast').Element} HastElement\n * @typedef {import('hast').ElementContent} HastElementContent\n * @typedef {import('hast').Nodes} HastNodes\n * @typedef {import('hast').Properties} HastProperties\n * @typedef {import('hast').RootContent} HastRootContent\n * @typedef {import('hast').Text} HastText\n *\n * @typedef {import('mdast').Definition} MdastDefinition\n * @typedef {import('mdast').FootnoteDefinition} MdastFootnoteDefinition\n * @typedef {import('mdast').Nodes} MdastNodes\n * @typedef {import('mdast').Parents} MdastParents\n *\n * @typedef {import('vfile').VFile} VFile\n *\n * @typedef {import('./footer.js').FootnoteBackContentTemplate} FootnoteBackContentTemplate\n * @typedef {import('./footer.js').FootnoteBackLabelTemplate} FootnoteBackLabelTemplate\n */\n\n/**\n * @callback Handler\n *   Handle a node.\n * @param {State} state\n *   Info passed around.\n * @param {any} node\n *   mdast node to handle.\n * @param {MdastParents | undefined} parent\n *   Parent of `node`.\n * @returns {Array<HastElementContent> | HastElementContent | undefined}\n *   hast node.\n *\n * @typedef {Partial<Record<MdastNodes['type'], Handler>>} Handlers\n *   Handle nodes.\n *\n * @typedef Options\n *   Configuration (optional).\n * @property {boolean | null | undefined} [allowDangerousHtml=false]\n *   Whether to persist raw HTML in markdown in the hast tree (default:\n *   `false`).\n * @property {string | null | undefined} [clobberPrefix='user-content-']\n *   Prefix to use before the `id` property on footnotes to prevent them from\n *   *clobbering* (default: `'user-content-'`).\n *\n *   Pass `''` for trusted markdown and when you are careful with\n *   polyfilling.\n *   You could pass a different prefix.\n *\n *   DOM clobbering is this:\n *\n *   ```html\n *   <p id=\"x\"></p>\n *   <script>alert(x) // `x` now refers to the `p#x` DOM element</script>\n *   ```\n *\n *   The above example shows that elements are made available by browsers, by\n *   their ID, on the `window` object.\n *   This is a security risk because you might be expecting some other variable\n *   at that place.\n *   It can also break polyfills.\n *   Using a prefix solves these problems.\n * @property {VFile | null | undefined} [file]\n *   Corresponding virtual file representing the input document (optional).\n * @property {FootnoteBackContentTemplate | string | null | undefined} [footnoteBackContent]\n *   Content of the backreference back to references (default: `defaultFootnoteBackContent`).\n *\n *   The default value is:\n *\n *   ```js\n *   function defaultFootnoteBackContent(_, rereferenceIndex) {\n *     const result = [{type: 'text', value: '↩'}]\n *\n *     if (rereferenceIndex > 1) {\n *       result.push({\n *         type: 'element',\n *         tagName: 'sup',\n *         properties: {},\n *         children: [{type: 'text', value: String(rereferenceIndex)}]\n *       })\n *     }\n *\n *     return result\n *   }\n *   ```\n *\n *   This content is used in the `a` element of each backreference (the `↩`\n *   links).\n * @property {FootnoteBackLabelTemplate | string | null | undefined} [footnoteBackLabel]\n *   Label to describe the backreference back to references (default:\n *   `defaultFootnoteBackLabel`).\n *\n *   The default value is:\n *\n *   ```js\n *   function defaultFootnoteBackLabel(referenceIndex, rereferenceIndex) {\n *    return (\n *      'Back to reference ' +\n *      (referenceIndex + 1) +\n *      (rereferenceIndex > 1 ? '-' + rereferenceIndex : '')\n *    )\n *   }\n *   ```\n *\n *   Change it when the markdown is not in English.\n *\n *   This label is used in the `ariaLabel` property on each backreference\n *   (the `↩` links).\n *   It affects users of assistive technology.\n * @property {string | null | undefined} [footnoteLabel='Footnotes']\n *   Textual label to use for the footnotes section (default: `'Footnotes'`).\n *\n *   Change it when the markdown is not in English.\n *\n *   This label is typically hidden visually (assuming a `sr-only` CSS class\n *   is defined that does that) and so affects screen readers only.\n *   If you do have such a class, but want to show this section to everyone,\n *   pass different properties with the `footnoteLabelProperties` option.\n * @property {HastProperties | null | undefined} [footnoteLabelProperties={className: ['sr-only']}]\n *   Properties to use on the footnote label (default: `{className:\n *   ['sr-only']}`).\n *\n *   Change it to show the label and add other properties.\n *\n *   This label is typically hidden visually (assuming an `sr-only` CSS class\n *   is defined that does that) and so affects screen readers only.\n *   If you do have such a class, but want to show this section to everyone,\n *   pass an empty string.\n *   You can also add different properties.\n *\n *   > **Note**: `id: 'footnote-label'` is always added, because footnote\n *   > calls use it with `aria-describedby` to provide an accessible label.\n * @property {string | null | undefined} [footnoteLabelTagName='h2']\n *   HTML tag name to use for the footnote label element (default: `'h2'`).\n *\n *   Change it to match your document structure.\n *\n *   This label is typically hidden visually (assuming a `sr-only` CSS class\n *   is defined that does that) and so affects screen readers only.\n *   If you do have such a class, but want to show this section to everyone,\n *   pass different properties with the `footnoteLabelProperties` option.\n * @property {Handlers | null | undefined} [handlers]\n *   Extra handlers for nodes (optional).\n * @property {Array<MdastNodes['type']> | null | undefined} [passThrough]\n *   List of custom mdast node types to pass through (keep) in hast (note that\n *   the node itself is passed, but eventual children are transformed)\n *   (optional).\n * @property {Handler | null | undefined} [unknownHandler]\n *   Handler for all unknown nodes (optional).\n *\n * @typedef State\n *   Info passed around.\n * @property {(node: MdastNodes) => Array<HastElementContent>} all\n *   Transform the children of an mdast parent to hast.\n * @property {<Type extends HastNodes>(from: MdastNodes, to: Type) => HastElement | Type} applyData\n *   Honor the `data` of `from`, and generate an element instead of `node`.\n * @property {Map<string, MdastDefinition>} definitionById\n *   Definitions by their identifier.\n * @property {Map<string, MdastFootnoteDefinition>} footnoteById\n *   Footnote definitions by their identifier.\n * @property {Map<string, number>} footnoteCounts\n *   Counts for how often the same footnote was called.\n * @property {Array<string>} footnoteOrder\n *   Identifiers of order when footnote calls first appear in tree order.\n * @property {Handlers} handlers\n *   Applied handlers.\n * @property {(node: MdastNodes, parent: MdastParents | undefined) => Array<HastElementContent> | HastElementContent | undefined} one\n *   Transform an mdast node to hast.\n * @property {Options} options\n *   Configuration.\n * @property {(from: MdastNodes, node: HastNodes) => undefined} patch\n *   Copy a node’s positional info.\n * @property {<Type extends HastRootContent>(nodes: Array<Type>, loose?: boolean | undefined) => Array<HastText | Type>} wrap\n *   Wrap `nodes` with line endings between each node, adds initial/final line endings when `loose`.\n */\n\nimport structuredClone from '@ungap/structured-clone'\nimport {visit} from 'unist-util-visit'\nimport {position} from 'unist-util-position'\nimport {handlers as defaultHandlers} from './handlers/index.js'\n\nconst own = {}.hasOwnProperty\n\n/** @type {Options} */\nconst emptyOptions = {}\n\n/**\n * Create `state` from an mdast tree.\n *\n * @param {MdastNodes} tree\n *   mdast node to transform.\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {State}\n *   `state` function.\n */\nexport function createState(tree, options) {\n  const settings = options || emptyOptions\n  /** @type {Map<string, MdastDefinition>} */\n  const definitionById = new Map()\n  /** @type {Map<string, MdastFootnoteDefinition>} */\n  const footnoteById = new Map()\n  /** @type {Map<string, number>} */\n  const footnoteCounts = new Map()\n  /** @type {Handlers} */\n  // @ts-expect-error: the root handler returns a root.\n  // Hard to type.\n  const handlers = {...defaultHandlers, ...settings.handlers}\n\n  /** @type {State} */\n  const state = {\n    all,\n    applyData,\n    definitionById,\n    footnoteById,\n    footnoteCounts,\n    footnoteOrder: [],\n    handlers,\n    one,\n    options: settings,\n    patch,\n    wrap\n  }\n\n  visit(tree, function (node) {\n    if (node.type === 'definition' || node.type === 'footnoteDefinition') {\n      const map = node.type === 'definition' ? definitionById : footnoteById\n      const id = String(node.identifier).toUpperCase()\n\n      // Mimick CM behavior of link definitions.\n      // See: <https://github.com/syntax-tree/mdast-util-definitions/blob/9032189/lib/index.js#L20-L21>.\n      if (!map.has(id)) {\n        // @ts-expect-error: node type matches map.\n        map.set(id, node)\n      }\n    }\n  })\n\n  return state\n\n  /**\n   * Transform an mdast node into a hast node.\n   *\n   * @param {MdastNodes} node\n   *   mdast node.\n   * @param {MdastParents | undefined} [parent]\n   *   Parent of `node`.\n   * @returns {Array<HastElementContent> | HastElementContent | undefined}\n   *   Resulting hast node.\n   */\n  function one(node, parent) {\n    const type = node.type\n    const handle = state.handlers[type]\n\n    if (own.call(state.handlers, type) && handle) {\n      return handle(state, node, parent)\n    }\n\n    if (state.options.passThrough && state.options.passThrough.includes(type)) {\n      if ('children' in node) {\n        const {children, ...shallow} = node\n        const result = structuredClone(shallow)\n        // @ts-expect-error: TS doesn’t understand…\n        result.children = state.all(node)\n        // @ts-expect-error: TS doesn’t understand…\n        return result\n      }\n\n      // @ts-expect-error: it’s custom.\n      return structuredClone(node)\n    }\n\n    const unknown = state.options.unknownHandler || defaultUnknownHandler\n\n    return unknown(state, node, parent)\n  }\n\n  /**\n   * Transform the children of an mdast node into hast nodes.\n   *\n   * @param {MdastNodes} parent\n   *   mdast node to compile\n   * @returns {Array<HastElementContent>}\n   *   Resulting hast nodes.\n   */\n  function all(parent) {\n    /** @type {Array<HastElementContent>} */\n    const values = []\n\n    if ('children' in parent) {\n      const nodes = parent.children\n      let index = -1\n      while (++index < nodes.length) {\n        const result = state.one(nodes[index], parent)\n\n        // To do: see if we van clean this? Can we merge texts?\n        if (result) {\n          if (index && nodes[index - 1].type === 'break') {\n            if (!Array.isArray(result) && result.type === 'text') {\n              result.value = trimMarkdownSpaceStart(result.value)\n            }\n\n            if (!Array.isArray(result) && result.type === 'element') {\n              const head = result.children[0]\n\n              if (head && head.type === 'text') {\n                head.value = trimMarkdownSpaceStart(head.value)\n              }\n            }\n          }\n\n          if (Array.isArray(result)) {\n            values.push(...result)\n          } else {\n            values.push(result)\n          }\n        }\n      }\n    }\n\n    return values\n  }\n}\n\n/**\n * Copy a node’s positional info.\n *\n * @param {MdastNodes} from\n *   mdast node to copy from.\n * @param {HastNodes} to\n *   hast node to copy into.\n * @returns {undefined}\n *   Nothing.\n */\nfunction patch(from, to) {\n  if (from.position) to.position = position(from)\n}\n\n/**\n * Honor the `data` of `from` and maybe generate an element instead of `to`.\n *\n * @template {HastNodes} Type\n *   Node type.\n * @param {MdastNodes} from\n *   mdast node to use data from.\n * @param {Type} to\n *   hast node to change.\n * @returns {HastElement | Type}\n *   Nothing.\n */\nfunction applyData(from, to) {\n  /** @type {HastElement | Type} */\n  let result = to\n\n  // Handle `data.hName`, `data.hProperties, `data.hChildren`.\n  if (from && from.data) {\n    const hName = from.data.hName\n    const hChildren = from.data.hChildren\n    const hProperties = from.data.hProperties\n\n    if (typeof hName === 'string') {\n      // Transforming the node resulted in an element with a different name\n      // than wanted:\n      if (result.type === 'element') {\n        result.tagName = hName\n      }\n      // Transforming the node resulted in a non-element, which happens for\n      // raw, text, and root nodes (unless custom handlers are passed).\n      // The intent of `hName` is to create an element, but likely also to keep\n      // the content around (otherwise: pass `hChildren`).\n      else {\n        /** @type {Array<HastElementContent>} */\n        // @ts-expect-error: assume no doctypes in `root`.\n        const children = 'children' in result ? result.children : [result]\n        result = {type: 'element', tagName: hName, properties: {}, children}\n      }\n    }\n\n    if (result.type === 'element' && hProperties) {\n      Object.assign(result.properties, structuredClone(hProperties))\n    }\n\n    if (\n      'children' in result &&\n      result.children &&\n      hChildren !== null &&\n      hChildren !== undefined\n    ) {\n      result.children = hChildren\n    }\n  }\n\n  return result\n}\n\n/**\n * Transform an unknown node.\n *\n * @param {State} state\n *   Info passed around.\n * @param {MdastNodes} node\n *   Unknown mdast node.\n * @returns {HastElement | HastText}\n *   Resulting hast node.\n */\nfunction defaultUnknownHandler(state, node) {\n  const data = node.data || {}\n  /** @type {HastElement | HastText} */\n  const result =\n    'value' in node &&\n    !(own.call(data, 'hProperties') || own.call(data, 'hChildren'))\n      ? {type: 'text', value: node.value}\n      : {\n          type: 'element',\n          tagName: 'div',\n          properties: {},\n          children: state.all(node)\n        }\n\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n\n/**\n * Wrap `nodes` with line endings between each node.\n *\n * @template {HastRootContent} Type\n *   Node type.\n * @param {Array<Type>} nodes\n *   List of nodes to wrap.\n * @param {boolean | undefined} [loose=false]\n *   Whether to add line endings at start and end (default: `false`).\n * @returns {Array<HastText | Type>}\n *   Wrapped nodes.\n */\nexport function wrap(nodes, loose) {\n  /** @type {Array<HastText | Type>} */\n  const result = []\n  let index = -1\n\n  if (loose) {\n    result.push({type: 'text', value: '\\n'})\n  }\n\n  while (++index < nodes.length) {\n    if (index) result.push({type: 'text', value: '\\n'})\n    result.push(nodes[index])\n  }\n\n  if (loose && nodes.length > 0) {\n    result.push({type: 'text', value: '\\n'})\n  }\n\n  return result\n}\n\n/**\n * Trim spaces and tabs at the start of `value`.\n *\n * @param {string} value\n *   Value to trim.\n * @returns {string}\n *   Result.\n */\nfunction trimMarkdownSpaceStart(value) {\n  let index = 0\n  let code = value.charCodeAt(index)\n\n  while (code === 9 || code === 32) {\n    index++\n    code = value.charCodeAt(index)\n  }\n\n  return value.slice(index)\n}\n", "/**\n * @typedef {import('hast').Nodes} HastNodes\n * @typedef {import('mdast').Nodes} MdastNodes\n * @typedef {import('./state.js').Options} Options\n */\n\nimport {ok as assert} from 'devlop'\nimport {footer} from './footer.js'\nimport {createState} from './state.js'\n\n/**\n * Transform mdast to hast.\n *\n * ##### Notes\n *\n * ###### HTML\n *\n * Raw HTML is available in mdast as `html` nodes and can be embedded in hast\n * as semistandard `raw` nodes.\n * Most utilities ignore `raw` nodes but two notable ones don’t:\n *\n * *   `hast-util-to-html` also has an option `allowDangerousHtml` which will\n *     output the raw HTML.\n *     This is typically discouraged as noted by the option name but is useful\n *     if you completely trust authors\n * *   `hast-util-raw` can handle the raw embedded HTML strings by parsing them\n *     into standard hast nodes (`element`, `text`, etc).\n *     This is a heavy task as it needs a full HTML parser, but it is the only\n *     way to support untrusted content\n *\n * ###### Footnotes\n *\n * Many options supported here relate to footnotes.\n * Footnotes are not specified by CommonMark, which we follow by default.\n * They are supported by GitHub, so footnotes can be enabled in markdown with\n * `mdast-util-gfm`.\n *\n * The options `footnoteBackLabel` and `footnoteLabel` define natural language\n * that explains footnotes, which is hidden for sighted users but shown to\n * assistive technology.\n * When your page is not in English, you must define translated values.\n *\n * Back references use ARIA attributes, but the section label itself uses a\n * heading that is hidden with an `sr-only` class.\n * To show it to sighted users, define different attributes in\n * `footnoteLabelProperties`.\n *\n * ###### Clobbering\n *\n * Footnotes introduces a problem, as it links footnote calls to footnote\n * definitions on the page through `id` attributes generated from user content,\n * which results in DOM clobbering.\n *\n * DOM clobbering is this:\n *\n * ```html\n * <p id=x></p>\n * <script>alert(x) // `x` now refers to the DOM `p#x` element</script>\n * ```\n *\n * Elements by their ID are made available by browsers on the `window` object,\n * which is a security risk.\n * Using a prefix solves this problem.\n *\n * More information on how to handle clobbering and the prefix is explained in\n * Example: headings (DOM clobbering) in `rehype-sanitize`.\n *\n * ###### Unknown nodes\n *\n * Unknown nodes are nodes with a type that isn’t in `handlers` or `passThrough`.\n * The default behavior for unknown nodes is:\n *\n * *   when the node has a `value` (and doesn’t have `data.hName`,\n *     `data.hProperties`, or `data.hChildren`, see later), create a hast `text`\n *     node\n * *   otherwise, create a `<div>` element (which could be changed with\n *     `data.hName`), with its children mapped from mdast to hast as well\n *\n * This behavior can be changed by passing an `unknownHandler`.\n *\n * @param {MdastNodes} tree\n *   mdast tree.\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {HastNodes}\n *   hast tree.\n */\nexport function toHast(tree, options) {\n  const state = createState(tree, options)\n  const node = state.one(tree, undefined)\n  const foot = footer(state)\n  /** @type {HastNodes} */\n  const result = Array.isArray(node)\n    ? {type: 'root', children: node}\n    : node || {type: 'root', children: []}\n\n  if (foot) {\n    // If there’s a footer, there were definitions, meaning block\n    // content.\n    // So `result` is a parent node.\n    assert('children' in result)\n    result.children.push({type: 'text', value: '\\n'}, foot)\n  }\n\n  return result\n}\n", "/**\n * @import {Root as HastRoot} from 'hast'\n * @import {Root as MdastRoot} from 'mdast'\n * @import {Options as ToHastOptions} from 'mdast-util-to-hast'\n * @import {Processor} from 'unified'\n * @import {VFile} from 'vfile'\n */\n\n/**\n * @typedef {Omit<ToHastOptions, 'file'>} Options\n *\n * @callback TransformBridge\n *   Bridge-mode.\n *\n *   Runs the destination with the new hast tree.\n *   Discards result.\n * @param {MdastRoot} tree\n *   Tree.\n * @param {VFile} file\n *   File.\n * @returns {Promise<undefined>}\n *   Nothing.\n *\n * @callback TransformMutate\n *  Mutate-mode.\n *\n *  Further transformers run on the hast tree.\n * @param {MdastRoot} tree\n *   Tree.\n * @param {VFile} file\n *   File.\n * @returns {HastRoot}\n *   Tree (hast).\n */\n\nimport {toHast} from 'mdast-util-to-hast'\n\n/**\n * Turn markdown into HTML.\n *\n * ##### Notes\n *\n * ###### Signature\n *\n * * if a processor is given,\n *   runs the (rehype) plugins used on it with a hast tree,\n *   then discards the result (*bridge mode*)\n * * otherwise,\n *   returns a hast tree,\n *   the plugins used after `remarkRehype` are rehype plugins (*mutate mode*)\n *\n * > 👉 **Note**:\n * > It’s highly unlikely that you want to pass a `processor`.\n *\n * ###### HTML\n *\n * Raw HTML is available in mdast as `html` nodes and can be embedded in hast\n * as semistandard `raw` nodes.\n * Most plugins ignore `raw` nodes but two notable ones don’t:\n *\n * * `rehype-stringify` also has an option `allowDangerousHtml` which will\n *   output the raw HTML.\n *   This is typically discouraged as noted by the option name but is useful if\n *   you completely trust authors\n * * `rehype-raw` can handle the raw embedded HTML strings by parsing them\n *   into standard hast nodes (`element`, `text`, etc);\n *   this is a heavy task as it needs a full HTML parser,\n *   but it is the only way to support untrusted content\n *\n * ###### Footnotes\n *\n * Many options supported here relate to footnotes.\n * Footnotes are not specified by CommonMark,\n * which we follow by default.\n * They are supported by GitHub,\n * so footnotes can be enabled in markdown with `remark-gfm`.\n *\n * The options `footnoteBackLabel` and `footnoteLabel` define natural language\n * that explains footnotes,\n * which is hidden for sighted users but shown to assistive technology.\n * When your page is not in English,\n * you must define translated values.\n *\n * Back references use ARIA attributes,\n * but the section label itself uses a heading that is hidden with an\n * `sr-only` class.\n * To show it to sighted users,\n * define different attributes in `footnoteLabelProperties`.\n *\n * ###### Clobbering\n *\n * Footnotes introduces a problem,\n * as it links footnote calls to footnote definitions on the page through `id`\n * attributes generated from user content,\n * which results in DOM clobbering.\n *\n * DOM clobbering is this:\n *\n * ```html\n * <p id=x></p>\n * <script>alert(x) // `x` now refers to the DOM `p#x` element</script>\n * ```\n *\n * Elements by their ID are made available by browsers on the `window` object,\n * which is a security risk.\n * Using a prefix solves this problem.\n *\n * More information on how to handle clobbering and the prefix is explained in\n * *Example: headings (DOM clobbering)* in `rehype-sanitize`.\n *\n * ###### Unknown nodes\n *\n * Unknown nodes are nodes with a type that isn’t in `handlers` or `passThrough`.\n * The default behavior for unknown nodes is:\n *\n * * when the node has a `value`\n *   (and doesn’t have `data.hName`, `data.hProperties`, or `data.hChildren`,\n *   see later),\n *   create a hast `text` node\n * * otherwise,\n *   create a `<div>` element (which could be changed with `data.hName`),\n *   with its children mapped from mdast to hast as well\n *\n * This behavior can be changed by passing an `unknownHandler`.\n *\n * @overload\n * @param {Processor} processor\n * @param {Readonly<Options> | null | undefined} [options]\n * @returns {TransformBridge}\n *\n * @overload\n * @param {Readonly<Options> | null | undefined} [options]\n * @returns {TransformMutate}\n *\n * @overload\n * @param {Readonly<Options> | Processor | null | undefined} [destination]\n * @param {Readonly<Options> | null | undefined} [options]\n * @returns {TransformBridge | TransformMutate}\n *\n * @param {Readonly<Options> | Processor | null | undefined} [destination]\n *   Processor or configuration (optional).\n * @param {Readonly<Options> | null | undefined} [options]\n *   When a processor was given,\n *   configuration (optional).\n * @returns {TransformBridge | TransformMutate}\n *   Transform.\n */\nexport default function remarkRehype(destination, options) {\n  if (destination && 'run' in destination) {\n    /**\n     * @type {TransformBridge}\n     */\n    return async function (tree, file) {\n      // Cast because root in -> root out.\n      const hastTree = /** @type {HastRoot} */ (\n        toHast(tree, {file, ...options})\n      )\n      await destination.run(hastTree, file)\n    }\n  }\n\n  /**\n   * @type {TransformMutate}\n   */\n  return function (tree, file) {\n    // Cast because root in -> root out.\n    // To do: in the future, disallow ` || options` fallback.\n    // With `unified-engine`, `destination` can be `undefined` but\n    // `options` will be the file set.\n    // We should not pass that as `options`.\n    return /** @type {HastRoot} */ (\n      toHast(tree, {file, ...(destination || options)})\n    )\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAmBO,SAAS,WAAW,OAAO,MAAM;AAEtC,QAAM,SAAS;AAAA,IACb,MAAM;AAAA,IACN,SAAS;AAAA,IACT,YAAY,CAAC;AAAA,IACb,UAAU,MAAM,KAAK,MAAM,IAAI,IAAI,GAAG,IAAI;AAAA,EAC5C;AACA,QAAM,MAAM,MAAM,MAAM;AACxB,SAAO,MAAM,UAAU,MAAM,MAAM;AACrC;;;ACTO,SAAS,UAAU,OAAO,MAAM;AAErC,QAAM,SAAS,EAAC,MAAM,WAAW,SAAS,MAAM,YAAY,CAAC,GAAG,UAAU,CAAC,EAAC;AAC5E,QAAM,MAAM,MAAM,MAAM;AACxB,SAAO,CAAC,MAAM,UAAU,MAAM,MAAM,GAAG,EAAC,MAAM,QAAQ,OAAO,KAAI,CAAC;AACpE;;;ACLO,SAAS,KAAK,OAAO,MAAM;AAChC,QAAM,QAAQ,KAAK,QAAQ,KAAK,QAAQ,OAAO;AAE/C,QAAM,aAAa,CAAC;AAEpB,MAAI,KAAK,MAAM;AACb,eAAW,YAAY,CAAC,cAAc,KAAK,IAAI;AAAA,EACjD;AAIA,MAAI,SAAS;AAAA,IACX,MAAM;AAAA,IACN,SAAS;AAAA,IACT;AAAA,IACA,UAAU,CAAC,EAAC,MAAM,QAAQ,MAAK,CAAC;AAAA,EAClC;AAEA,MAAI,KAAK,MAAM;AACb,WAAO,OAAO,EAAC,MAAM,KAAK,KAAI;AAAA,EAChC;AAEA,QAAM,MAAM,MAAM,MAAM;AACxB,WAAS,MAAM,UAAU,MAAM,MAAM;AAGrC,WAAS,EAAC,MAAM,WAAW,SAAS,OAAO,YAAY,CAAC,GAAG,UAAU,CAAC,MAAM,EAAC;AAC7E,QAAM,MAAM,MAAM,MAAM;AACxB,SAAO;AACT;;;AC9BO,SAAS,cAAc,OAAO,MAAM;AAEzC,QAAM,SAAS;AAAA,IACb,MAAM;AAAA,IACN,SAAS;AAAA,IACT,YAAY,CAAC;AAAA,IACb,UAAU,MAAM,IAAI,IAAI;AAAA,EAC1B;AACA,QAAM,MAAM,MAAM,MAAM;AACxB,SAAO,MAAM,UAAU,MAAM,MAAM;AACrC;;;ACVO,SAAS,SAAS,OAAO,MAAM;AAEpC,QAAM,SAAS;AAAA,IACb,MAAM;AAAA,IACN,SAAS;AAAA,IACT,YAAY,CAAC;AAAA,IACb,UAAU,MAAM,IAAI,IAAI;AAAA,EAC1B;AACA,QAAM,MAAM,MAAM,MAAM;AACxB,SAAO,MAAM,UAAU,MAAM,MAAM;AACrC;;;ACXO,SAAS,kBAAkB,OAAO,MAAM;AAC7C,QAAM,gBACJ,OAAO,MAAM,QAAQ,kBAAkB,WACnC,MAAM,QAAQ,gBACd;AACN,QAAM,KAAK,OAAO,KAAK,UAAU,EAAE,YAAY;AAC/C,QAAM,SAAS,aAAa,GAAG,YAAY,CAAC;AAC5C,QAAM,QAAQ,MAAM,cAAc,QAAQ,EAAE;AAE5C,MAAI;AAEJ,MAAI,eAAe,MAAM,eAAe,IAAI,EAAE;AAE9C,MAAI,iBAAiB,QAAW;AAC9B,mBAAe;AACf,UAAM,cAAc,KAAK,EAAE;AAC3B,cAAU,MAAM,cAAc;AAAA,EAChC,OAAO;AACL,cAAU,QAAQ;AAAA,EACpB;AAEA,kBAAgB;AAChB,QAAM,eAAe,IAAI,IAAI,YAAY;AAGzC,QAAMA,QAAO;AAAA,IACX,MAAM;AAAA,IACN,SAAS;AAAA,IACT,YAAY;AAAA,MACV,MAAM,MAAM,gBAAgB,QAAQ;AAAA,MACpC,IACE,gBACA,WACA,UACC,eAAe,IAAI,MAAM,eAAe;AAAA,MAC3C,iBAAiB;AAAA,MACjB,iBAAiB,CAAC,gBAAgB;AAAA,IACpC;AAAA,IACA,UAAU,CAAC,EAAC,MAAM,QAAQ,OAAO,OAAO,OAAO,EAAC,CAAC;AAAA,EACnD;AACA,QAAM,MAAM,MAAMA,KAAI;AAGtB,QAAM,MAAM;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,YAAY,CAAC;AAAA,IACb,UAAU,CAACA,KAAI;AAAA,EACjB;AACA,QAAM,MAAM,MAAM,GAAG;AACrB,SAAO,MAAM,UAAU,MAAM,GAAG;AAClC;;;AClDO,SAAS,QAAQ,OAAO,MAAM;AAEnC,QAAM,SAAS;AAAA,IACb,MAAM;AAAA,IACN,SAAS,MAAM,KAAK;AAAA,IACpB,YAAY,CAAC;AAAA,IACb,UAAU,MAAM,IAAI,IAAI;AAAA,EAC1B;AACA,QAAM,MAAM,MAAM,MAAM;AACxB,SAAO,MAAM,UAAU,MAAM,MAAM;AACrC;;;ACRO,SAAS,KAAK,OAAO,MAAM;AAChC,MAAI,MAAM,QAAQ,oBAAoB;AAEpC,UAAM,SAAS,EAAC,MAAM,OAAO,OAAO,KAAK,MAAK;AAC9C,UAAM,MAAM,MAAM,MAAM;AACxB,WAAO,MAAM,UAAU,MAAM,MAAM;AAAA,EACrC;AAEA,SAAO;AACT;;;ACRO,SAAS,OAAO,OAAO,MAAM;AAClC,QAAM,UAAU,KAAK;AACrB,MAAI,SAAS;AAEb,MAAI,YAAY,aAAa;AAC3B,cAAU;AAAA,EACZ,WAAW,YAAY,QAAQ;AAC7B,cAAU,OAAO,KAAK,SAAS,KAAK,cAAc;AAAA,EACpD;AAEA,MAAI,KAAK,SAAS,kBAAkB;AAClC,WAAO,CAAC,EAAC,MAAM,QAAQ,OAAO,OAAO,KAAK,MAAM,OAAM,CAAC;AAAA,EACzD;AAEA,QAAM,WAAW,MAAM,IAAI,IAAI;AAC/B,QAAM,OAAO,SAAS,CAAC;AAEvB,MAAI,QAAQ,KAAK,SAAS,QAAQ;AAChC,SAAK,QAAQ,MAAM,KAAK;AAAA,EAC1B,OAAO;AACL,aAAS,QAAQ,EAAC,MAAM,QAAQ,OAAO,IAAG,CAAC;AAAA,EAC7C;AAEA,QAAM,OAAO,SAAS,SAAS,SAAS,CAAC;AAEzC,MAAI,QAAQ,KAAK,SAAS,QAAQ;AAChC,SAAK,SAAS;AAAA,EAChB,OAAO;AACL,aAAS,KAAK,EAAC,MAAM,QAAQ,OAAO,OAAM,CAAC;AAAA,EAC7C;AAEA,SAAO;AACT;;;ACjCO,SAAS,eAAe,OAAO,MAAM;AAC1C,QAAM,KAAK,OAAO,KAAK,UAAU,EAAE,YAAY;AAC/C,QAAM,aAAa,MAAM,eAAe,IAAI,EAAE;AAE9C,MAAI,CAAC,YAAY;AACf,WAAO,OAAO,OAAO,IAAI;AAAA,EAC3B;AAGA,QAAM,aAAa,EAAC,KAAK,aAAa,WAAW,OAAO,EAAE,GAAG,KAAK,KAAK,IAAG;AAE1E,MAAI,WAAW,UAAU,QAAQ,WAAW,UAAU,QAAW;AAC/D,eAAW,QAAQ,WAAW;AAAA,EAChC;AAGA,QAAM,SAAS,EAAC,MAAM,WAAW,SAAS,OAAO,YAAY,UAAU,CAAC,EAAC;AACzE,QAAM,MAAM,MAAM,MAAM;AACxB,SAAO,MAAM,UAAU,MAAM,MAAM;AACrC;;;ACrBO,SAAS,MAAM,OAAO,MAAM;AAEjC,QAAM,aAAa,EAAC,KAAK,aAAa,KAAK,GAAG,EAAC;AAE/C,MAAI,KAAK,QAAQ,QAAQ,KAAK,QAAQ,QAAW;AAC/C,eAAW,MAAM,KAAK;AAAA,EACxB;AAEA,MAAI,KAAK,UAAU,QAAQ,KAAK,UAAU,QAAW;AACnD,eAAW,QAAQ,KAAK;AAAA,EAC1B;AAGA,QAAM,SAAS,EAAC,MAAM,WAAW,SAAS,OAAO,YAAY,UAAU,CAAC,EAAC;AACzE,QAAM,MAAM,MAAM,MAAM;AACxB,SAAO,MAAM,UAAU,MAAM,MAAM;AACrC;;;ACfO,SAAS,WAAW,OAAO,MAAM;AAEtC,QAAMC,QAAO,EAAC,MAAM,QAAQ,OAAO,KAAK,MAAM,QAAQ,aAAa,GAAG,EAAC;AACvE,QAAM,MAAM,MAAMA,KAAI;AAGtB,QAAM,SAAS;AAAA,IACb,MAAM;AAAA,IACN,SAAS;AAAA,IACT,YAAY,CAAC;AAAA,IACb,UAAU,CAACA,KAAI;AAAA,EACjB;AACA,QAAM,MAAM,MAAM,MAAM;AACxB,SAAO,MAAM,UAAU,MAAM,MAAM;AACrC;;;ACbO,SAAS,cAAc,OAAO,MAAM;AACzC,QAAM,KAAK,OAAO,KAAK,UAAU,EAAE,YAAY;AAC/C,QAAM,aAAa,MAAM,eAAe,IAAI,EAAE;AAE9C,MAAI,CAAC,YAAY;AACf,WAAO,OAAO,OAAO,IAAI;AAAA,EAC3B;AAGA,QAAM,aAAa,EAAC,MAAM,aAAa,WAAW,OAAO,EAAE,EAAC;AAE5D,MAAI,WAAW,UAAU,QAAQ,WAAW,UAAU,QAAW;AAC/D,eAAW,QAAQ,WAAW;AAAA,EAChC;AAGA,QAAM,SAAS;AAAA,IACb,MAAM;AAAA,IACN,SAAS;AAAA,IACT;AAAA,IACA,UAAU,MAAM,IAAI,IAAI;AAAA,EAC1B;AACA,QAAM,MAAM,MAAM,MAAM;AACxB,SAAO,MAAM,UAAU,MAAM,MAAM;AACrC;;;AC1BO,SAAS,KAAK,OAAO,MAAM;AAEhC,QAAM,aAAa,EAAC,MAAM,aAAa,KAAK,GAAG,EAAC;AAEhD,MAAI,KAAK,UAAU,QAAQ,KAAK,UAAU,QAAW;AACnD,eAAW,QAAQ,KAAK;AAAA,EAC1B;AAGA,QAAM,SAAS;AAAA,IACb,MAAM;AAAA,IACN,SAAS;AAAA,IACT;AAAA,IACA,UAAU,MAAM,IAAI,IAAI;AAAA,EAC1B;AACA,QAAM,MAAM,MAAM,MAAM;AACxB,SAAO,MAAM,UAAU,MAAM,MAAM;AACrC;;;ACZO,SAAS,SAAS,OAAO,MAAM,QAAQ;AAC5C,QAAM,UAAU,MAAM,IAAI,IAAI;AAC9B,QAAM,QAAQ,SAAS,UAAU,MAAM,IAAI,cAAc,IAAI;AAE7D,QAAM,aAAa,CAAC;AAEpB,QAAM,WAAW,CAAC;AAElB,MAAI,OAAO,KAAK,YAAY,WAAW;AACrC,UAAM,OAAO,QAAQ,CAAC;AAEtB,QAAIC;AAEJ,QAAI,QAAQ,KAAK,SAAS,aAAa,KAAK,YAAY,KAAK;AAC3D,MAAAA,aAAY;AAAA,IACd,OAAO;AACL,MAAAA,aAAY,EAAC,MAAM,WAAW,SAAS,KAAK,YAAY,CAAC,GAAG,UAAU,CAAC,EAAC;AACxE,cAAQ,QAAQA,UAAS;AAAA,IAC3B;AAEA,QAAIA,WAAU,SAAS,SAAS,GAAG;AACjC,MAAAA,WAAU,SAAS,QAAQ,EAAC,MAAM,QAAQ,OAAO,IAAG,CAAC;AAAA,IACvD;AAEA,IAAAA,WAAU,SAAS,QAAQ;AAAA,MACzB,MAAM;AAAA,MACN,SAAS;AAAA,MACT,YAAY,EAAC,MAAM,YAAY,SAAS,KAAK,SAAS,UAAU,KAAI;AAAA,MACpE,UAAU,CAAC;AAAA,IACb,CAAC;AAID,eAAW,YAAY,CAAC,gBAAgB;AAAA,EAC1C;AAEA,MAAI,QAAQ;AAEZ,SAAO,EAAE,QAAQ,QAAQ,QAAQ;AAC/B,UAAM,QAAQ,QAAQ,KAAK;AAG3B,QACE,SACA,UAAU,KACV,MAAM,SAAS,aACf,MAAM,YAAY,KAClB;AACA,eAAS,KAAK,EAAC,MAAM,QAAQ,OAAO,KAAI,CAAC;AAAA,IAC3C;AAEA,QAAI,MAAM,SAAS,aAAa,MAAM,YAAY,OAAO,CAAC,OAAO;AAC/D,eAAS,KAAK,GAAG,MAAM,QAAQ;AAAA,IACjC,OAAO;AACL,eAAS,KAAK,KAAK;AAAA,IACrB;AAAA,EACF;AAEA,QAAM,OAAO,QAAQ,QAAQ,SAAS,CAAC;AAGvC,MAAI,SAAS,SAAS,KAAK,SAAS,aAAa,KAAK,YAAY,MAAM;AACtE,aAAS,KAAK,EAAC,MAAM,QAAQ,OAAO,KAAI,CAAC;AAAA,EAC3C;AAGA,QAAM,SAAS,EAAC,MAAM,WAAW,SAAS,MAAM,YAAY,SAAQ;AACpE,QAAM,MAAM,MAAM,MAAM;AACxB,SAAO,MAAM,UAAU,MAAM,MAAM;AACrC;AAMA,SAAS,UAAU,MAAM;AACvB,MAAI,QAAQ;AACZ,MAAI,KAAK,SAAS,QAAQ;AACxB,YAAQ,KAAK,UAAU;AACvB,UAAM,WAAW,KAAK;AACtB,QAAI,QAAQ;AAEZ,WAAO,CAAC,SAAS,EAAE,QAAQ,SAAS,QAAQ;AAC1C,cAAQ,cAAc,SAAS,KAAK,CAAC;AAAA,IACvC;AAAA,EACF;AAEA,SAAO;AACT;AAMA,SAAS,cAAc,MAAM;AAC3B,QAAM,SAAS,KAAK;AAEpB,SAAO,WAAW,QAAQ,WAAW,SACjC,KAAK,SAAS,SAAS,IACvB;AACN;;;ACxGO,SAAS,KAAK,OAAO,MAAM;AAEhC,QAAM,aAAa,CAAC;AACpB,QAAM,UAAU,MAAM,IAAI,IAAI;AAC9B,MAAI,QAAQ;AAEZ,MAAI,OAAO,KAAK,UAAU,YAAY,KAAK,UAAU,GAAG;AACtD,eAAW,QAAQ,KAAK;AAAA,EAC1B;AAGA,SAAO,EAAE,QAAQ,QAAQ,QAAQ;AAC/B,UAAM,QAAQ,QAAQ,KAAK;AAE3B,QACE,MAAM,SAAS,aACf,MAAM,YAAY,QAClB,MAAM,cACN,MAAM,QAAQ,MAAM,WAAW,SAAS,KACxC,MAAM,WAAW,UAAU,SAAS,gBAAgB,GACpD;AACA,iBAAW,YAAY,CAAC,oBAAoB;AAC5C;AAAA,IACF;AAAA,EACF;AAGA,QAAM,SAAS;AAAA,IACb,MAAM;AAAA,IACN,SAAS,KAAK,UAAU,OAAO;AAAA,IAC/B;AAAA,IACA,UAAU,MAAM,KAAK,SAAS,IAAI;AAAA,EACpC;AACA,QAAM,MAAM,MAAM,MAAM;AACxB,SAAO,MAAM,UAAU,MAAM,MAAM;AACrC;;;ACpCO,SAAS,UAAU,OAAO,MAAM;AAErC,QAAM,SAAS;AAAA,IACb,MAAM;AAAA,IACN,SAAS;AAAA,IACT,YAAY,CAAC;AAAA,IACb,UAAU,MAAM,IAAI,IAAI;AAAA,EAC1B;AACA,QAAM,MAAM,MAAM,MAAM;AACxB,SAAO,MAAM,UAAU,MAAM,MAAM;AACrC;;;ACTO,SAAS,KAAK,OAAO,MAAM;AAEhC,QAAM,SAAS,EAAC,MAAM,QAAQ,UAAU,MAAM,KAAK,MAAM,IAAI,IAAI,CAAC,EAAC;AACnE,QAAM,MAAM,MAAM,MAAM;AACxB,SAAO,MAAM,UAAU,MAAM,MAAM;AACrC;;;ACNO,SAAS,OAAO,OAAO,MAAM;AAElC,QAAM,SAAS;AAAA,IACb,MAAM;AAAA,IACN,SAAS;AAAA,IACT,YAAY,CAAC;AAAA,IACb,UAAU,MAAM,IAAI,IAAI;AAAA,EAC1B;AACA,QAAM,MAAM,MAAM,MAAM;AACxB,SAAO,MAAM,UAAU,MAAM,MAAM;AACrC;;;ACXO,SAAS,MAAM,OAAO,MAAM;AACjC,QAAM,OAAO,MAAM,IAAI,IAAI;AAC3B,QAAM,WAAW,KAAK,MAAM;AAE5B,QAAM,eAAe,CAAC;AAEtB,MAAI,UAAU;AAEZ,UAAM,OAAO;AAAA,MACX,MAAM;AAAA,MACN,SAAS;AAAA,MACT,YAAY,CAAC;AAAA,MACb,UAAU,MAAM,KAAK,CAAC,QAAQ,GAAG,IAAI;AAAA,IACvC;AACA,UAAM,MAAM,KAAK,SAAS,CAAC,GAAG,IAAI;AAClC,iBAAa,KAAK,IAAI;AAAA,EACxB;AAEA,MAAI,KAAK,SAAS,GAAG;AAEnB,UAAM,OAAO;AAAA,MACX,MAAM;AAAA,MACN,SAAS;AAAA,MACT,YAAY,CAAC;AAAA,MACb,UAAU,MAAM,KAAK,MAAM,IAAI;AAAA,IACjC;AAEA,UAAM,QAAQ,WAAW,KAAK,SAAS,CAAC,CAAC;AACzC,UAAM,MAAM,SAAS,KAAK,SAAS,KAAK,SAAS,SAAS,CAAC,CAAC;AAC5D,QAAI,SAAS,IAAK,MAAK,WAAW,EAAC,OAAO,IAAG;AAC7C,iBAAa,KAAK,IAAI;AAAA,EACxB;AAGA,QAAM,SAAS;AAAA,IACb,MAAM;AAAA,IACN,SAAS;AAAA,IACT,YAAY,CAAC;AAAA,IACb,UAAU,MAAM,KAAK,cAAc,IAAI;AAAA,EACzC;AACA,QAAM,MAAM,MAAM,MAAM;AACxB,SAAO,MAAM,UAAU,MAAM,MAAM;AACrC;;;ACpCO,SAAS,SAAS,OAAO,MAAM,QAAQ;AAC5C,QAAM,WAAW,SAAS,OAAO,WAAW;AAE5C,QAAM,WAAW,WAAW,SAAS,QAAQ,IAAI,IAAI;AACrD,QAAM,UAAU,aAAa,IAAI,OAAO;AAExC,QAAM,QAAQ,UAAU,OAAO,SAAS,UAAU,OAAO,QAAQ;AACjE,QAAM,SAAS,QAAQ,MAAM,SAAS,KAAK,SAAS;AACpD,MAAI,YAAY;AAEhB,QAAM,QAAQ,CAAC;AAEf,SAAO,EAAE,YAAY,QAAQ;AAE3B,UAAM,OAAO,KAAK,SAAS,SAAS;AAEpC,UAAM,aAAa,CAAC;AACpB,UAAM,aAAa,QAAQ,MAAM,SAAS,IAAI;AAE9C,QAAI,YAAY;AACd,iBAAW,QAAQ;AAAA,IACrB;AAGA,QAAIC,UAAS,EAAC,MAAM,WAAW,SAAS,YAAY,UAAU,CAAC,EAAC;AAEhE,QAAI,MAAM;AACR,MAAAA,QAAO,WAAW,MAAM,IAAI,IAAI;AAChC,YAAM,MAAM,MAAMA,OAAM;AACxB,MAAAA,UAAS,MAAM,UAAU,MAAMA,OAAM;AAAA,IACvC;AAEA,UAAM,KAAKA,OAAM;AAAA,EACnB;AAGA,QAAM,SAAS;AAAA,IACb,MAAM;AAAA,IACN,SAAS;AAAA,IACT,YAAY,CAAC;AAAA,IACb,UAAU,MAAM,KAAK,OAAO,IAAI;AAAA,EAClC;AACA,QAAM,MAAM,MAAM,MAAM;AACxB,SAAO,MAAM,UAAU,MAAM,MAAM;AACrC;;;ACjDO,SAAS,UAAU,OAAO,MAAM;AAIrC,QAAM,SAAS;AAAA,IACb,MAAM;AAAA,IACN,SAAS;AAAA;AAAA,IACT,YAAY,CAAC;AAAA,IACb,UAAU,MAAM,IAAI,IAAI;AAAA,EAC1B;AACA,QAAM,MAAM,MAAM,MAAM;AACxB,SAAO,MAAM,UAAU,MAAM,MAAM;AACrC;;;AC/BA,IAAM,MAAM;AACZ,IAAM,QAAQ;AAWP,SAAS,UAAU,OAAO;AAC/B,QAAM,SAAS,OAAO,KAAK;AAC3B,QAAM,SAAS;AACf,MAAI,QAAQ,OAAO,KAAK,MAAM;AAC9B,MAAI,OAAO;AAEX,QAAM,QAAQ,CAAC;AAEf,SAAO,OAAO;AACZ,UAAM;AAAA,MACJ,SAAS,OAAO,MAAM,MAAM,MAAM,KAAK,GAAG,OAAO,GAAG,IAAI;AAAA,MACxD,MAAM,CAAC;AAAA,IACT;AAEA,WAAO,MAAM,QAAQ,MAAM,CAAC,EAAE;AAC9B,YAAQ,OAAO,KAAK,MAAM;AAAA,EAC5B;AAEA,QAAM,KAAK,SAAS,OAAO,MAAM,IAAI,GAAG,OAAO,GAAG,KAAK,CAAC;AAExD,SAAO,MAAM,KAAK,EAAE;AACtB;AAYA,SAAS,SAAS,OAAO,OAAO,KAAK;AACnC,MAAI,aAAa;AACjB,MAAI,WAAW,MAAM;AAErB,MAAI,OAAO;AACT,QAAIC,QAAO,MAAM,YAAY,UAAU;AAEvC,WAAOA,UAAS,OAAOA,UAAS,OAAO;AACrC;AACA,MAAAA,QAAO,MAAM,YAAY,UAAU;AAAA,IACrC;AAAA,EACF;AAEA,MAAI,KAAK;AACP,QAAIA,QAAO,MAAM,YAAY,WAAW,CAAC;AAEzC,WAAOA,UAAS,OAAOA,UAAS,OAAO;AACrC;AACA,MAAAA,QAAO,MAAM,YAAY,WAAW,CAAC;AAAA,IACvC;AAAA,EACF;AAEA,SAAO,WAAW,aAAa,MAAM,MAAM,YAAY,QAAQ,IAAI;AACrE;;;ACjDO,SAAS,KAAK,OAAO,MAAM;AAEhC,QAAM,SAAS,EAAC,MAAM,QAAQ,OAAO,UAAU,OAAO,KAAK,KAAK,CAAC,EAAC;AAClE,QAAM,MAAM,MAAM,MAAM;AACxB,SAAO,MAAM,UAAU,MAAM,MAAM;AACrC;;;ACLO,SAAS,cAAc,OAAO,MAAM;AAEzC,QAAM,SAAS;AAAA,IACb,MAAM;AAAA,IACN,SAAS;AAAA,IACT,YAAY,CAAC;AAAA,IACb,UAAU,CAAC;AAAA,EACb;AACA,QAAM,MAAM,MAAM,MAAM;AACxB,SAAO,MAAM,UAAU,MAAM,MAAM;AACrC;;;ACAO,IAAM,WAAW;AAAA,EACtB;AAAA,EACA,OAAO;AAAA,EACP;AAAA,EACA,QAAQ;AAAA,EACR;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,MAAM;AAAA,EACN,MAAM;AAAA,EACN,YAAY;AAAA,EACZ,oBAAoB;AACtB;AAGA,SAAS,SAAS;AAChB,SAAO;AACT;;;ACiBO,SAAS,2BAA2B,GAAG,kBAAkB;AAE9D,QAAM,SAAS,CAAC,EAAC,MAAM,QAAQ,OAAO,IAAG,CAAC;AAE1C,MAAI,mBAAmB,GAAG;AACxB,WAAO,KAAK;AAAA,MACV,MAAM;AAAA,MACN,SAAS;AAAA,MACT,YAAY,CAAC;AAAA,MACb,UAAU,CAAC,EAAC,MAAM,QAAQ,OAAO,OAAO,gBAAgB,EAAC,CAAC;AAAA,IAC5D,CAAC;AAAA,EACH;AAEA,SAAO;AACT;AAaO,SAAS,yBAAyB,gBAAgB,kBAAkB;AACzE,SACE,wBACC,iBAAiB,MACjB,mBAAmB,IAAI,MAAM,mBAAmB;AAErD;AAWO,SAAS,OAAO,OAAO;AAC5B,QAAM,gBACJ,OAAO,MAAM,QAAQ,kBAAkB,WACnC,MAAM,QAAQ,gBACd;AACN,QAAM,sBACJ,MAAM,QAAQ,uBAAuB;AACvC,QAAM,oBACJ,MAAM,QAAQ,qBAAqB;AACrC,QAAM,gBAAgB,MAAM,QAAQ,iBAAiB;AACrD,QAAM,uBAAuB,MAAM,QAAQ,wBAAwB;AACnE,QAAM,0BAA0B,MAAM,QAAQ,2BAA2B;AAAA,IACvE,WAAW,CAAC,SAAS;AAAA,EACvB;AAEA,QAAM,YAAY,CAAC;AACnB,MAAI,iBAAiB;AAErB,SAAO,EAAE,iBAAiB,MAAM,cAAc,QAAQ;AACpD,UAAM,aAAa,MAAM,aAAa;AAAA,MACpC,MAAM,cAAc,cAAc;AAAA,IACpC;AAEA,QAAI,CAAC,YAAY;AACf;AAAA,IACF;AAEA,UAAM,UAAU,MAAM,IAAI,UAAU;AACpC,UAAM,KAAK,OAAO,WAAW,UAAU,EAAE,YAAY;AACrD,UAAM,SAAS,aAAa,GAAG,YAAY,CAAC;AAC5C,QAAI,mBAAmB;AAEvB,UAAM,iBAAiB,CAAC;AACxB,UAAM,SAAS,MAAM,eAAe,IAAI,EAAE;AAG1C,WAAO,WAAW,UAAa,EAAE,oBAAoB,QAAQ;AAC3D,UAAI,eAAe,SAAS,GAAG;AAC7B,uBAAe,KAAK,EAAC,MAAM,QAAQ,OAAO,IAAG,CAAC;AAAA,MAChD;AAEA,UAAI,WACF,OAAO,wBAAwB,WAC3B,sBACA,oBAAoB,gBAAgB,gBAAgB;AAE1D,UAAI,OAAO,aAAa,UAAU;AAChC,mBAAW,EAAC,MAAM,QAAQ,OAAO,SAAQ;AAAA,MAC3C;AAEA,qBAAe,KAAK;AAAA,QAClB,MAAM;AAAA,QACN,SAAS;AAAA,QACT,YAAY;AAAA,UACV,MACE,MACA,gBACA,WACA,UACC,mBAAmB,IAAI,MAAM,mBAAmB;AAAA,UACnD,qBAAqB;AAAA,UACrB,WACE,OAAO,sBAAsB,WACzB,oBACA,kBAAkB,gBAAgB,gBAAgB;AAAA,UACxD,WAAW,CAAC,uBAAuB;AAAA,QACrC;AAAA,QACA,UAAU,MAAM,QAAQ,QAAQ,IAAI,WAAW,CAAC,QAAQ;AAAA,MAC1D,CAAC;AAAA,IACH;AAEA,UAAM,OAAO,QAAQ,QAAQ,SAAS,CAAC;AAEvC,QAAI,QAAQ,KAAK,SAAS,aAAa,KAAK,YAAY,KAAK;AAC3D,YAAM,WAAW,KAAK,SAAS,KAAK,SAAS,SAAS,CAAC;AACvD,UAAI,YAAY,SAAS,SAAS,QAAQ;AACxC,iBAAS,SAAS;AAAA,MACpB,OAAO;AACL,aAAK,SAAS,KAAK,EAAC,MAAM,QAAQ,OAAO,IAAG,CAAC;AAAA,MAC/C;AAEA,WAAK,SAAS,KAAK,GAAG,cAAc;AAAA,IACtC,OAAO;AACL,cAAQ,KAAK,GAAG,cAAc;AAAA,IAChC;AAGA,UAAMC,YAAW;AAAA,MACf,MAAM;AAAA,MACN,SAAS;AAAA,MACT,YAAY,EAAC,IAAI,gBAAgB,QAAQ,OAAM;AAAA,MAC/C,UAAU,MAAM,KAAK,SAAS,IAAI;AAAA,IACpC;AAEA,UAAM,MAAM,YAAYA,SAAQ;AAEhC,cAAU,KAAKA,SAAQ;AAAA,EACzB;AAEA,MAAI,UAAU,WAAW,GAAG;AAC1B;AAAA,EACF;AAEA,SAAO;AAAA,IACL,MAAM;AAAA,IACN,SAAS;AAAA,IACT,YAAY,EAAC,eAAe,MAAM,WAAW,CAAC,WAAW,EAAC;AAAA,IAC1D,UAAU;AAAA,MACR;AAAA,QACE,MAAM;AAAA,QACN,SAAS;AAAA,QACT,YAAY;AAAA,UACV,GAAG,YAAgB,uBAAuB;AAAA,UAC1C,IAAI;AAAA,QACN;AAAA,QACA,UAAU,CAAC,EAAC,MAAM,QAAQ,OAAO,cAAa,CAAC;AAAA,MACjD;AAAA,MACA,EAAC,MAAM,QAAQ,OAAO,KAAI;AAAA,MAC1B;AAAA,QACE,MAAM;AAAA,QACN,SAAS;AAAA,QACT,YAAY,CAAC;AAAA,QACb,UAAU,MAAM,KAAK,WAAW,IAAI;AAAA,MACtC;AAAA,MACA,EAAC,MAAM,QAAQ,OAAO,KAAI;AAAA,IAC5B;AAAA,EACF;AACF;;;ACxEA,IAAM,MAAM,CAAC,EAAE;AAGf,IAAM,eAAe,CAAC;AAYf,SAAS,YAAY,MAAM,SAAS;AACzC,QAAM,WAAW,WAAW;AAE5B,QAAM,iBAAiB,oBAAI,IAAI;AAE/B,QAAM,eAAe,oBAAI,IAAI;AAE7B,QAAM,iBAAiB,oBAAI,IAAI;AAI/B,QAAMC,YAAW,EAAC,GAAG,UAAiB,GAAG,SAAS,SAAQ;AAG1D,QAAM,QAAQ;AAAA,IACZ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,eAAe,CAAC;AAAA,IAChB,UAAAA;AAAA,IACA;AAAA,IACA,SAAS;AAAA,IACT;AAAA,IACA;AAAA,EACF;AAEA,QAAM,MAAM,SAAU,MAAM;AAC1B,QAAI,KAAK,SAAS,gBAAgB,KAAK,SAAS,sBAAsB;AACpE,YAAM,MAAM,KAAK,SAAS,eAAe,iBAAiB;AAC1D,YAAM,KAAK,OAAO,KAAK,UAAU,EAAE,YAAY;AAI/C,UAAI,CAAC,IAAI,IAAI,EAAE,GAAG;AAEhB,YAAI,IAAI,IAAI,IAAI;AAAA,MAClB;AAAA,IACF;AAAA,EACF,CAAC;AAED,SAAO;AAYP,WAAS,IAAI,MAAM,QAAQ;AACzB,UAAM,OAAO,KAAK;AAClB,UAAM,SAAS,MAAM,SAAS,IAAI;AAElC,QAAI,IAAI,KAAK,MAAM,UAAU,IAAI,KAAK,QAAQ;AAC5C,aAAO,OAAO,OAAO,MAAM,MAAM;AAAA,IACnC;AAEA,QAAI,MAAM,QAAQ,eAAe,MAAM,QAAQ,YAAY,SAAS,IAAI,GAAG;AACzE,UAAI,cAAc,MAAM;AACtB,cAAM,EAAC,UAAU,GAAG,QAAO,IAAI;AAC/B,cAAM,SAAS,YAAgB,OAAO;AAEtC,eAAO,WAAW,MAAM,IAAI,IAAI;AAEhC,eAAO;AAAA,MACT;AAGA,aAAO,YAAgB,IAAI;AAAA,IAC7B;AAEA,UAAM,UAAU,MAAM,QAAQ,kBAAkB;AAEhD,WAAO,QAAQ,OAAO,MAAM,MAAM;AAAA,EACpC;AAUA,WAAS,IAAI,QAAQ;AAEnB,UAAM,SAAS,CAAC;AAEhB,QAAI,cAAc,QAAQ;AACxB,YAAM,QAAQ,OAAO;AACrB,UAAI,QAAQ;AACZ,aAAO,EAAE,QAAQ,MAAM,QAAQ;AAC7B,cAAM,SAAS,MAAM,IAAI,MAAM,KAAK,GAAG,MAAM;AAG7C,YAAI,QAAQ;AACV,cAAI,SAAS,MAAM,QAAQ,CAAC,EAAE,SAAS,SAAS;AAC9C,gBAAI,CAAC,MAAM,QAAQ,MAAM,KAAK,OAAO,SAAS,QAAQ;AACpD,qBAAO,QAAQ,uBAAuB,OAAO,KAAK;AAAA,YACpD;AAEA,gBAAI,CAAC,MAAM,QAAQ,MAAM,KAAK,OAAO,SAAS,WAAW;AACvD,oBAAM,OAAO,OAAO,SAAS,CAAC;AAE9B,kBAAI,QAAQ,KAAK,SAAS,QAAQ;AAChC,qBAAK,QAAQ,uBAAuB,KAAK,KAAK;AAAA,cAChD;AAAA,YACF;AAAA,UACF;AAEA,cAAI,MAAM,QAAQ,MAAM,GAAG;AACzB,mBAAO,KAAK,GAAG,MAAM;AAAA,UACvB,OAAO;AACL,mBAAO,KAAK,MAAM;AAAA,UACpB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AACF;AAYA,SAAS,MAAM,MAAM,IAAI;AACvB,MAAI,KAAK,SAAU,IAAG,WAAW,SAAS,IAAI;AAChD;AAcA,SAAS,UAAU,MAAM,IAAI;AAE3B,MAAI,SAAS;AAGb,MAAI,QAAQ,KAAK,MAAM;AACrB,UAAM,QAAQ,KAAK,KAAK;AACxB,UAAM,YAAY,KAAK,KAAK;AAC5B,UAAM,cAAc,KAAK,KAAK;AAE9B,QAAI,OAAO,UAAU,UAAU;AAG7B,UAAI,OAAO,SAAS,WAAW;AAC7B,eAAO,UAAU;AAAA,MACnB,OAKK;AAGH,cAAM,WAAW,cAAc,SAAS,OAAO,WAAW,CAAC,MAAM;AACjE,iBAAS,EAAC,MAAM,WAAW,SAAS,OAAO,YAAY,CAAC,GAAG,SAAQ;AAAA,MACrE;AAAA,IACF;AAEA,QAAI,OAAO,SAAS,aAAa,aAAa;AAC5C,aAAO,OAAO,OAAO,YAAY,YAAgB,WAAW,CAAC;AAAA,IAC/D;AAEA,QACE,cAAc,UACd,OAAO,YACP,cAAc,QACd,cAAc,QACd;AACA,aAAO,WAAW;AAAA,IACpB;AAAA,EACF;AAEA,SAAO;AACT;AAYA,SAAS,sBAAsB,OAAO,MAAM;AAC1C,QAAM,OAAO,KAAK,QAAQ,CAAC;AAE3B,QAAM,SACJ,WAAW,QACX,EAAE,IAAI,KAAK,MAAM,aAAa,KAAK,IAAI,KAAK,MAAM,WAAW,KACzD,EAAC,MAAM,QAAQ,OAAO,KAAK,MAAK,IAChC;AAAA,IACE,MAAM;AAAA,IACN,SAAS;AAAA,IACT,YAAY,CAAC;AAAA,IACb,UAAU,MAAM,IAAI,IAAI;AAAA,EAC1B;AAEN,QAAM,MAAM,MAAM,MAAM;AACxB,SAAO,MAAM,UAAU,MAAM,MAAM;AACrC;AAcO,SAAS,KAAK,OAAO,OAAO;AAEjC,QAAM,SAAS,CAAC;AAChB,MAAI,QAAQ;AAEZ,MAAI,OAAO;AACT,WAAO,KAAK,EAAC,MAAM,QAAQ,OAAO,KAAI,CAAC;AAAA,EACzC;AAEA,SAAO,EAAE,QAAQ,MAAM,QAAQ;AAC7B,QAAI,MAAO,QAAO,KAAK,EAAC,MAAM,QAAQ,OAAO,KAAI,CAAC;AAClD,WAAO,KAAK,MAAM,KAAK,CAAC;AAAA,EAC1B;AAEA,MAAI,SAAS,MAAM,SAAS,GAAG;AAC7B,WAAO,KAAK,EAAC,MAAM,QAAQ,OAAO,KAAI,CAAC;AAAA,EACzC;AAEA,SAAO;AACT;AAUA,SAAS,uBAAuB,OAAO;AACrC,MAAI,QAAQ;AACZ,MAAIC,QAAO,MAAM,WAAW,KAAK;AAEjC,SAAOA,UAAS,KAAKA,UAAS,IAAI;AAChC;AACA,IAAAA,QAAO,MAAM,WAAW,KAAK;AAAA,EAC/B;AAEA,SAAO,MAAM,MAAM,KAAK;AAC1B;;;ACjYO,SAAS,OAAO,MAAM,SAAS;AACpC,QAAM,QAAQ,YAAY,MAAM,OAAO;AACvC,QAAM,OAAO,MAAM,IAAI,MAAM,MAAS;AACtC,QAAM,OAAO,OAAO,KAAK;AAEzB,QAAM,SAAS,MAAM,QAAQ,IAAI,IAC7B,EAAC,MAAM,QAAQ,UAAU,KAAI,IAC7B,QAAQ,EAAC,MAAM,QAAQ,UAAU,CAAC,EAAC;AAEvC,MAAI,MAAM;AAIR,OAAO,cAAc,MAAM;AAC3B,WAAO,SAAS,KAAK,EAAC,MAAM,QAAQ,OAAO,KAAI,GAAG,IAAI;AAAA,EACxD;AAEA,SAAO;AACT;;;AC0Ce,SAAR,aAA8B,aAAa,SAAS;AACzD,MAAI,eAAe,SAAS,aAAa;AAIvC,WAAO,eAAgB,MAAM,MAAM;AAEjC,YAAM;AAAA;AAAA,QACJ,OAAO,MAAM,EAAC,MAAM,GAAG,QAAO,CAAC;AAAA;AAEjC,YAAM,YAAY,IAAI,UAAU,IAAI;AAAA,IACtC;AAAA,EACF;AAKA,SAAO,SAAU,MAAM,MAAM;AAM3B;AAAA;AAAA,MACE,OAAO,MAAM,EAAC,MAAM,GAAI,eAAe,QAAQ,CAAC;AAAA;AAAA,EAEpD;AACF;", "names": ["link", "text", "paragraph", "result", "code", "listItem", "handlers", "code"]}