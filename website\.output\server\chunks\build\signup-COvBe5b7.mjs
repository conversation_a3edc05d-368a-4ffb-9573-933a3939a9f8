import { _ as _export_sfc, h as useNuxtApp, b as useUserStore, a as __nuxt_component_0$2 } from './server.mjs';
import { defineComponent, ref, inject, mergeProps, withCtx, createVNode, unref, createTextVNode, useSSRContext } from 'vue';
import { ssrRenderAttrs, ssrRenderComponent, ssrRenderAttr, ssrRenderClass, ssrInterpolate, ssrIncludeBooleanAttr, ssrLooseContain } from 'vue/server-renderer';
import { _ as _imports_2 } from './virtual_public-Cy4CASs_.mjs';
import { u as useLoader, _ as _imports_1 } from './useLoader-2w2M3dXZ.mjs';
import { u as useAppSeoMeta } from './useAppSeoMeta-HWukB6FN.mjs';
import '../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:url';
import '@iconify/utils';
import 'node:crypto';
import 'consola';
import 'node:path';
import 'better-sqlite3';
import 'ipx';
import 'pinia';
import 'vue-router';
import 'cookie';
import '@iconify/vue';
import 'tailwindcss/colors';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import 'unhead/server';
import 'devalue';
import 'unhead/plugins';
import 'unhead/utils';

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "signup",
  __ssrInlineRender: true,
  setup(__props) {
    useAppSeoMeta({
      title: "SignUp | Bookslotz",
      description: "SignUp to Bookslotz to manage your appointments and profile.",
      image: "/images/auth-image.png"
    });
    const { $firebaseAuth, $googleProvider, $appleProvider } = useNuxtApp();
    const form = ref({
      firstName: "",
      lastName: "",
      email: "",
      terms: false
    });
    const firstNameError = ref("");
    const lastNameError = ref("");
    const emailError = ref("");
    const isLoading = ref(false);
    useUserStore();
    useLoader();
    inject("showSnackbar");
    return (_ctx, _push, _parent, _attrs) => {
      const _component_NuxtLink = __nuxt_component_0$2;
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "h-screen overflow-hidden" }, _attrs))} data-v-07f33367><div class="grid grid-cols-12 h-full overflow-y-auto" data-v-07f33367><div class="col-span-12 sm:col-span-12 md:col-span-7 lg:col-span-7 flex flex-col justify-between pb-6 pt-12" data-v-07f33367><div class="flex-1 flex flex-col justify-center items-center" data-v-07f33367><div class="w-full max-w-md" data-v-07f33367><div class="flex items-center justify-center mb-2" data-v-07f33367><div class="mr-2" data-v-07f33367>`);
      _push(ssrRenderComponent(_component_NuxtLink, { to: "/" }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`<img class="h-20 w-auto"${ssrRenderAttr("src", _imports_2)} alt="logo" data-v-07f33367${_scopeId}>`);
          } else {
            return [
              createVNode("img", {
                class: "h-20 w-auto",
                src: _imports_2,
                alt: "logo"
              })
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`</div></div><div class="text-center mb-2" data-v-07f33367><h2 class="text-2xl text-[#263238] font-extrabold mb-1" data-v-07f33367>Create Your Account</h2><p class="text-md font-normal text-[#303030B2]" data-v-07f33367> Enter your details to create <br class="block sm:hidden" data-v-07f33367> a new account </p></div><form data-v-07f33367><div class="space-y-3 px-10" data-v-07f33367><div data-v-07f33367><label class="block text-sm dm-sansmedium text-[#272727]" data-v-07f33367>First Name</label><input type="text"${ssrRenderAttr("value", unref(form).firstName)} placeholder="Jane" class="${ssrRenderClass([[
        unref(firstNameError) ? "border-red-500 focus:ring-red-500" : "border-gray-200 focus:ring-[#7B3FF2]"
      ], "w-full px-4 font-light py-2 bg-[#B0D4FC1F] text-[#333333] border rounded-md focus:outline-none focus:ring-1 transition"])}" data-v-07f33367>`);
      if (unref(firstNameError)) {
        _push(`<div class="text-red-500 text-xs mt-1" data-v-07f33367>${ssrInterpolate(unref(firstNameError))}</div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div><div data-v-07f33367><label class="block text-sm dm-sansmedium text-[#272727] pb-1" data-v-07f33367>Last Name</label><input type="text"${ssrRenderAttr("value", unref(form).lastName)} placeholder="Carter" class="${ssrRenderClass([[
        unref(lastNameError) ? "border-red-500 focus:ring-red-500" : "border-gray-200 focus:ring-[#7B3FF2]"
      ], "w-full px-4 font-light py-2 bg-[#B0D4FC1F] text-[#333333] border rounded-md focus:outline-none focus:ring-1 transition"])}" data-v-07f33367>`);
      if (unref(lastNameError)) {
        _push(`<div class="text-red-500 text-xs mt-1" data-v-07f33367>${ssrInterpolate(unref(lastNameError))}</div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div><div data-v-07f33367><label class="block text-sm dm-sansmedium text-[#272727] pb-1" data-v-07f33367>Email</label><input type="email"${ssrRenderAttr("value", unref(form).email)} placeholder="<EMAIL>" autocomplete="email" class="${ssrRenderClass([[
        unref(emailError) ? "border-red-500 focus:ring-red-500" : "border-gray-200 focus:ring-[#7B3FF2]"
      ], "w-full px-4 font-light py-2 bg-[#B0D4FC1F] text-[#333333] border rounded-md focus:outline-none focus:ring-1 transition"])}" data-v-07f33367>`);
      if (unref(emailError)) {
        _push(`<div class="text-red-500 text-xs mt-1" data-v-07f33367>${ssrInterpolate(unref(emailError))}</div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div><div class="flex items-start gap-2 text-xs text-[#272727]" data-v-07f33367><input type="checkbox" id="terms"${ssrIncludeBooleanAttr(Array.isArray(unref(form).terms) ? ssrLooseContain(unref(form).terms, null) : unref(form).terms) ? " checked" : ""} class="mt-1 accent-[#7B27E7] w-3 h-3 border-gray-300 rounded focus:ring-0" data-v-07f33367><label for="terms" class="font-normal leading-snug" data-v-07f33367> I agree to the `);
      _push(ssrRenderComponent(_component_NuxtLink, {
        href: "#",
        class: "text-[#000000CC] font-semibold underline hover:text-[#7B3FF2]"
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`Terms &amp; Conditions`);
          } else {
            return [
              createTextVNode("Terms & Conditions")
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(` and `);
      _push(ssrRenderComponent(_component_NuxtLink, {
        href: "#",
        class: "text-[#000000CC] font-semibold underline hover:text-[#7B3FF2]"
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`Privacy Policy`);
          } else {
            return [
              createTextVNode("Privacy Policy")
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`</label></div><button type="submit"${ssrIncludeBooleanAttr(unref(isLoading)) ? " disabled" : ""} class="w-full bg-[#7B27E7] shadow-lg font-normal text-white py-2 rounded-full font-medium hover:bg-[#6a2eea] transition duration-200 disabled:opacity-70 disabled:cursor-not-allowed" data-v-07f33367> Sign up </button><div class="relative flex items-center justify-center my-2 py-2" data-v-07f33367><div class="border-t border-[#33333333] w-full" data-v-07f33367></div><div class="absolute bg-white px-4 text-xs font-normal text-gray-500" data-v-07f33367>or</div></div><button type="button"${ssrIncludeBooleanAttr(unref(isLoading)) ? " disabled" : ""} class="w-full flex items-center justify-center font-normal text-[#333232] gap-2 py-2 border bg-[#FAFAFA] border-[#F1F1F1] rounded-full hover:bg-gray-50 transition duration-200 cursor-pointer disabled:opacity-70 disabled:cursor-not-allowed" data-v-07f33367><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48" width="20px" height="20px" data-v-07f33367><path fill="#FFC107" d="M43.611,20.083H42V20H24v8h11.303c-1.649,4.657-6.08,8-11.303,8c-6.627,0-12-5.373-12-12c0-6.627,5.373-12,12-12c3.059,0,5.842,1.154,7.961,3.039l5.657-5.657C34.046,6.053,29.268,4,24,4C12.955,4,4,12.955,4,24c0,11.045,8.955,20,20,20c11.045,0,20-8.955,20-20C44,22.659,43.862,21.35,43.611,20.083z" data-v-07f33367></path><path fill="#FF3D00" d="M6.306,14.691l6.571,4.819C14.655,15.108,18.961,12,24,12c3.059,0,5.842,1.154,7.961,3.039l5.657-5.657C34.046,6.053,29.268,4,24,4C16.318,4,9.656,8.337,6.306,14.691z" data-v-07f33367></path><path fill="#4CAF50" d="M24,44c5.166,0,9.86-1.977,13.409-5.192l-6.19-5.238C29.211,35.091,26.715,36,24,36c-5.202,0-9.619-3.317-11.283-7.946l-6.522,5.025C9.505,39.556,16.227,44,24,44z" data-v-07f33367></path><path fill="#1976D2" d="M43.611,20.083H42V20H24v8h11.303c-0.792,2.237-2.231,4.166-4.087,5.571c0.001-0.001,0.002-0.001,0.003-0.002l6.19,5.238C36.971,39.205,44,34,44,24C44,22.659,43.862,21.35,43.611,20.083z" data-v-07f33367></path></svg> Continue with Google </button><button type="button"${ssrIncludeBooleanAttr(unref(isLoading)) ? " disabled" : ""} class="w-full flex items-center justify-center font-normal gap-3 py-2 bg-black text-white rounded-full hover:bg-gray-800 transition duration-200 cursor-pointer disabled:opacity-70 disabled:cursor-not-allowed" data-v-07f33367><svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor" data-v-07f33367><path d="M18.71 19.5C17.88 20.74 17 21.95 15.66 21.97C14.32 22 13.89 21.18 12.37 21.18C10.84 21.18 10.37 21.95 9.09997 22C7.78997 22.05 6.79997 20.68 5.95997 19.47C4.24997 17 2.93997 12.45 4.69997 9.39C5.56997 7.87 7.13997 6.91 8.85997 6.88C10.15 6.86 11.36 7.75 12.11 7.75C12.85 7.75 14.28 6.68 15.85 6.84C16.48 6.87 18.02 7.12 19.05 8.55C18.96 8.61 17.15 9.74 17.18 11.93C17.22 14.69 19.68 15.64 19.73 15.67C19.68 15.8 19.31 17.04 18.71 19.5ZM13 3.5C13.73 2.67 14.94 2.04 15.94 2C16.07 3.17 15.6 4.35 14.9 5.19C14.21 6.04 13.07 6.7 11.95 6.61C11.8 5.46 12.36 4.26 13 3.5Z" data-v-07f33367></path></svg> Continue with Apple </button><div class="text-center mt-2 font-normal" data-v-07f33367><p class="text-sm text-[#000000]" data-v-07f33367> Already have an account? `);
      _push(ssrRenderComponent(_component_NuxtLink, {
        to: "/auth/login",
        class: "text-[#1976D2] font-medium hover:underline ml-1"
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`Login`);
          } else {
            return [
              createTextVNode("Login")
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`</p></div></div></form></div></div><div class="hidden md:flex justify-start pl-12 w-full mb-5 text-xs pt-12 text-black gap-4" data-v-07f33367><a href="#" class="hover:text-gray-700" data-v-07f33367>Terms and conditions</a><a href="#" class="hover:text-gray-700" data-v-07f33367>Privacy Policy</a></div></div><div class="hidden md:flex col-span-12 md:col-span-5 lg:col-span-5 md:p-8 relative" data-v-07f33367><img${ssrRenderAttr("src", _imports_1)} alt="salon" class="h-full w-full object-cover rounded-tr-2xl rounded-br-2xl" data-v-07f33367><div class="absolute bottom-14 right-12 text-xs text-gray-700" data-v-07f33367> \xA9 2025 All rights reserved </div></div></div></div>`);
    };
  }
});
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/auth/signup.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const signup = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-07f33367"]]);

export { signup as default };
//# sourceMappingURL=signup-COvBe5b7.mjs.map
