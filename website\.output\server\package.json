{"name": "nuxt-app-prod", "version": "0.0.0", "type": "module", "private": true, "dependencies": {"@babel/parser": "7.27.5", "@fastify/accept-negotiator": "1.1.0", "@firebase/app": "0.13.1", "@firebase/auth": "1.10.7", "@firebase/component": "0.6.17", "@firebase/logger": "0.4.4", "@firebase/util": "1.12.0", "@googlemaps/js-api-loader": "1.16.8", "@iconify/utils": "2.3.0", "@iconify/vue": "5.0.0", "@shikijs/core": "3.6.0", "@shikijs/engine-javascript": "3.6.0", "@shikijs/engine-oniguruma": "3.6.0", "@shikijs/langs": "3.6.0", "@shikijs/themes": "3.6.0", "@shikijs/transformers": "3.6.0", "@shikijs/types": "3.6.0", "@shikijs/vscode-textmate": "10.0.2", "@sindresorhus/is": "4.6.0", "@trysound/sax": "0.2.0", "@ungap/structured-clone": "1.3.0", "@vue/compiler-core": "3.5.16", "@vue/compiler-dom": "3.5.16", "@vue/compiler-ssr": "3.5.16", "@vue/devtools-api": "7.7.7", "@vue/devtools-kit": "7.7.7", "@vue/devtools-shared": "7.7.7", "@vue/reactivity": "3.5.16", "@vue/runtime-core": "3.5.16", "@vue/runtime-dom": "3.5.16", "@vue/server-renderer": "3.5.16", "@vue/shared": "3.5.16", "bail": "2.0.2", "better-sqlite3": "11.10.0", "bindings": "1.5.0", "birpc": "2.4.0", "boolbase": "1.0.0", "ccount": "2.0.1", "char-regex": "1.0.2", "character-entities": "2.0.2", "character-entities-html4": "2.1.0", "character-entities-legacy": "3.0.0", "character-reference-invalid": "2.0.1", "color": "4.2.3", "color-convert": "2.0.1", "color-name": "1.1.4", "color-string": "1.9.1", "comma-separated-tokens": "2.0.3", "consola": "3.4.2", "cookie": "1.0.2", "cookie-es": "1.2.2", "css-select": "5.1.0", "css-tree": "2.3.1", "css-what": "6.1.0", "csso": "5.0.5", "debug": "4.4.1", "decode-named-character-reference": "1.2.0", "defu": "6.1.4", "destr": "2.0.5", "detab": "3.0.2", "detect-libc": "2.0.4", "devalue": "5.1.1", "devlop": "1.1.0", "dom-serializer": "2.0.0", "domelementtype": "2.3.0", "domhandler": "5.0.3", "domutils": "3.2.2", "emojilib": "2.4.0", "emoticon": "4.1.0", "entities": "6.0.1", "escape-string-regexp": "5.0.0", "estree-walker": "2.0.2", "etag": "1.8.1", "extend": "3.0.2", "file-uri-to-path": "1.0.0", "firebase": "11.9.1", "flat": "6.0.1", "github-slugger": "2.0.0", "h3": "1.15.3", "hast-util-embedded": "3.0.0", "hast-util-format": "1.1.0", "hast-util-from-parse5": "8.0.3", "hast-util-has-property": "3.0.0", "hast-util-is-body-ok-link": "3.0.1", "hast-util-is-element": "3.0.0", "hast-util-minify-whitespace": "1.0.1", "hast-util-parse-selector": "4.0.0", "hast-util-phrasing": "3.0.1", "hast-util-raw": "9.1.0", "hast-util-to-html": "9.0.5", "hast-util-to-mdast": "10.1.2", "hast-util-to-parse5": "8.0.0", "hast-util-to-string": "3.0.1", "hast-util-to-text": "4.0.2", "hast-util-whitespace": "3.0.0", "hastscript": "9.0.1", "hookable": "5.5.3", "html-void-elements": "3.0.0", "html-whitespace-sensitive-tag-names": "3.0.1", "idb": "7.1.1", "image-meta": "0.2.1", "ipx": "2.1.0", "iron-webcrypto": "1.2.1", "is-absolute-url": "4.0.1", "is-alphabetical": "2.0.1", "is-alphanumerical": "2.0.1", "is-arrayish": "0.3.2", "is-decimal": "2.0.1", "is-hexadecimal": "2.0.1", "is-plain-obj": "4.1.0", "longest-streak": "3.1.0", "markdown-table": "3.0.4", "mdast-util-find-and-replace": "3.0.2", "mdast-util-from-markdown": "2.0.2", "mdast-util-gfm": "3.1.0", "mdast-util-gfm-autolink-literal": "2.0.1", "mdast-util-gfm-footnote": "2.1.0", "mdast-util-gfm-strikethrough": "2.0.0", "mdast-util-gfm-table": "2.0.0", "mdast-util-gfm-task-list-item": "2.0.0", "mdast-util-phrasing": "4.1.0", "mdast-util-to-hast": "13.2.0", "mdast-util-to-markdown": "2.1.2", "mdast-util-to-string": "4.0.0", "mdn-data": "2.0.30", "micromark": "4.0.2", "micromark-core-commonmark": "2.0.3", "micromark-extension-gfm": "3.0.0", "micromark-extension-gfm-autolink-literal": "2.1.0", "micromark-extension-gfm-footnote": "2.1.0", "micromark-extension-gfm-strikethrough": "2.1.0", "micromark-extension-gfm-table": "2.1.1", "micromark-extension-gfm-tagfilter": "2.0.0", "micromark-extension-gfm-task-list-item": "2.1.0", "micromark-factory-destination": "2.0.1", "micromark-factory-label": "2.0.1", "micromark-factory-space": "2.0.1", "micromark-factory-title": "2.0.1", "micromark-factory-whitespace": "2.0.1", "micromark-util-character": "2.1.1", "micromark-util-chunked": "2.0.1", "micromark-util-classify-character": "2.0.1", "micromark-util-combine-extensions": "2.0.1", "micromark-util-decode-numeric-character-reference": "2.0.2", "micromark-util-decode-string": "2.0.1", "micromark-util-encode": "2.0.1", "micromark-util-html-tag-name": "2.0.1", "micromark-util-normalize-identifier": "2.0.1", "micromark-util-resolve-all": "2.0.1", "micromark-util-sanitize-uri": "2.0.1", "micromark-util-subtokenize": "2.1.0", "minimark": "0.2.0", "ms": "2.1.3", "node-emoji": "2.2.0", "node-fetch-native": "1.6.6", "node-mock-http": "1.0.0", "nth-check": "2.1.1", "ofetch": "1.4.1", "oniguruma-parser": "0.12.1", "oniguruma-to-es": "4.3.3", "parse-entities": "4.0.2", "parse5": "7.3.0", "pathe": "1.1.2", "perfect-debounce": "1.0.0", "pinia": "3.0.3", "property-information": "7.1.0", "radix3": "1.1.2", "regex": "6.0.1", "regex-recursion": "6.0.2", "regex-utilities": "2.3.0", "rehype-external-links": "3.0.0", "rehype-minify-whitespace": "6.0.2", "rehype-raw": "7.0.0", "rehype-sort-attribute-values": "5.0.1", "rehype-sort-attributes": "5.0.1", "remark-emoji": "5.0.1", "remark-gfm": "4.0.1", "remark-mdc": "3.6.0", "remark-parse": "11.0.0", "remark-rehype": "11.1.2", "remark-stringify": "11.0.0", "scule": "1.3.0", "semver": "7.7.2", "sharp": "0.32.6", "shiki": "3.6.0", "simple-swizzle": "0.2.2", "skin-tone": "2.0.0", "source-map-js": "1.2.1", "space-separated-tokens": "2.0.2", "stringify-entities": "4.0.4", "supports-color": "10.0.0", "svgo": "3.3.2", "tailwindcss": "4.1.10", "trim-lines": "3.0.1", "trim-trailing-lines": "2.1.0", "trough": "2.2.0", "tslib": "2.8.1", "ufo": "1.6.1", "uncrypto": "0.1.3", "unhead": "2.0.10", "unicode-emoji-modifier-base": "1.0.0", "unified": "11.0.5", "unist-util-find-after": "5.0.0", "unist-util-is": "6.0.0", "unist-util-position": "5.0.0", "unist-util-stringify-position": "4.0.0", "unist-util-visit": "5.0.0", "unist-util-visit-parents": "6.0.1", "vfile": "6.0.3", "vfile-location": "5.0.3", "vfile-message": "4.0.2", "vue": "3.5.16", "vue-advanced-cropper": "2.8.9", "vue-bundle-renderer": "2.1.1", "vue-router": "4.5.1", "web-namespaces": "2.0.1", "yaml": "2.8.0", "zwitch": "2.0.4"}}