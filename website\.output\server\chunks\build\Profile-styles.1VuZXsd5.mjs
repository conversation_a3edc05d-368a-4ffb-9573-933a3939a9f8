const style = ".vue-advanced-cropper{direction:ltr;max-height:100%;max-width:100%;position:relative;text-align:center;-webkit-user-select:none;-moz-user-select:none;user-select:none}.vue-advanced-cropper__stretcher{max-height:100%;max-width:100%;pointer-events:none;position:relative}.vue-advanced-cropper__image{max-width:none!important;position:absolute;transform-origin:center;-webkit-user-select:none;-moz-user-select:none;user-select:none}.vue-advanced-cropper__background,.vue-advanced-cropper__foreground{background:#000;left:50%;opacity:1;position:absolute;top:50%;transform:translate(-50%,-50%)}.vue-advanced-cropper__foreground{opacity:.5}.vue-advanced-cropper__boundaries{left:50%;opacity:1;position:absolute;top:50%;transform:translate(-50%,-50%)}.vue-advanced-cropper__cropper-wrapper{height:100%;width:100%}.vue-advanced-cropper__image-wrapper{height:100%;overflow:hidden;position:absolute;width:100%}.vue-advanced-cropper__stencil-wrapper{position:absolute}.vue-simple-handler{background:#fff;display:block;height:10px;width:10px}.vue-handler-wrapper{height:30px;position:absolute;transform:translate(-50%,-50%);width:30px}.vue-handler-wrapper__draggable{align-items:center;display:flex;height:100%;justify-content:center;width:100%}.vue-handler-wrapper--west-north{cursor:nw-resize}.vue-handler-wrapper--north{cursor:n-resize}.vue-handler-wrapper--east-north{cursor:ne-resize}.vue-handler-wrapper--east{cursor:e-resize}.vue-handler-wrapper--east-south{cursor:se-resize}.vue-handler-wrapper--south{cursor:s-resize}.vue-handler-wrapper--west-south{cursor:sw-resize}.vue-handler-wrapper--west{cursor:w-resize}.vue-handler-wrapper--disabled{cursor:auto}.vue-line-wrapper{align-items:center;background:none;display:flex;justify-content:center;position:absolute}.vue-line-wrapper--north,.vue-line-wrapper--south{height:12px;left:0;transform:translateY(-50%);width:100%}.vue-line-wrapper--north{cursor:n-resize;top:0}.vue-line-wrapper--south{cursor:s-resize;top:100%}.vue-line-wrapper--east,.vue-line-wrapper--west{height:100%;top:0;transform:translate(-50%);width:12px}.vue-line-wrapper--east{cursor:e-resize;left:100%}.vue-line-wrapper--west{cursor:w-resize;left:0}.vue-line-wrapper--disabled{cursor:auto}.vue-bounding-box{height:100%;position:relative;width:100%}.vue-bounding-box__handler{position:absolute}.vue-bounding-box__handler--west-north{left:0;top:0}.vue-bounding-box__handler--north{left:50%;top:0}.vue-bounding-box__handler--east-north{left:100%;top:0}.vue-bounding-box__handler--east{left:100%;top:50%}.vue-bounding-box__handler--east-south{left:100%;top:100%}.vue-bounding-box__handler--south{left:50%;top:100%}.vue-bounding-box__handler--west-south{left:0;top:100%}.vue-bounding-box__handler--west{left:0;top:50%}.vue-draggable-area{position:relative}.vue-preview-result{box-sizing:border-box;height:100%;overflow:hidden;position:absolute;width:100%}.vue-preview-result__wrapper{position:absolute}.vue-preview-result__image{max-width:none!important;pointer-events:none;position:relative;transform-origin:center;-webkit-user-select:none;-moz-user-select:none;user-select:none}.vue-rectangle-stencil{box-sizing:border-box}.vue-rectangle-stencil,.vue-rectangle-stencil__preview{height:100%;position:absolute;width:100%}.vue-rectangle-stencil--movable{cursor:move}.vue-circle-stencil{box-sizing:content-box;cursor:move;height:100%;position:absolute;width:100%}.vue-circle-stencil__preview{border-radius:50%;height:100%;position:absolute;width:100%}.vue-circle-stencil--movable{cursor:move}.vue-preview{box-sizing:border-box;overflow:hidden;position:relative}.vue-preview--fill,.vue-preview__wrapper{height:100%;position:absolute;width:100%}.vue-preview__image{max-width:none!important;pointer-events:none;position:absolute;transform-origin:center;-webkit-user-select:none;-moz-user-select:none;user-select:none}.vue-simple-line{background:none;border:0 solid hsla(0,0%,100%,.3);transition:border .5s}.vue-simple-line--north,.vue-simple-line--south{height:0;width:100%}.vue-simple-line--east,.vue-simple-line--west{height:100%;width:0}.vue-simple-line--east{border-right-width:1px}.vue-simple-line--west{border-left-width:1px}.vue-simple-line--south{border-bottom-width:1px}.vue-simple-line--north{border-top-width:1px}.vue-simple-line--hover{border-color:#fff;opacity:1}";

const ProfileStyles_1VuZXsd5 = [style];

export { ProfileStyles_1VuZXsd5 as default };
//# sourceMappingURL=Profile-styles.1VuZXsd5.mjs.map
