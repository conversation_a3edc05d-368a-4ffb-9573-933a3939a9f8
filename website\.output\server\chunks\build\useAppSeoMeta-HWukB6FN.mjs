import { f as useRoute, g as useHead } from './server.mjs';

function useAppSeoMeta({
  title = "Bookslotz",
  description = "Bookslotz is your go-to platform for managing salon appointments.",
  image = "/og-image.jpg",
  url = ""
}) {
  const route = useRoute();
  const siteUrl = "https://www.Bookslotz.com";
  useHead({
    title,
    meta: [
      { name: "description", content: description },
      { name: "robots", content: "index, follow" },
      // Open Graph
      { property: "og:title", content: title },
      { property: "og:description", content: description },
      { property: "og:image", content: image },
      { property: "og:url", content: url || `${siteUrl}${route.fullPath}` },
      { property: "og:type", content: "website" },
      // Twitter Card
      { name: "twitter:card", content: "summary_large_image" },
      { name: "twitter:title", content: title },
      { name: "twitter:description", content: description },
      { name: "twitter:image", content: image }
    ]
  });
}

export { useAppSeoMeta as u };
//# sourceMappingURL=useAppSeoMeta-HWukB6FN.mjs.map
