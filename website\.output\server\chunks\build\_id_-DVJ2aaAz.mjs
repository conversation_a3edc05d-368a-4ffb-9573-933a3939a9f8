import { defineComponent, ref, computed, watch, mergeProps, unref, useSSRContext } from 'vue';
import { ssrRenderAttrs, ssrRenderComponent, ssrInterpolate, ssrRenderList, ssrRenderAttr, ssrIncludeBooleanAttr } from 'vue/server-renderer';
import { useRoute, useRouter } from 'vue-router';
import { _ as _export_sfc, b as useUserStore, c as useRuntimeConfig, d as useState, L as Loader, S as Snackbar, e as _sfc_main$1, i as useCookie } from './server.mjs';
import { u as useAppSeoMeta } from './useAppSeoMeta-HWukB6FN.mjs';
import '../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:url';
import '@iconify/utils';
import 'node:crypto';
import 'consola';
import 'node:path';
import 'better-sqlite3';
import 'ipx';
import 'pinia';
import 'cookie';
import '@iconify/vue';
import 'tailwindcss/colors';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import 'unhead/server';
import 'devalue';
import 'unhead/plugins';
import 'unhead/utils';

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "[id]",
  __ssrInlineRender: true,
  setup(__props) {
    const route = useRoute();
    useRouter();
    const userStore = useUserStore();
    const { public: { BASE_URL } } = useRuntimeConfig();
    const isLoading = useState("globalLoader", () => false);
    const hasError = ref(false);
    const snackbarRef = ref(null);
    const categoryId = route.params.id;
    const categoryName = computed(() => {
      const catMap = {
        "hair-styling": "Hair & Styling",
        "nail-art-extensions": "Nail Art & Extensions",
        "facials": "Facials",
        "massages": "Massages"
      };
      return catMap[categoryId] || categoryId;
    });
    const filteredSalons = ref([]);
    const noDataMessage = ref("");
    const isComponentMounted = ref(false);
    const resetData = () => {
      filteredSalons.value = [];
      noDataMessage.value = "";
      hasError.value = false;
    };
    const getImageUrl = (coverimage) => {
      if (!coverimage) return "/icon/saloon-image.jpg";
      if (coverimage.startsWith("http")) return coverimage;
      return `${BASE_URL}/wp/${coverimage}`;
    };
    const formattedCategoryName = computed(() => {
      const raw = categoryName.value;
      return raw.replace(/-/g, " ").replace(/\b\d+\b/g, "").replace(/\s+/g, " ").trim().replace(/\b\w/g, (char) => char.toUpperCase());
    });
    const fetchSalonsByCategory = async () => {
      var _a, _b, _c, _d;
      isLoading.value = true;
      hasError.value = false;
      noDataMessage.value = "";
      filteredSalons.value = [];
      try {
        const guestId = useCookie("guestId").value;
        const headers = {
          "Content-Type": "application/json"
        };
        if (userStore.token) headers["token"] = userStore.token;
        if (guestId) headers["guestId"] = guestId;
        const response = await fetch(`${BASE_URL}/v1/client/business/salon-list?categorySlug=${encodeURIComponent(categoryId)}`, {
          method: "GET",
          headers
        });
        const data = await response.json();
        if (data.status_code === 401) {
          (_a = snackbarRef.value) == null ? void 0 : _a.showSnackbar(data.msg || "Unauthorized. Logging out...");
          userStore.logout();
          return;
        }
        if (data.status && data.status_code === 200) {
          if (Array.isArray((_b = data.data) == null ? void 0 : _b.businessList)) {
            filteredSalons.value = data.data.businessList.map((salon) => ({
              id: salon._id,
              name: salon.businessname || salon.salonname || "Unknown Salon",
              location: salon.salonAddress || salon.address || "Location not available",
              rating: 4.5,
              reviews: 127,
              image: getImageUrl(salon.coverimage),
              wishlist: salon.wishlist || false,
              // Use wishlist from backend
              isWishlistLoading: false,
              // Initialize loading state
              categoryId,
              slug: salon.slug || salon._id
            }));
            if (filteredSalons.value.length > 0) {
              (_c = snackbarRef.value) == null ? void 0 : _c.showSnackbar("Salons loaded successfully");
            } else {
              noDataMessage.value = "No salons found for this category.";
            }
          } else {
            noDataMessage.value = data.msg || "No salons found for this category.";
          }
        } else if (data.status_code === 400) {
          noDataMessage.value = data.msg || "No salons found for this category.";
        } else {
          throw new _sfc_main$1(data.msg || "Failed to load salons");
        }
      } catch (error) {
        console.error("Error fetching salons:", error);
        hasError.value = true;
        (_d = snackbarRef.value) == null ? void 0 : _d.showSnackbar("Failed to load salons. Please try again.", 3e3);
      } finally {
        isLoading.value = false;
      }
    };
    useAppSeoMeta({
      title: computed(() => `Saloons for "${categoryName.value}" | Bookslotz`),
      description: computed(() => `Discover top salons for ${categoryName.value} on Bookslotz. Book appointments, view ratings, and more.`),
      image: "/og-image.jpg",
      url: computed(() => `https://www.Bookslotz.com${route.fullPath}`)
    });
    watch(() => route.params.id, (newId, oldId) => {
      if (newId !== oldId && isComponentMounted.value) {
        resetData();
        fetchSalonsByCategory();
      }
    });
    return (_ctx, _push, _parent, _attrs) => {
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "min-h-screen bg-white relative" }, _attrs))} data-v-928d6fa5>`);
      if (unref(isLoading)) {
        _push(ssrRenderComponent(Loader, null, null, _parent));
      } else {
        _push(`<!---->`);
      }
      _push(ssrRenderComponent(Snackbar, {
        ref_key: "snackbarRef",
        ref: snackbarRef
      }, null, _parent));
      if (hasError.value) {
        _push(ssrRenderComponent(_sfc_main$1, { error: { statusCode: 500, message: "Something went wrong. Please try again later." } }, null, _parent));
      } else {
        _push(`<!---->`);
      }
      if (!hasError.value) {
        _push(`<section class="py-8 bg-gray-50 min-h-screen" data-v-928d6fa5><div class="max-w-8xl mx-auto px-6 sm:px-16 md:px-20 space-y-12" data-v-928d6fa5><div data-v-928d6fa5><h2 class="text-2xl font-bold text-gray-900 mb-6" data-v-928d6fa5> Saloons for &quot;${ssrInterpolate(formattedCategoryName.value)}&quot; </h2>`);
        if (filteredSalons.value.length) {
          _push(`<div data-v-928d6fa5><div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-4 gap-4" data-v-928d6fa5><!--[-->`);
          ssrRenderList(filteredSalons.value, (salon, i) => {
            _push(`<div class="bg-white rounded-2xl overflow-hidden shadow-sm hover:shadow-lg transition-all duration-300 cursor-pointer group" data-v-928d6fa5><div class="relative aspect-[4/3] overflow-hidden" data-v-928d6fa5><img${ssrRenderAttr("src", salon.image)}${ssrRenderAttr("alt", salon.name)} class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300" data-v-928d6fa5><button class="absolute bottom-3 right-3 w-8 h-8 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center shadow-sm hover:bg-white transition-all duration-200 disabled:opacity-50"${ssrIncludeBooleanAttr(salon.isWishlistLoading) ? " disabled" : ""} data-v-928d6fa5>`);
            if (salon.isWishlistLoading) {
              _push(`<div class="w-4 h-4 border-2 border-gray-300 border-t-red-500 rounded-full animate-spin" data-v-928d6fa5></div>`);
            } else if (salon.wishlist) {
              _push(`<svg class="w-4 h-4 text-red-500" fill="currentColor" viewBox="0 0 20 20" data-v-928d6fa5><path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd" data-v-928d6fa5></path></svg>`);
            } else {
              _push(`<svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-928d6fa5><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" data-v-928d6fa5></path></svg>`);
            }
            _push(`</button></div><div class="p-4 space-y-2" data-v-928d6fa5><h3 class="font-semibold text-gray-900 text-base leading-tight line-clamp-1" data-v-928d6fa5>${ssrInterpolate(salon.name)}</h3><div class="flex items-center text-gray-500 text-sm" data-v-928d6fa5><svg class="w-4 h-4 mr-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-928d6fa5><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" data-v-928d6fa5></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" data-v-928d6fa5></path></svg><span class="line-clamp-1" data-v-928d6fa5>${ssrInterpolate(salon.location)}</span></div><div class="flex items-center space-x-1" data-v-928d6fa5><svg class="w-4 h-4 text-yellow-400 fill-current" viewBox="0 0 20 20" data-v-928d6fa5><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" data-v-928d6fa5></path></svg><span class="font-semibold text-gray-900 text-sm" data-v-928d6fa5>${ssrInterpolate(salon.rating)}</span><span class="text-gray-500 text-sm" data-v-928d6fa5>(${ssrInterpolate(salon.reviews)})</span></div></div></div>`);
          });
          _push(`<!--]--></div></div>`);
        } else {
          _push(`<div class="text-center py-12" data-v-928d6fa5><div class="text-gray-400 mb-4" data-v-928d6fa5><svg class="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-928d6fa5><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" data-v-928d6fa5></path></svg></div><p class="text-gray-500 text-lg text-center" data-v-928d6fa5>${ssrInterpolate(noDataMessage.value || "No salons found for this category.")}</p></div>`);
        }
        _push(`</div></div></section>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div>`);
    };
  }
});
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/category/[id].vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const _id_ = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-928d6fa5"]]);

export { _id_ as default };
//# sourceMappingURL=_id_-DVJ2aaAz.mjs.map
