import { defineComponent, mergeProps, unref, useSSRContext } from 'vue';
import { ssrRenderAttrs, ssrRenderComponent, ssrRenderStyle, ssrRenderAttr, ssrRenderList } from 'vue/server-renderer';
import { u as useSearchStore } from './search-CN1Jpzjq.mjs';
import { _ as _export_sfc, u as useSeoMeta } from './server.mjs';
import { p as publicAssetsURL } from '../_/nitro.mjs';
import 'pinia';
import 'vue-router';
import 'cookie';
import '@iconify/vue';
import 'tailwindcss/colors';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import 'unhead/server';
import 'devalue';
import 'unhead/plugins';
import 'unhead/utils';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:url';
import '@iconify/utils';
import 'node:crypto';
import 'consola';
import 'node:path';
import 'better-sqlite3';
import 'ipx';

const _sfc_main$6 = /* @__PURE__ */ defineComponent({
  __name: "BannerSection",
  __ssrInlineRender: true,
  setup(__props) {
    const searchStore = useSearchStore();
    return (_ctx, _push, _parent, _attrs) => {
      _push(`<section${ssrRenderAttrs(mergeProps({
        class: "relative w-full min-h-[600px] bg-center bg-cover bg-no-repeat flex items-center justify-center",
        style: { "background-image": "url('/icon/banner-image.png')" }
      }, _attrs))}><div class="pt-30 sm:pt-2 pb-8 -mt-20"><div class="max-w-4xl mx-auto px-4 text-center"><div class="text-4xl sm:text-[55px] font-[700] text-gray-900 leading-snug dm-sansmedium"> Book Your Salon <span class="text-purple-600">Experience</span>, <br> Anytime, Anywhere </div><p class="mt-4 text-lg text-[18px] text-[#000000] font-normal"> Discover top salons near you and book your beauty &amp; wellness appointments in seconds \u2014 no waiting, no calls, just pure convenience. </p><div class="mt-8 flex flex-col sm:flex-row items-center justify-center gap-3 max-w-2xl mx-auto"><div class="relative w-full sm:flex-1"><div class="p-[2px] rounded-full" style="${ssrRenderStyle({ "background": "linear-gradient(90deg, #7b27e766 0%, #e0e0e0 100%)" })}"><div class="relative rounded-full bg-white w-full"><span class="absolute left-3 top-1/2 transform -translate-y-1/2 flex items-center justify-center rounded-full bg-[#7B27E7] w-9 h-9"><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="#fff"><path d="M15.5 14h-.79l-.28-.27A6.47 6.47 0 0 0 16 9.5A6.5 6.5 0 1 0 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5S14 7.01 14 9.5S11.99 14 9.5 14"></path></svg></span><input type="text" placeholder="Search for salons, services, locations..." class="w-full pl-14 pr-4 py-3 rounded-full bg-white focus:outline-none focus:ring-2 focus:ring-purple-500 border-none"${ssrRenderAttr("value", unref(searchStore).searchQuery)}></div></div></div><button class="px-6 py-3 bg-purple-600 text-white font-semibold rounded-full hover:bg-purple-700 transition w-full sm:w-auto cursor-pointer"> Search </button></div></div></div></section>`);
    };
  }
});
const _sfc_setup$6 = _sfc_main$6.setup;
_sfc_main$6.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("components/landing/BannerSection.vue");
  return _sfc_setup$6 ? _sfc_setup$6(props, ctx) : void 0;
};
const _imports_0$2 = publicAssetsURL("/images/phone-advertisement.png");
const _sfc_main$5 = /* @__PURE__ */ defineComponent({
  __name: "DownloadSection",
  __ssrInlineRender: true,
  setup(__props) {
    return (_ctx, _push, _parent, _attrs) => {
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "min-h-screen bg-white overflow-hidden" }, _attrs))} data-v-b976a0a8><div class="container mx-auto px-6 py-0 lg:py-0" data-v-b976a0a8><div class="flex flex-col lg:flex-row items-center justify-between gap-12 lg:gap-0 rounded-3xl" style="${ssrRenderStyle({ "background-color": "#FAF7FF" })}" data-v-b976a0a8><div class="flex flex-col lg:flex-row items-center justify-center mx-4 sm:mx-24 gap-0 lg:gap-20 py-10" data-v-b976a0a8><div class="flex-1 flex justify-center lg:justify-center pt-10 lg:pt-10 lg:basis-1/3 lg:max-w-1/3" data-v-b976a0a8><div class="phone-mockup" data-v-b976a0a8><img${ssrRenderAttr("src", _imports_0$2)} alt="Salon Booking App" class="h-auto max-w-full drop-shadow-2xl" data-v-b976a0a8></div></div><div class="flex-1 text-left space-y-6 justify-start lg:basis-2/3 lg:max-w-2/3" data-v-b976a0a8><div data-v-b976a0a8><h1 class="font-bold text-black leading-tight" style="${ssrRenderStyle({ "font-size": "35px" })}" data-v-b976a0a8> Discover and Book the Best <span class="text-purple-600" data-v-b976a0a8> Salons </span> Anytime, Anywhere \u2014 Right from Your Phone! </h1></div><div data-v-b976a0a8><p class="text-gray-600 leading-relaxed max-w-2xl" style="${ssrRenderStyle({ "font-size": "16px" })}" data-v-b976a0a8> Bookslotz is your all-in-one solution for discovering, booking, and managing salon appointments with ease. Whether you&#39;re looking for a quick trim, a luxurious spa treatment, or your favorite stylist, our app helps you find top-rated salons, view available time slots, and secure your booking in seconds. With real-time updates, exclusive offers, and personalized recommendations, Salonslot\u0240 puts the power of self-care right in your pocket \u2014 anytime, anywhere. </p></div><div data-v-b976a0a8><button class="bg-purple-600 hover:bg-purple-700 text-white font-semibold px-6 py-3 rounded-full text-[14px] transition-all duration-300 transform hover:scale-105 shadow-lg" data-v-b976a0a8> Download the App now </button></div><div class="flex flex-row gap-3 justify-start mt-6" data-v-b976a0a8><a href="#" class="inline-block transition-transform hover:scale-105" data-v-b976a0a8><img src="https://upload.wikimedia.org/wikipedia/commons/7/78/Google_Play_Store_badge_EN.svg" alt="Get it on Google Play" class="h-10 w-auto" data-v-b976a0a8></a><a href="#" class="inline-block transition-transform hover:scale-105" data-v-b976a0a8><img src="https://upload.wikimedia.org/wikipedia/commons/3/3c/Download_on_the_App_Store_Badge.svg" alt="Download on the App Store" class="h-10 w-auto" data-v-b976a0a8></a></div></div></div></div></div></div>`);
    };
  }
});
const _sfc_setup$5 = _sfc_main$5.setup;
_sfc_main$5.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("components/landing/DownloadSection.vue");
  return _sfc_setup$5 ? _sfc_setup$5(props, ctx) : void 0;
};
const DownloadSection = /* @__PURE__ */ _export_sfc(_sfc_main$5, [["__scopeId", "data-v-b976a0a8"]]);
const _sfc_main$4 = /* @__PURE__ */ defineComponent({
  __name: "countSection",
  __ssrInlineRender: true,
  setup(__props) {
    return (_ctx, _push, _parent, _attrs) => {
      _push(`<section${ssrRenderAttrs(mergeProps({ class: "py-10 px-4 section-gradient-bottom" }, _attrs))} data-v-7db0aa52><div class="max-w-6xl mx-auto text-center" data-v-7db0aa52><h1 class="text-[35px] md:text-[35px] lg:text-[35px] font-[700] text-gray-900 mb-6 leading-tight dm-sansmedium" data-v-7db0aa52> The go-to platform for beauty and grooming appointments </h1><p class="text-[25px] md:text-[25px] text-gray-900 mb-16 mx-auto leading-relaxed font-[500] dm-sansmedium" data-v-7db0aa52> One powerful app. One seamless solution. Trusted by the top names in salons and spas. </p></div></section>`);
    };
  }
});
const _sfc_setup$4 = _sfc_main$4.setup;
_sfc_main$4.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("components/landing/countSection.vue");
  return _sfc_setup$4 ? _sfc_setup$4(props, ctx) : void 0;
};
const CountSection = /* @__PURE__ */ _export_sfc(_sfc_main$4, [["__scopeId", "data-v-7db0aa52"]]);
const _imports_0$1 = publicAssetsURL("/images/riya-avatar.png");
const _sfc_main$3 = /* @__PURE__ */ defineComponent({
  __name: "TestimonialSection",
  __ssrInlineRender: true,
  props: {
    heroImage: { default: "/images/beauty-clients.png" },
    testimonialImage: { default: "/images/testimonial-section.png" },
    altTexts: { default: () => ({
      hero: "Happy beauty salon clients",
      testimonial: "Customer testimonial - Riya Sharma review"
    }) }
  },
  setup(__props) {
    const props = __props;
    useSeoMeta({
      title: "Bookslotz - Where Beauty Meets Loyalty",
      ogTitle: "Bookslotz - Where Beauty Meets Loyalty",
      description: "Trusted by thousands of happy clients. Building lasting relationships\u2014one appointment at a time.",
      ogDescription: "Trusted by thousands of happy clients. Building lasting relationships\u2014one appointment at a time.",
      ogImage: "/og-image.png",
      twitterCard: "summary_large_image"
    });
    return (_ctx, _push, _parent, _attrs) => {
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "min-h-screen bg-gray-50 py-8 px-4" }, _attrs))} data-v-0585aade><div class="max-w-7xl mx-auto" data-v-0585aade><div class="grid lg:grid-cols-2 gap-8 lg:gap-1 items-start" data-v-0585aade><div class="relative lg:col-span-1" data-v-0585aade><img${ssrRenderAttr("src", props.heroImage)}${ssrRenderAttr("alt", props.altTexts.hero)} class="w-full h-auto object-cover" data-v-0585aade><div class="absolute bottom-6 right-6 max-w-xs" data-v-0585aade><div class="flex items-start space-x-3 bg-white/90 backdrop-blur-sm p-4 rounded-xl shadow-lg border border-white/30" data-v-0585aade><div class="flex-shrink-0" data-v-0585aade><img${ssrRenderAttr("src", _imports_0$1)} alt="Riya Sharma" class="w-12 h-12 rounded-full object-cover border-2 border-white" data-v-0585aade></div><div class="flex-1" data-v-0585aade><div class="space-y-1" data-v-0585aade><h3 class="font-semibold text-gray-900 text-sm" data-v-0585aade>Riya Sharma</h3><p class="text-gray-700 text-xs italic leading-relaxed" data-v-0585aade> &quot;Bookslotz makes booking appointments so easy!&quot; </p><div class="flex space-x-1 mt-2" data-v-0585aade><!--[-->`);
      ssrRenderList(5, (i) => {
        _push(`<svg class="w-3 h-3 text-yellow-400 fill-current" viewBox="0 0 20 20" data-v-0585aade><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" data-v-0585aade></path></svg>`);
      });
      _push(`<!--]--></div></div></div></div></div></div><div class="space-y-2 pt-0 lg:pt-30 lg:col-span-1 p-2 md:p-0" data-v-0585aade><div class="space-y-4" data-v-0585aade><span class="text-[35px] lg:text-[35px] xl:text-[35px] font-bold text-gray-900 leading-tight" data-v-0585aade> Where Beauty Meets Loyalty: <p data-v-0585aade><span class="text-[35px] lg:text-[35px] xl:text-[35px] font-bold text-gray-900" data-v-0585aade>Trusted by </span><span class="text-purple-600" data-v-0585aade>Thousands</span> of Happy Clients</p></span></div><div class="text-[25px] lg:text-[25px] font-semibold text-gray-800" data-v-0585aade><span data-v-0585aade> Building lasting relationships\u2014one appointment at a time </span></div><div class="text-gray-600 text-[16px] lg:text-[16px] leading-relaxed space-y-4 mt-3" data-v-0585aade><p data-v-0585aade> At Bookslotz, customer happiness is at the heart of everything we do. With thousands of 5-star reviews and loyal clients who keep coming back, we&#39;ve created more than just a booking platform\u2014we&#39;ve built a space where trust, care, and satisfaction thrive. From seamless scheduling to personalized experiences, Bookslotz helps salons deliver top-notch service and build genuine connections with their customers. </p></div></div></div></div></div>`);
    };
  }
});
const _sfc_setup$3 = _sfc_main$3.setup;
_sfc_main$3.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("components/landing/TestimonialSection.vue");
  return _sfc_setup$3 ? _sfc_setup$3(props, ctx) : void 0;
};
const TestimonialSection = /* @__PURE__ */ _export_sfc(_sfc_main$3, [["__scopeId", "data-v-0585aade"]]);
const _imports_0 = publicAssetsURL("/images/analytics-mockup.png");
const _sfc_main$2 = {};
function _sfc_ssrRender$1(_ctx, _push, _parent, _attrs) {
  _push(`<section${ssrRenderAttrs(mergeProps({ class: "relative bg-white overflow-hidden" }, _attrs))}><div class="absolute inset-0 bg-gradient-to-tr from-[#f1ecf7] via-[#ece3f6] to-[#7B27E7] opacity-30 z-0"></div><div class="relative z-10 mx-auto px-4 lg:px-0 grid grid-cols-1 lg:grid-cols-2 items-center"><div><div class="px-2 mt-4 mb-4 lg:px-20"><h1 class="text-[35px] font-bold text-gray-900 leading-tight"> All-in-One Salon <span class="text-purple-600">App</span> for Clients &amp; Businesses </h1><div class="mt-6 space-y-4 text-gray-800 text-base leading-relaxed"><p><span class="font-semibold text-black text-[25px]">Book it. <span class="text-purple-600">Love it.</span></span></p><p><span class="text-[16px]">Find and book top salons, spas, and stylists near you \u2014 fast, easy, and reliable.</span></p><p><span class="font-semibold text-black text-[25px]">Run it. Grow it.</span></p><p><span class="text-[16px]">Salon owners can manage bookings, staff, payments, and more \u2014 all from one smart dashboard.</span></p><p class="font-semibold text-black text-[16px]"> One powerful app. Whether you\u2019re getting styled or running the style. </p></div><button class="mt-8 bg-purple-600 hover:bg-purple-700 text-white font-medium px-6 py-3 rounded-full shadow-lg transition"> List your business </button></div></div><div class="flex justify-center lg:justify-end lg:pr-0 lg:pl-8"><img${ssrRenderAttr("src", _imports_0)} alt="Salon App Preview" class="max-w-full h-auto lg:rounded-none"></div></div></section>`);
}
const _sfc_setup$2 = _sfc_main$2.setup;
_sfc_main$2.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("components/landing/AnalyticsSection.vue");
  return _sfc_setup$2 ? _sfc_setup$2(props, ctx) : void 0;
};
const AnalyticsSection = /* @__PURE__ */ _export_sfc(_sfc_main$2, [["ssrRender", _sfc_ssrRender$1]]);
const _sfc_main$1 = {};
function _sfc_ssrRender(_ctx, _push, _parent, _attrs) {
  _push(`<section${ssrRenderAttrs(mergeProps({ class: "bg-white py-16" }, _attrs))}><div class="mx-auto px-4 pl-5 sm:pl-1 md:pl-20"><h2 class="text-[22px] font-[500] dm-sansmedium text-gray-900 mb-8"> Top saloons in you Mumbai </h2><div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 xl:grid-cols-4 gap-8 text-sm text-gray-700"><div><h3 class="text-[20px] font-[500] dm-sansmedium text-black mb-2">Bandra West</h3><ul class="space-y-4 text-[16px]"><li>Hair Salons in Bandra West</li><li>Nail Salons in Bandra West</li><li>Barbershops in Bandra West</li><li>Beauty Salons in Bandra West</li><li>Spas in Bandra West</li></ul></div><div><h3 class="text-[20px] font-[500] dm-sansmedium text-black mb-2">Andheri West</h3><ul class="space-y-4 text-[16px]"><li>Hair Salons in Andheri West</li><li>Nail Salons in Andheri West</li><li>Barbershops in Andheri West</li><li>Beauty Salons in Andheri West</li><li>Spas in Andheri West</li></ul></div><div><h3 class="text-[20px] font-[500] dm-sansmedium text-black mb-2">Colaba</h3><ul class="space-y-4 text-[16px]"><li>Hair Salons in Colaba</li><li>Nail Salons in Colaba</li><li>Barbershops in Colaba</li><li>Beauty Salons in Colaba</li><li>Spas in Colaba</li></ul></div><div><h3 class="text-[20px] font-[500] dm-sansmedium text-black mb-2">Powai</h3><ul class="space-y-4 text-[16px]"><li>Hair Salons in Powai</li><li>Nail Salons in Powai</li><li>Barbershops in Powai</li><li>Beauty Salons in Powai</li><li>Spas in Powai</li></ul></div></div></div></section>`);
}
const _sfc_setup$1 = _sfc_main$1.setup;
_sfc_main$1.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("components/landing/TopSaloonSection.vue");
  return _sfc_setup$1 ? _sfc_setup$1(props, ctx) : void 0;
};
const TopSaloonSection = /* @__PURE__ */ _export_sfc(_sfc_main$1, [["ssrRender", _sfc_ssrRender]]);
const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "index",
  __ssrInlineRender: true,
  setup(__props) {
    return (_ctx, _push, _parent, _attrs) => {
      _push(`<div${ssrRenderAttrs(_attrs)}>`);
      _push(ssrRenderComponent(_sfc_main$6, null, null, _parent));
      _push(ssrRenderComponent(DownloadSection, null, null, _parent));
      _push(ssrRenderComponent(CountSection, null, null, _parent));
      _push(ssrRenderComponent(TestimonialSection, null, null, _parent));
      _push(ssrRenderComponent(AnalyticsSection, null, null, _parent));
      _push(ssrRenderComponent(TopSaloonSection, null, null, _parent));
      _push(`</div>`);
    };
  }
});
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/index.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};

export { _sfc_main as default };
//# sourceMappingURL=index-_THk10HF.mjs.map
