import { defineComponent, ref, computed, watch, mergeProps, useSSRContext } from 'vue';
import { ssrRenderAttrs, ssrRenderStyle, ssrRenderAttr, ssrInterpolate, ssrRenderClass, ssrIncludeBooleanAttr } from 'vue/server-renderer';
import { useRouter } from 'vue-router';
import { u as useBookingStore } from './booking-D2Du41fq.mjs';
import { _ as _export_sfc, g as useHead } from './server.mjs';
import 'pinia';
import '../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:url';
import '@iconify/utils';
import 'node:crypto';
import 'consola';
import 'node:path';
import 'better-sqlite3';
import 'ipx';
import 'cookie';
import '@iconify/vue';
import 'tailwindcss/colors';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import 'unhead/server';
import 'devalue';
import 'unhead/plugins';
import 'unhead/utils';

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "payment",
  __ssrInlineRender: true,
  setup(__props) {
    useRouter();
    const bookingStore = useBookingStore();
    const quantity = ref(1);
    const originalGiftCardPrice = ref(0);
    const showSuccessModal = ref(false);
    const selectedGiftCard = computed(() => {
      const membership = bookingStore.selectedMembership;
      if (!membership || bookingStore.bookingType !== "giftcard") return null;
      return {
        id: membership.id,
        brand: membership.name,
        description: membership.type,
        amount: originalGiftCardPrice.value || membership.price,
        expiry: membership.expiry,
        gradient: membership.color,
        pattern: "/images/gift-card.jpg",
        // Default pattern
        salon: membership.salon
      };
    });
    const totalAmount = computed(() => {
      return selectedGiftCard.value ? selectedGiftCard.value.amount * quantity.value : 0;
    });
    const canProceedToPayment = computed(() => {
      return selectedGiftCard.value && quantity.value > 0 && bookingStore.bookingType === "giftcard";
    });
    function updateTotalInStore() {
      if (selectedGiftCard.value && bookingStore.selectedMembership) {
        const updatedGiftCard = {
          ...bookingStore.selectedMembership,
          price: originalGiftCardPrice.value * quantity.value
        };
        bookingStore.setGiftCard(updatedGiftCard);
      }
    }
    function formatExpiryDate(expiry) {
      let expiryMonths = 6;
      const match = expiry.match(/(\d+)\s*(month|year)/i);
      if (match) {
        const value = parseInt(match[1]);
        const unit = match[2].toLowerCase();
        expiryMonths = unit === "year" ? value * 12 : value;
      }
      const expiryDate = /* @__PURE__ */ new Date();
      expiryDate.setMonth(expiryDate.getMonth() + expiryMonths);
      return expiryDate.toLocaleDateString("en-IN", {
        day: "2-digit",
        month: "2-digit",
        year: "numeric"
      });
    }
    watch(quantity, (newQuantity) => {
      updateTotalInStore();
    });
    useHead({
      title: "Gift Card Payment | Elite Beauty Salon",
      meta: [
        {
          name: "description",
          content: "Complete your gift card purchase for Elite Beauty Salon. Secure payment for premium beauty services."
        },
        {
          name: "keywords",
          content: "gift card payment, beauty salon payment, salon voucher, Elite Beauty Salon"
        }
      ]
    });
    return (_ctx, _push, _parent, _attrs) => {
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "min-h-screen bg-white" }, _attrs))} data-v-2a974eea><div class="flex flex-col xl:flex-row max-w-8xl mx-auto px-1 sm:px-12 xl:justify-center xl:min-h-screen" data-v-2a974eea><div class="flex-1 sm:flex-[0.6] lg:flex-[0.4] px-6 lg:px-8 py-6 lg:py-8" data-v-2a974eea><div class="flex items-center space-x-3 mb-3" data-v-2a974eea><button class="w-10 h-10 rounded-full border border-gray-300 flex items-center justify-center group cursor-pointer" data-v-2a974eea><svg xmlns="http://www.w3.org/2000/svg" class="w-7 h-7 text-purple-600 group-hover:text-purple-700 transition-colors" viewBox="0 0 24 24" fill="currentColor" data-v-2a974eea><path d="m6.523 12.5l3.735 3.735q.146.146.153.344q.006.198-.153.363q-.166.166-.357.168t-.357-.162l-4.382-4.383q-.243-.242-.243-.565t.243-.566l4.382-4.382q.147-.146.347-.153q.201-.007.367.159q.16.165.162.353q.003.189-.162.354L6.523 11.5h12.38q.214 0 .358.143t.143.357t-.143.357t-.357.143z" data-v-2a974eea></path></svg></button><h1 class="text-[25px] font-semibold text-[700]" data-v-2a974eea>Buy a Gift Card</h1></div>`);
      if (selectedGiftCard.value) {
        _push(`<div class="border border-gray-200 rounded-2xl p-6 mb-6" data-v-2a974eea><div class="relative p-6 rounded-2xl overflow-hidden mb-6 h-55 sm:h-48 flex flex-col justify-between" style="${ssrRenderStyle({ background: selectedGiftCard.value.gradient })}" data-v-2a974eea><img${ssrRenderAttr("src", selectedGiftCard.value.pattern)} alt="Pattern" class="absolute inset-0 w-full h-full object-cover opacity-20 pointer-events-none" data-v-2a974eea><div class="relative z-10 text-white h-full flex flex-col justify-between" data-v-2a974eea><div class="text-center sm:text-left" data-v-2a974eea><h3 class="text-[20px] font-medium" data-v-2a974eea>${ssrInterpolate(selectedGiftCard.value.brand)}</h3></div><div class="flex flex-col items-center text-center sm:flex-row sm:items-end sm:justify-between sm:text-left sm:space-x-4" data-v-2a974eea><div class="mb-4 sm:mb-0" data-v-2a974eea><div class="text-[32px] font-bold leading-none mb-1" data-v-2a974eea> \u20B9${ssrInterpolate(selectedGiftCard.value.amount.toLocaleString())}</div><p class="text-[16px] opacity-90" data-v-2a974eea>${ssrInterpolate(selectedGiftCard.value.description)}</p></div><div class="sm:text-right" data-v-2a974eea><p class="text-[14px] opacity-90" data-v-2a974eea>Expires on</p><p class="text-[14px] font-medium" data-v-2a974eea>${ssrInterpolate(formatExpiryDate(selectedGiftCard.value.expiry))}</p></div></div></div></div><div data-v-2a974eea><h2 class="text-[16px] font-medium text-gray-900 mb-4 text-center" data-v-2a974eea>Quantity</h2><div class="flex items-center justify-center" data-v-2a974eea><div class="flex items-center h-14 bg-white rounded-2xl overflow-hidden" data-v-2a974eea><button${ssrIncludeBooleanAttr(quantity.value <= 1) ? " disabled" : ""} class="${ssrRenderClass([quantity.value <= 1 ? "bg-gray-400 cursor-not-allowed" : "bg-gray-500 hover:bg-gray-600 cursor-pointer", "w-14 h-full flex items-center justify-center transition-colors"])}" data-v-2a974eea><svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="3" data-v-2a974eea><path stroke-linecap="round" stroke-linejoin="round" d="M20 12H4" data-v-2a974eea></path></svg></button><div class="w-40 h-full flex items-center justify-center bg-white" data-v-2a974eea><span class="text-[16px] font-medium text-gray-900" data-v-2a974eea>${ssrInterpolate(quantity.value.toString().padStart(2, "0"))}</span></div><button class="w-14 h-full flex items-center justify-center bg-purple-600 hover:bg-purple-700 transition-colors cursor-pointer" data-v-2a974eea><svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="3" data-v-2a974eea><path stroke-linecap="round" stroke-linejoin="round" d="M12 4v16m8-8H4" data-v-2a974eea></path></svg></button></div></div></div></div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div><div class="w-full sm:flex-[0.4] lg:flex-[0.4] xl:w-[400px] bg-white p-6 lg:p-8 xl:p-0 mt-0 xl:mt-20" data-v-2a974eea><div class="bg-white rounded-2xl shadow-sm border border-gray-100 p-6 space-y-6" data-v-2a974eea>`);
      if (selectedGiftCard.value) {
        _push(`<div data-v-2a974eea><div class="flex items-center gap-3 p-3 rounded-xl bg-white border border-gray-100" data-v-2a974eea><div class="w-16 h-16 rounded-lg bg-gray-200 flex items-center justify-center overflow-hidden flex-shrink-0" data-v-2a974eea><img${ssrRenderAttr("src", selectedGiftCard.value.salon.image)} alt="salon" class="w-full h-full object-cover" data-v-2a974eea></div><div class="flex-1 min-w-0" data-v-2a974eea><h3 class="text-[18px] font-semibold text-gray-900 truncate" data-v-2a974eea>${ssrInterpolate(selectedGiftCard.value.salon.name)}</h3><div class="flex items-center gap-1 text-[14px] text-gray-500" data-v-2a974eea><svg class="w-4 h-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-2a974eea><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" data-v-2a974eea></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" data-v-2a974eea></path></svg><span class="truncate" data-v-2a974eea>${ssrInterpolate(selectedGiftCard.value.salon.location)}</span></div></div></div></div>`);
      } else {
        _push(`<!---->`);
      }
      if (selectedGiftCard.value) {
        _push(`<div class="space-y-5" data-v-2a974eea><div class="pb-4" data-v-2a974eea><h3 class="text-[16px] font-semibold text-gray-900 mb-1" data-v-2a974eea>${ssrInterpolate(selectedGiftCard.value.description)}</h3><div class="space-y-2 sm:flex sm:items-center sm:justify-between sm:space-y-0 mb-1" data-v-2a974eea><div class="flex items-center gap-2 text-[15px] text-gray-600" data-v-2a974eea><span class="font-semibold" data-v-2a974eea>${ssrInterpolate(quantity.value)}X</span><div class="w-1 h-1 bg-gray-400 rounded-full" data-v-2a974eea></div><div class="flex items-center gap-1" data-v-2a974eea><svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-2a974eea><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" data-v-2a974eea></path></svg><span data-v-2a974eea>Valid for ${ssrInterpolate(selectedGiftCard.value.expiry)}</span></div></div><div class="hidden sm:block text-[18px] font-semibold text-gray-900" data-v-2a974eea> \u20B9${ssrInterpolate(totalAmount.value.toLocaleString())}</div></div><div class="text-[14px] text-gray-500" data-v-2a974eea> Expires on ${ssrInterpolate(formatExpiryDate(selectedGiftCard.value.expiry))}</div></div></div>`);
      } else {
        _push(`<div class="text-center py-12" data-v-2a974eea><div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4" data-v-2a974eea><svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-2a974eea><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" data-v-2a974eea></path></svg></div><p class="text-[16px] text-gray-500" data-v-2a974eea>Select a gift card to continue</p></div>`);
      }
      if (selectedGiftCard.value) {
        _push(`<div class="border-t border-gray-100 pt-6" data-v-2a974eea><div class="flex justify-between items-center mb-6" data-v-2a974eea><span class="text-[20px] font-semibold text-gray-900" data-v-2a974eea>Total</span><span class="text-[20px] font-semibold text-gray-900" data-v-2a974eea> \u20B9${ssrInterpolate(totalAmount.value.toLocaleString())}</span></div><button${ssrIncludeBooleanAttr(!canProceedToPayment.value) ? " disabled" : ""} class="${ssrRenderClass([canProceedToPayment.value ? "bg-purple-600 hover:bg-purple-700 hover:shadow-lg" : "bg-gray-300 cursor-not-allowed", "w-full py-4 rounded-full text-white font-semibold text-[16px] transition-all duration-200 cursor-pointer"])}" data-v-2a974eea> Pay Now </button></div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div></div></div>`);
      if (showSuccessModal.value) {
        _push(`<div class="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-3 sm:p-4" data-v-2a974eea><div class="bg-white rounded-2xl sm:rounded-3xl w-full max-w-2xl sm:max-w-3xl mx-auto text-center relative overflow-hidden" style="${ssrRenderStyle({ "max-height": "90vh" })}" data-v-2a974eea><div class="p-6 sm:p-8" data-v-2a974eea><div class="relative mb-6 sm:mb-8 flex justify-center" data-v-2a974eea><div class="absolute -top-3 sm:-top-4 -left-6 sm:-left-8 w-2 sm:w-3 h-2 sm:h-3 bg-red-400 rounded-full animate-float-1" data-v-2a974eea></div><div class="absolute -top-4 sm:-top-6 left-8 sm:left-12 w-1.5 sm:w-2 h-1.5 sm:h-2 bg-orange-400 rounded-full animate-float-2" data-v-2a974eea></div><div class="absolute top-1 sm:top-2 -right-6 sm:-right-8 w-3 sm:w-4 h-3 sm:h-4 bg-pink-400 rounded-full animate-float-3" data-v-2a974eea></div><div class="absolute top-8 sm:top-12 -left-4 sm:-left-6 w-1.5 sm:w-2 h-1.5 sm:h-2 bg-blue-400 rounded-full animate-float-4" data-v-2a974eea></div><div class="absolute -bottom-1 sm:-bottom-2 -right-3 sm:-right-4 w-2 sm:w-3 h-2 sm:h-3 bg-green-400 rounded-full animate-float-5" data-v-2a974eea></div><div class="absolute -bottom-3 sm:-bottom-4 left-6 sm:left-8 w-1.5 sm:w-2 h-1.5 sm:h-2 bg-purple-400 rounded-full animate-float-6" data-v-2a974eea></div><div class="relative" data-v-2a974eea><div class="absolute inset-0 w-16 sm:w-20 h-16 sm:h-20 border-2 border-purple-200 rounded-full animate-pulse-ring opacity-30" data-v-2a974eea></div><div class="absolute inset-0.5 sm:inset-1 w-15 sm:w-18 h-15 sm:h-18 border-2 border-purple-300 rounded-full animate-pulse-ring-delayed opacity-20" data-v-2a974eea></div><div class="relative w-16 sm:w-20 h-16 sm:h-20 bg-gradient-to-br from-purple-500 to-purple-700 rounded-full flex items-center justify-center shadow-lg animate-scale-in" data-v-2a974eea><svg xmlns="http://www.w3.org/2000/svg" class="w-8 sm:w-10 h-8 sm:h-10 text-white" viewBox="0 0 24 24" fill="currentColor" data-v-2a974eea><path d="M20 6h-2.18c.11-.31.18-.65.18-1a2.996 2.996 0 0 0-5.5-1.65l-.5.67l-.5-.68C10.96 2.54 10.05 2 9 2C7.34 2 6 3.34 6 5c0 .35.07.69.18 1H4c-1.11 0-1.99.89-1.99 2L2 19c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V8c0-1.11-.89-2-2-2m-5-2c.55 0 1 .45 1 1s-.45 1-1 1s-1-.45-1-1s.45-1 1-1M9 4c.55 0 1 .45 1 1s-.45 1-1 1s-1-.45-1-1s.45-1 1-1m11 15H4v-2h16zm0-5H4V8h5.08L7 10.83L8.62 12L11 8.76l1-1.36l1 1.36L15.38 12L17 10.83L14.92 8H20z" data-v-2a974eea></path></svg></div></div></div><h2 class="text-xl sm:text-2xl font-bold text-gray-900 mb-2 sm:mb-3 animate-fade-in-up leading-tight" data-v-2a974eea> Gift Card Added Successfully! </h2><p class="text-gray-600 text-sm sm:text-base mb-6 sm:mb-8 leading-relaxed animate-fade-in-up-delayed px-2 sm:px-0" data-v-2a974eea> Your gift card has been activated and is ready to use. </p><button class="px-10 py-3 sm:py-4 bg-gradient-to-r from-purple-600 to-purple-700 text-white font-semibold text-sm sm:text-base rounded-full hover:from-purple-700 hover:to-purple-800 transition-all duration-200 transform hover:scale-105 shadow-lg animate-fade-in-up-delayed cursor-pointer" data-v-2a974eea> Go to My Gift Cards </button></div></div></div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div>`);
    };
  }
});
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/giftcards/payment.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const payment = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-2a974eea"]]);

export { payment as default };
//# sourceMappingURL=payment-CfsC_bX7.mjs.map
