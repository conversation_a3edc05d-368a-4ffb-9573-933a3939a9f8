import { defineComponent, ref, mergeProps, unref, useSSRContext } from 'vue';
import { ssrRenderAttrs, ssrRenderComponent } from 'vue/server-renderer';
import { useRouter } from 'vue-router';
import { b as useUserStore, d as useState, c as useRuntimeConfig, L as Loader, S as Snackbar, e as _sfc_main$1 } from './server.mjs';
import '../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:url';
import '@iconify/utils';
import 'node:crypto';
import 'consola';
import 'node:path';
import 'better-sqlite3';
import 'ipx';
import 'pinia';
import 'cookie';
import '@iconify/vue';
import 'tailwindcss/colors';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import 'unhead/server';
import 'devalue';
import 'unhead/plugins';
import 'unhead/utils';

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "terms-and-condition",
  __ssrInlineRender: true,
  setup(__props) {
    useRouter();
    useUserStore();
    const snackbarRef = ref(null);
    const isLoading = useState("globalLoader", () => false);
    const hasError = ref(false);
    const privacyPolicyDescription = ref("");
    const { public: { BASE_URL } } = useRuntimeConfig();
    return (_ctx, _push, _parent, _attrs) => {
      var _a;
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "min-h-screen h-screen overflow-auto bg-gradient-to-br from-purple-50 to-blue-50" }, _attrs))}>`);
      if (unref(isLoading)) {
        _push(ssrRenderComponent(Loader, null, null, _parent));
      } else {
        _push(`<!---->`);
      }
      _push(ssrRenderComponent(Snackbar, {
        ref_key: "snackbarRef",
        ref: snackbarRef
      }, null, _parent));
      if (hasError.value) {
        _push(ssrRenderComponent(_sfc_main$1, { error: { statusCode: 500, message: "Something went wrong. Please try again later." } }, null, _parent));
      } else {
        _push(`<!---->`);
      }
      if (!hasError.value) {
        _push(`<main class="max-w-4xl mx-auto px-6 py-8"><div class="mb-6"><button class="w-10 h-10 rounded-full border border-gray-300 flex items-center justify-center group cursor-pointer"><svg xmlns="http://www.w3.org/2000/svg" class="w-7 h-7 text-purple-600 group-hover:text-purple-700 transition-colors" viewBox="0 0 24 24" fill="currentColor"><path d="m6.523 12.5l3.735 3.735q.146.146.153.344q.006.198-.153.363q-.166.166-.357.168t-.357-.162l-4.382-4.383q-.243-.242-.243-.565t.243-.566l4.382-4.382q.147-.146.347-.153q.201-.007.367.159q.16.165.162.353q.003.189-.162.354L6.523 11.5h12.38q.214 0.358.143t.143.357t-.143.357t-.357.143z"></path></svg></button></div><div class="bg-white rounded-2xl shadow-lg overflow-hidden"><div class="bg-gradient-to-r from-indigo-600 to-purple-600 px-8 py-12"><h1 class="text-4xl font-bold text-white mb-4">Terms And Conditions</h1></div><div class="px-8 py-10"><div class="prose prose-lg max-w-none"><section class="mb-10"><div class="ml-11 text-gray-700 leading-relaxed">${(_a = privacyPolicyDescription.value) != null ? _a : ""}</div></section></div></div></div></main>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div>`);
    };
  }
});
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/terms-and-condition.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};

export { _sfc_main as default };
//# sourceMappingURL=terms-and-condition-CKgE7rqU.mjs.map
