const client_manifest = {
  "_B0mZDeZW.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "B0mZDeZW.js",
    "name": "search",
    "imports": [
      "node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "_CUFphvrx.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "CUFphvrx.js",
    "name": "useLoader",
    "imports": [
      "node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "_CWEgQgqD.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "CWEgQgqD.js",
    "name": "booking",
    "imports": [
      "node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "_CrzxClJJ.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "CrzxClJJ.js",
    "name": "BookingSummary.vue",
    "imports": [
      "_CWEgQgqD.js",
      "node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "_D5LVHYZX.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "D5LVHYZX.js",
    "name": "Modal.vue",
    "imports": [
      "node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "_DCp4WqjN.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "DCp4WqjN.js",
    "name": "virtual_public",
    "imports": [
      "node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "_DztrEL98.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "DztrEL98.js",
    "name": "useAppSeoMeta",
    "imports": [
      "node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "assets/fonts/AlfaSlabOne-Regular.ttf": {
    "resourceType": "font",
    "mimeType": "font/ttf",
    "file": "AlfaSlabOne-Regular.De-1OOnu.ttf",
    "src": "assets/fonts/AlfaSlabOne-Regular.ttf"
  },
  "assets/fonts/DMSans-Bold.ttf": {
    "resourceType": "font",
    "mimeType": "font/ttf",
    "file": "DMSans-Bold.VVlVe7G0.ttf",
    "src": "assets/fonts/DMSans-Bold.ttf"
  },
  "assets/fonts/DMSans-Medium.ttf": {
    "resourceType": "font",
    "mimeType": "font/ttf",
    "file": "DMSans-Medium.CRZfJduw.ttf",
    "src": "assets/fonts/DMSans-Medium.ttf"
  },
  "assets/fonts/DMSans-Regular.ttf": {
    "resourceType": "font",
    "mimeType": "font/ttf",
    "file": "DMSans-Regular.Bw-KDgr1.ttf",
    "src": "assets/fonts/DMSans-Regular.ttf"
  },
  "assets/fonts/DMSans-SemiBold.ttf": {
    "resourceType": "font",
    "mimeType": "font/ttf",
    "file": "DMSans-SemiBold.yXWBtD5K.ttf",
    "src": "assets/fonts/DMSans-SemiBold.ttf"
  },
  "assets/fonts/Inter-SemiBold.ttf": {
    "resourceType": "font",
    "mimeType": "font/ttf",
    "file": "Inter-SemiBold.XVCL1Lno.ttf",
    "src": "assets/fonts/Inter-SemiBold.ttf"
  },
  "assets/fonts/Inter_28pt-Regular.ttf": {
    "resourceType": "font",
    "mimeType": "font/ttf",
    "file": "Inter_28pt-Regular.Dou9gfMK.ttf",
    "src": "assets/fonts/Inter_28pt-Regular.ttf"
  },
  "layouts/blank.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "DBxaw8ss.js",
    "name": "blank",
    "src": "layouts/blank.vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "layouts/default.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "DwuqoCVo.js",
    "name": "default",
    "src": "layouts/default.vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js",
      "_DCp4WqjN.js",
      "_B0mZDeZW.js"
    ],
    "css": [
      "default.DYfRv6XR.css"
    ]
  },
  "default.DYfRv6XR.css": {
    "file": "default.DYfRv6XR.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "node_modules/@nuxt/icon/dist/runtime/components/index.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "QcmcHULX.js",
    "name": "index",
    "src": "node_modules/@nuxt/icon/dist/runtime/components/index.js",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "node_modules/@nuxtjs/mdc/dist/runtime/components/prose/ProseA.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "DYXn1ltL.js",
    "name": "ProseA",
    "src": "node_modules/@nuxtjs/mdc/dist/runtime/components/prose/ProseA.vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "node_modules/@nuxtjs/mdc/dist/runtime/components/prose/ProseBlockquote.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "BNkdWnVO.js",
    "name": "ProseBlockquote",
    "src": "node_modules/@nuxtjs/mdc/dist/runtime/components/prose/ProseBlockquote.vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "node_modules/@nuxtjs/mdc/dist/runtime/components/prose/ProseCode.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "obmAhqLU.js",
    "name": "ProseCode",
    "src": "node_modules/@nuxtjs/mdc/dist/runtime/components/prose/ProseCode.vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "node_modules/@nuxtjs/mdc/dist/runtime/components/prose/ProseEm.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "DFg_Tvx_.js",
    "name": "ProseEm",
    "src": "node_modules/@nuxtjs/mdc/dist/runtime/components/prose/ProseEm.vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "node_modules/@nuxtjs/mdc/dist/runtime/components/prose/ProseH1.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "Dh1gm-Zt.js",
    "name": "ProseH1",
    "src": "node_modules/@nuxtjs/mdc/dist/runtime/components/prose/ProseH1.vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "node_modules/@nuxtjs/mdc/dist/runtime/components/prose/ProseH2.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "C61UfPtN.js",
    "name": "ProseH2",
    "src": "node_modules/@nuxtjs/mdc/dist/runtime/components/prose/ProseH2.vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "node_modules/@nuxtjs/mdc/dist/runtime/components/prose/ProseH3.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "BFS2Kxah.js",
    "name": "ProseH3",
    "src": "node_modules/@nuxtjs/mdc/dist/runtime/components/prose/ProseH3.vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "node_modules/@nuxtjs/mdc/dist/runtime/components/prose/ProseH4.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "LxX9ZWH0.js",
    "name": "ProseH4",
    "src": "node_modules/@nuxtjs/mdc/dist/runtime/components/prose/ProseH4.vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "node_modules/@nuxtjs/mdc/dist/runtime/components/prose/ProseH5.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "BCXf2QMO.js",
    "name": "ProseH5",
    "src": "node_modules/@nuxtjs/mdc/dist/runtime/components/prose/ProseH5.vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "node_modules/@nuxtjs/mdc/dist/runtime/components/prose/ProseH6.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "BywELM9f.js",
    "name": "ProseH6",
    "src": "node_modules/@nuxtjs/mdc/dist/runtime/components/prose/ProseH6.vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "node_modules/@nuxtjs/mdc/dist/runtime/components/prose/ProseHr.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "DLN_HeVX.js",
    "name": "ProseHr",
    "src": "node_modules/@nuxtjs/mdc/dist/runtime/components/prose/ProseHr.vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "node_modules/@nuxtjs/mdc/dist/runtime/components/prose/ProseImg.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "BpMMdlcH.js",
    "name": "ProseImg",
    "src": "node_modules/@nuxtjs/mdc/dist/runtime/components/prose/ProseImg.vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "node_modules/@nuxtjs/mdc/dist/runtime/components/prose/ProseLi.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "BE4N2iHI.js",
    "name": "ProseLi",
    "src": "node_modules/@nuxtjs/mdc/dist/runtime/components/prose/ProseLi.vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "node_modules/@nuxtjs/mdc/dist/runtime/components/prose/ProseOl.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "DvHR1n8C.js",
    "name": "ProseOl",
    "src": "node_modules/@nuxtjs/mdc/dist/runtime/components/prose/ProseOl.vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "node_modules/@nuxtjs/mdc/dist/runtime/components/prose/ProseP.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "vIwerdR6.js",
    "name": "ProseP",
    "src": "node_modules/@nuxtjs/mdc/dist/runtime/components/prose/ProseP.vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "node_modules/@nuxtjs/mdc/dist/runtime/components/prose/ProsePre.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "CgzjPAR0.js",
    "name": "ProsePre",
    "src": "node_modules/@nuxtjs/mdc/dist/runtime/components/prose/ProsePre.vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js"
    ],
    "css": []
  },
  "ProsePre.D5orA6B_.css": {
    "file": "ProsePre.D5orA6B_.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "node_modules/@nuxtjs/mdc/dist/runtime/components/prose/ProseScript.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "DT9QGDXS.js",
    "name": "ProseScript",
    "src": "node_modules/@nuxtjs/mdc/dist/runtime/components/prose/ProseScript.vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "node_modules/@nuxtjs/mdc/dist/runtime/components/prose/ProseStrong.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "DxKns7Lm.js",
    "name": "ProseStrong",
    "src": "node_modules/@nuxtjs/mdc/dist/runtime/components/prose/ProseStrong.vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "node_modules/@nuxtjs/mdc/dist/runtime/components/prose/ProseTable.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "CHMTlxnL.js",
    "name": "ProseTable",
    "src": "node_modules/@nuxtjs/mdc/dist/runtime/components/prose/ProseTable.vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "node_modules/@nuxtjs/mdc/dist/runtime/components/prose/ProseTbody.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "DtYE7vfm.js",
    "name": "ProseTbody",
    "src": "node_modules/@nuxtjs/mdc/dist/runtime/components/prose/ProseTbody.vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "node_modules/@nuxtjs/mdc/dist/runtime/components/prose/ProseTd.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "CismWNDN.js",
    "name": "ProseTd",
    "src": "node_modules/@nuxtjs/mdc/dist/runtime/components/prose/ProseTd.vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "node_modules/@nuxtjs/mdc/dist/runtime/components/prose/ProseTh.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "ByX51qZ8.js",
    "name": "ProseTh",
    "src": "node_modules/@nuxtjs/mdc/dist/runtime/components/prose/ProseTh.vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "node_modules/@nuxtjs/mdc/dist/runtime/components/prose/ProseThead.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "CtS6RCZ_.js",
    "name": "ProseThead",
    "src": "node_modules/@nuxtjs/mdc/dist/runtime/components/prose/ProseThead.vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "node_modules/@nuxtjs/mdc/dist/runtime/components/prose/ProseTr.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "MyRTVtva.js",
    "name": "ProseTr",
    "src": "node_modules/@nuxtjs/mdc/dist/runtime/components/prose/ProseTr.vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "node_modules/@nuxtjs/mdc/dist/runtime/components/prose/ProseUl.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "BzkvYoxS.js",
    "name": "ProseUl",
    "src": "node_modules/@nuxtjs/mdc/dist/runtime/components/prose/ProseUl.vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "node_modules/nuxt/dist/app/entry.js": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "DhWElDpj.js",
    "name": "entry",
    "src": "node_modules/nuxt/dist/app/entry.js",
    "isEntry": true,
    "dynamicImports": [
      "layouts/blank.vue",
      "layouts/default.vue"
    ],
    "css": [
      "entry.DloE7FIG.css"
    ],
    "assets": [],
    "_globalCSS": true
  },
  "entry.DloE7FIG.css": {
    "file": "entry.DloE7FIG.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "pages/404.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "CERcNLEV.js",
    "name": "404",
    "src": "pages/404.vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "pages/auth/login.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "CuaWroFv.js",
    "name": "login",
    "src": "pages/auth/login.vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js",
      "_DCp4WqjN.js",
      "_CUFphvrx.js",
      "_DztrEL98.js"
    ],
    "css": []
  },
  "login.e2J8-Jp_.css": {
    "file": "login.e2J8-Jp_.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "pages/auth/otp.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "DtOvoDqI.js",
    "name": "otp",
    "src": "pages/auth/otp.vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js",
      "_DCp4WqjN.js",
      "_CUFphvrx.js",
      "_DztrEL98.js"
    ]
  },
  "pages/auth/signup.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "D_i1-Ujq.js",
    "name": "signup",
    "src": "pages/auth/signup.vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js",
      "_DCp4WqjN.js",
      "_CUFphvrx.js",
      "_DztrEL98.js"
    ],
    "css": []
  },
  "signup.DoQO2ruY.css": {
    "file": "signup.DoQO2ruY.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "pages/booking/addons.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "DuMNgxfY.js",
    "name": "addons",
    "src": "pages/booking/addons.vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js",
      "_CWEgQgqD.js",
      "_DztrEL98.js",
      "_CrzxClJJ.js"
    ]
  },
  "pages/booking/cart.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "CYllffY7.js",
    "name": "cart",
    "src": "pages/booking/cart.vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js",
      "_CWEgQgqD.js",
      "_DztrEL98.js",
      "_CrzxClJJ.js"
    ],
    "css": []
  },
  "cart.COMAFErI.css": {
    "file": "cart.COMAFErI.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "pages/booking/index.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "5rGJdNu1.js",
    "name": "index",
    "src": "pages/booking/index.vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js",
      "_CWEgQgqD.js",
      "_DztrEL98.js",
      "_CrzxClJJ.js",
      "_D5LVHYZX.js"
    ],
    "css": []
  },
  "index.B_lNgNLt.css": {
    "file": "index.B_lNgNLt.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "pages/booking/professionals.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "BK8Kv8-i.js",
    "name": "professionals",
    "src": "pages/booking/professionals.vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js",
      "_CWEgQgqD.js",
      "_DztrEL98.js",
      "_CrzxClJJ.js"
    ]
  },
  "pages/booking/schedule.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "D_g4tSwH.js",
    "name": "schedule",
    "src": "pages/booking/schedule.vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js",
      "_CWEgQgqD.js",
      "_DztrEL98.js",
      "_CrzxClJJ.js"
    ],
    "css": []
  },
  "schedule.633a1FNm.css": {
    "file": "schedule.633a1FNm.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "pages/category/[id].vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "Bjshyr0U.js",
    "name": "_id_",
    "src": "pages/category/[id].vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js",
      "_DztrEL98.js"
    ],
    "css": []
  },
  "_id_.BzTvipQ0.css": {
    "file": "_id_.BzTvipQ0.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "pages/contact-us.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "lLGHq7XY.js",
    "name": "contact-us",
    "src": "pages/contact-us.vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "pages/giftcards/index.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "DhSd84_r.js",
    "name": "index",
    "src": "pages/giftcards/index.vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js",
      "_CWEgQgqD.js"
    ]
  },
  "pages/giftcards/payment.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "BmJBuZaG.js",
    "name": "payment",
    "src": "pages/giftcards/payment.vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js",
      "_CWEgQgqD.js"
    ],
    "css": []
  },
  "payment.D5muXfaj.css": {
    "file": "payment.D5muXfaj.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "pages/index.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "DuAMeX-f.js",
    "name": "index",
    "src": "pages/index.vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js",
      "_B0mZDeZW.js"
    ],
    "css": [
      "index.KvxazU0U.css"
    ]
  },
  "index.KvxazU0U.css": {
    "file": "index.KvxazU0U.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "pages/list-salons/[type].vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "ClgzyCM2.js",
    "name": "_type_",
    "src": "pages/list-salons/[type].vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js",
      "_DztrEL98.js"
    ],
    "css": []
  },
  "_type_.DjeTOSBl.css": {
    "file": "_type_.DjeTOSBl.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "pages/membership/index.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "B7nuZonj.js",
    "name": "index",
    "src": "pages/membership/index.vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js",
      "_CWEgQgqD.js"
    ]
  },
  "pages/membership/payment.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "DdbasMfX.js",
    "name": "payment",
    "src": "pages/membership/payment.vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js",
      "_CWEgQgqD.js"
    ],
    "css": []
  },
  "payment.DZlEZ4Bq.css": {
    "file": "payment.DZlEZ4Bq.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "pages/notifications.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "rLdhad8h.js",
    "name": "notifications",
    "src": "pages/notifications.vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "pages/privacy-policy.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "Bz63l8dJ.js",
    "name": "privacy-policy",
    "src": "pages/privacy-policy.vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js"
    ]
  },
  "pages/profile/index.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "tqHhtKNa.js",
    "name": "index",
    "src": "pages/profile/index.vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js",
      "_DztrEL98.js"
    ],
    "css": []
  },
  "index.DwAoV1nu.css": {
    "file": "index.DwAoV1nu.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "pages/reviews/[orderId].vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "CqBjqLkU.js",
    "name": "_orderId_",
    "src": "pages/reviews/[orderId].vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js"
    ],
    "css": []
  },
  "_orderId_.BpyyXpuw.css": {
    "file": "_orderId_.BpyyXpuw.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "pages/salon/[query].vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "cpquglwG.js",
    "name": "_query_",
    "src": "pages/salon/[query].vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js",
      "_B0mZDeZW.js",
      "_DztrEL98.js"
    ],
    "css": []
  },
  "_query_.DIPVw4UO.css": {
    "file": "_query_.DIPVw4UO.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "pages/salons/[id].vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "BIt5rbXn.js",
    "name": "_id_",
    "src": "pages/salons/[id].vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js",
      "_CWEgQgqD.js",
      "_D5LVHYZX.js",
      "_DztrEL98.js"
    ],
    "css": []
  },
  "_id_.DhoWoUKW.css": {
    "file": "_id_.DhoWoUKW.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "pages/search.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "Cd1zUmRz.js",
    "name": "search",
    "src": "pages/search.vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js"
    ],
    "css": []
  },
  "search.CwmmX8Qb.css": {
    "file": "search.CwmmX8Qb.css",
    "resourceType": "style",
    "prefetch": true,
    "preload": true
  },
  "pages/terms-and-condition.vue": {
    "resourceType": "script",
    "module": true,
    "prefetch": true,
    "preload": true,
    "file": "0RZr5hcD.js",
    "name": "terms-and-condition",
    "src": "pages/terms-and-condition.vue",
    "isDynamicEntry": true,
    "imports": [
      "node_modules/nuxt/dist/app/entry.js"
    ]
  }
};

export { client_manifest as default };
//# sourceMappingURL=client.manifest.mjs.map
