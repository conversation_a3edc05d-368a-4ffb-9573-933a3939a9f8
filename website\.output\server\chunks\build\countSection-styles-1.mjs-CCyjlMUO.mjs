const countSection_vue_vue_type_style_index_0_scoped_7db0aa52_lang = '.group[data-v-7db0aa52]:hover{transform:translateY(-2px);transition:transform .3s ease-in-out}.section-gradient-bottom[data-v-7db0aa52]{isolation:isolate;overflow:hidden;position:relative}.section-gradient-bottom[data-v-7db0aa52]:after{background:linear-gradient(180deg,#fff 2%,#eee5f9);bottom:0;content:"";height:100%;left:0;pointer-events:none;position:absolute;width:100%;z-index:-1}';

export { countSection_vue_vue_type_style_index_0_scoped_7db0aa52_lang as c };
//# sourceMappingURL=countSection-styles-1.mjs-CCyjlMUO.mjs.map
