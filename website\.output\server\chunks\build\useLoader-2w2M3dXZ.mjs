import { p as publicAssetsURL } from '../_/nitro.mjs';
import { d as useState } from './server.mjs';

const _imports_1 = publicAssetsURL("/images/auth-image.png");
function useLoader() {
  const isLoading = useState("globalLoader", () => false);
  const showLoader = () => isLoading.value = true;
  const hideLoader = () => isLoading.value = false;
  return { isLoading, showLoader, hideLoader };
}

export { _imports_1 as _, useLoader as u };
//# sourceMappingURL=useLoader-2w2M3dXZ.mjs.map
