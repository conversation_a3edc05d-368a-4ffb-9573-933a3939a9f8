const interopDefault = r => r.default || r || [];
const styles = {
  "node_modules/nuxt/dist/app/entry.js": () => import('./entry-styles.Cvx9VdF_.mjs').then(interopDefault),
  "node_modules/@nuxtjs/mdc/dist/runtime/components/prose/ProsePre.vue": () => import('./ProsePre-styles.a18PV1zE.mjs').then(interopDefault),
  "node_modules/@nuxtjs/mdc/dist/runtime/components/prose/ProsePre.vue?vue&type=style&index=0&lang.css": () => import('./ProsePre-styles.K7YIDmWo.mjs').then(interopDefault),
  "pages/auth/login.vue": () => import('./login-styles.DVUBaQfT.mjs').then(interopDefault),
  "pages/auth/signup.vue": () => import('./signup-styles.B8Lbhltq.mjs').then(interopDefault),
  "pages/search.vue": () => import('./search-styles.B_TTymhh.mjs').then(interopDefault),
  "pages/salons/[id].vue": () => import('./_id_-styles.DE-6KNV5.mjs').then(interopDefault),
  "pages/auth/login.vue?vue&type=style&index=0&scoped=a1bc815d&lang.css": () => import('./login-styles.DpyrLczG.mjs').then(interopDefault),
  "pages/search.vue?vue&type=style&index=0&scoped=430997f3&lang.css": () => import('./search-styles.CG8TxPbi.mjs').then(interopDefault),
  "pages/auth/signup.vue?vue&type=style&index=0&scoped=07f33367&lang.css": () => import('./signup-styles.CnJUWhbM.mjs').then(interopDefault),
  "pages/salons/[id].vue?vue&type=style&index=0&scoped=2d535f29&lang.css": () => import('./_id_-styles.URl4RKBQ.mjs').then(interopDefault),
  "pages/category/[id].vue": () => import('./_id_-styles.CXgE2ng4.mjs').then(interopDefault),
  "pages/booking/schedule.vue": () => import('./schedule-styles.BK2YDjJ_.mjs').then(interopDefault),
  "pages/booking/index.vue": () => import('./index-styles.Cjg4mcWE.mjs').then(interopDefault),
  "pages/booking/cart.vue": () => import('./cart-styles._00fe1jx.mjs').then(interopDefault),
  "pages/giftcards/payment.vue": () => import('./payment-styles.DgG00oFh.mjs').then(interopDefault),
  "pages/reviews/[orderId].vue": () => import('./_orderId_-styles.BAyRsYt3.mjs').then(interopDefault),
  "pages/salon/[query].vue": () => import('./_query_-styles.CiAVxQMU.mjs').then(interopDefault),
  "pages/booking/schedule.vue?vue&type=style&index=0&lang.css": () => import('./schedule-styles.CQWNBWma.mjs').then(interopDefault),
  "pages/category/[id].vue?vue&type=style&index=0&scoped=928d6fa5&lang.css": () => import('./_id_-styles.BLzUfjpM.mjs').then(interopDefault),
  "pages/booking/index.vue?vue&type=style&index=0&lang.css": () => import('./index-styles.DAxijaAd.mjs').then(interopDefault),
  "pages/booking/cart.vue?vue&type=style&index=0&scoped=8d23881c&lang.css": () => import('./cart-styles.C1wYJ9aE.mjs').then(interopDefault),
  "pages/giftcards/payment.vue?vue&type=style&index=0&scoped=2a974eea&lang.css": () => import('./payment-styles.C5uDlVwQ.mjs').then(interopDefault),
  "pages/reviews/[orderId].vue?vue&type=style&index=0&scoped=7d319dea&lang.css": () => import('./_orderId_-styles.BTFZ5mHh.mjs').then(interopDefault),
  "pages/salon/[query].vue?vue&type=style&index=0&lang.css": () => import('./_query_-styles.Tt7ZGspn.mjs').then(interopDefault),
  "pages/membership/payment.vue": () => import('./payment-styles.BfbhNinE.mjs').then(interopDefault),
  "pages/notifications.vue": () => import('./notifications-styles.By9RYSM9.mjs').then(interopDefault),
  "pages/profile/index.vue": () => import('./index-styles.DDZytI-Y.mjs').then(interopDefault),
  "pages/membership/payment.vue?vue&type=style&index=0&scoped=71d29216&lang.css": () => import('./payment-styles.CZPFRnM8.mjs').then(interopDefault),
  "pages/notifications.vue?vue&type=style&index=0&scoped=87b55d03&lang.css": () => import('./notifications-styles.T3jS_0ml.mjs').then(interopDefault),
  "pages/profile/index.vue?vue&type=style&index=0&scoped=43160719&lang.css": () => import('./index-styles.DLasefL8.mjs').then(interopDefault),
  "pages/list-salons/[type].vue": () => import('./_type_-styles.R4X2BqpQ.mjs').then(interopDefault),
  "pages/list-salons/[type].vue?vue&type=style&index=0&scoped=9e8aca4a&lang.css": () => import('./_type_-styles.yXeSbvsh.mjs').then(interopDefault),
  "components/landing/countSection.vue": () => import('./countSection-styles.CDoiqmgU.mjs').then(interopDefault),
  "components/landing/TestimonialSection.vue": () => import('./TestimonialSection-styles.DOmoU0Ai.mjs').then(interopDefault),
  "components/landing/DownloadSection.vue": () => import('./DownloadSection-styles.CiTn6dhZ.mjs').then(interopDefault),
  "components/landing/countSection.vue?vue&type=style&index=0&scoped=7db0aa52&lang.css": () => import('./countSection-styles.Dj1GRXZB.mjs').then(interopDefault),
  "components/landing/TestimonialSection.vue?vue&type=style&index=0&scoped=0585aade&lang.css": () => import('./TestimonialSection-styles.Cpxaczrl.mjs').then(interopDefault),
  "components/landing/DownloadSection.vue?vue&type=style&index=0&scoped=b976a0a8&lang.css": () => import('./DownloadSection-styles.4mNpJy87.mjs').then(interopDefault),
  "components/Snackbar.vue": () => import('./Snackbar-styles.BSKWqFAd.mjs').then(interopDefault),
  "components/Loader.vue": () => import('./Loader-styles.Bd9AggdC.mjs').then(interopDefault),
  "components/Snackbar.vue?vue&type=style&index=0&scoped=972537fa&lang.css": () => import('./Snackbar-styles.DyTiNl8h.mjs').then(interopDefault),
  "components/Loader.vue?vue&type=style&index=0&scoped=094a9e71&lang.css": () => import('./Loader-styles.v6oaclV_.mjs').then(interopDefault),
  "components/profile/ProfileSettings.vue": () => import('./ProfileSettings-styles.C_gMxgut.mjs').then(interopDefault),
  "components/profile/ProfileCart.vue": () => import('./ProfileCart-styles.BukfDv9x.mjs').then(interopDefault),
  "components/profile/ProfileWishlist.vue": () => import('./ProfileWishlist-styles.C8QXIo29.mjs').then(interopDefault),
  "components/profile/ProfileOrders.vue": () => import('./ProfileOrders-styles.qt0IvmRM.mjs').then(interopDefault),
  "components/profile/ProfileGiftCards.vue": () => import('./ProfileGiftCards-styles.Ch8xrNL-.mjs').then(interopDefault),
  "components/profile/ProfileCart.vue?vue&type=style&index=0&scoped=618051d1&lang.css": () => import('./ProfileCart-styles.CvHXBibP.mjs').then(interopDefault),
  "components/profile/ProfileSettings.vue?vue&type=style&index=0&scoped=adf81e9c&lang.css": () => import('./ProfileSettings-styles.DoqinKFK.mjs').then(interopDefault),
  "components/profile/ProfileOrders.vue?vue&type=style&index=0&scoped=b5729ff7&lang.css": () => import('./ProfileOrders-styles.BfeZ-Jvt.mjs').then(interopDefault),
  "components/profile/ProfileWishlist.vue?vue&type=style&index=0&scoped=cda2934e&lang.css": () => import('./ProfileWishlist-styles.C_h2LVfD.mjs').then(interopDefault),
  "components/profile/ProfileGiftCards.vue?vue&type=style&index=0&scoped=8c74baac&lang.css": () => import('./ProfileGiftCards-styles.CdiAkIVN.mjs').then(interopDefault),
  "components/profile/Profile.vue?vue&type=script&setup=true&lang.ts": () => import('./Profile-styles.1VuZXsd5.mjs').then(interopDefault),
  "components/Header.vue": () => import('./Header-styles.lkWZmXI8.mjs').then(interopDefault),
  "components/Header.vue?vue&type=style&index=0&scoped=c340e611&lang.css": () => import('./Header-styles.CGLlLQSz.mjs').then(interopDefault)
};

export { styles as default };
//# sourceMappingURL=styles.mjs.map
