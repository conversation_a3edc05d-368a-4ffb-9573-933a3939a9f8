import { defineComponent, computed, ref, mergeProps, unref, useSSRContext } from 'vue';
import { ssrRenderAttrs, ssrRenderComponent, ssrRenderAttr, ssrIncludeBooleanAttr, ssrInterpolate, ssrRenderList, ssrRenderStyle } from 'vue/server-renderer';
import { useRouter, useRoute } from 'vue-router';
import { u as useBookingStore } from './booking-D2Du41fq.mjs';
import { u as useAppSeoMeta } from './useAppSeoMeta-HWukB6FN.mjs';
import { _ as _sfc_main$2 } from './BookingSummary-C7u1ob1e.mjs';
import { _ as _export_sfc, c as useRuntimeConfig, d as useState, L as Loader, S as Snackbar, e as _sfc_main$1 } from './server.mjs';
import 'pinia';
import '../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:url';
import '@iconify/utils';
import 'node:crypto';
import 'consola';
import 'node:path';
import 'better-sqlite3';
import 'ipx';
import 'cookie';
import '@iconify/vue';
import 'tailwindcss/colors';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import 'unhead/server';
import 'devalue';
import 'unhead/plugins';
import 'unhead/utils';

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "cart",
  __ssrInlineRender: true,
  setup(__props) {
    useAppSeoMeta({
      title: computed(() => `Cart | Bookslotz`),
      meta: [
        { name: "description", content: computed(() => `Review your salon booking and proceed to payment at ${salon.value.name}.`) },
        { name: "keywords", content: "salon cart, booking checkout, payment" },
        { name: "robots", content: "index, follow" },
        { property: "og:title", content: computed(() => `Cart | ${salon.value.name}`) },
        { property: "og:description", content: "Complete your salon booking with secure payment options." },
        { property: "og:type", content: "website" }
      ]
    });
    useRouter();
    const route = useRoute();
    const bookingStore = useBookingStore();
    const { public: { BASE_URL } } = useRuntimeConfig();
    const isLoading = useState("globalLoader", () => false);
    const hasError = ref(false);
    const snackbarRef = ref(null);
    const bookingSummaryRef = ref(null);
    const showSuccessModal = ref(false);
    const giftCardCode = ref("");
    const appliedGiftCard = ref(null);
    const otherCharges = ref(20);
    const membershipApplied = ref(false);
    const membershipData = ref(null);
    const salon = ref({
      name: "Loading...",
      location: "MG Road, Mumbai",
      image: "/images/saloon-small-image.png"
    });
    computed(() => decodeURIComponent(route.query.slug || ""));
    const orderAmount = computed(() => bookingStore.total || 0);
    const membershipDiscount = computed(() => {
      var _a;
      return ((_a = membershipData.value) == null ? void 0 : _a.totalDiscount) || 0;
    });
    const giftCardDiscount = computed(() => {
      var _a;
      return ((_a = appliedGiftCard.value) == null ? void 0 : _a.discount) || 0;
    });
    const totalDiscount = computed(() => membershipDiscount.value + giftCardDiscount.value);
    const finalTotal = computed(() => Math.max(0, orderAmount.value + otherCharges.value - totalDiscount.value));
    const discounts = computed(() => {
      var _a;
      const discountList = [];
      if (membershipData.value && membershipDiscount.value > 0) {
        discountList.push({ name: "Membership Discount", amount: membershipDiscount.value });
      }
      if ((_a = appliedGiftCard.value) == null ? void 0 : _a.discount) {
        discountList.push({ name: "Gift Card", amount: giftCardDiscount.value });
      }
      return discountList;
    });
    const allServices = computed(() => {
      const services = (bookingStore.selectedServices || []).map((service) => ({
        ...service,
        type: "service",
        professional: getProfessionalForService(service.id)
      }));
      const addons = (bookingStore.selectedAddons || []).map((addon) => ({
        ...addon,
        type: "addon",
        professional: null
      }));
      return [...services, ...addons];
    });
    const canContinue = computed(() => {
      return bookingStore.isReadyForNextStep && (bookingStore.bookingType !== "service" || allServices.value.length > 0);
    });
    function getProfessionalForService(serviceId) {
      if (!bookingStore.professionalSelectionType) return null;
      if (bookingStore.professionalSelectionType === "any") return "Any Professional";
      if (bookingStore.professionalSelectionType === "per-service" && bookingStore.selectedProfessionals) {
        const professional = bookingStore.selectedProfessionals[serviceId];
        return professional ? professional.name : null;
      }
      return null;
    }
    function removeItem(id) {
      if (bookingStore.bookingType === "service") {
        const isService = bookingStore.selectedServices.some((s) => s.id === id);
        if (isService) {
          bookingStore.removeService(id);
        } else {
          const updatedAddons = bookingStore.selectedAddons.filter((addon) => addon.id !== id);
          bookingStore.setSelectedAddons(updatedAddons);
        }
      }
    }
    function proceedToPayment() {
      var _a;
      if (bookingStore.bookingType === "service" && allServices.value.length === 0) {
        (_a = snackbarRef.value) == null ? void 0 : _a.showSnackbar("Please select at least one service before proceeding to payment");
        return;
      }
      showSuccessModal.value = true;
    }
    return (_ctx, _push, _parent, _attrs) => {
      var _a;
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "min-h-screen bg-white relative" }, _attrs))} data-v-8d23881c>`);
      if (unref(isLoading)) {
        _push(ssrRenderComponent(Loader, null, null, _parent));
      } else {
        _push(`<!---->`);
      }
      _push(ssrRenderComponent(Snackbar, {
        ref_key: "snackbarRef",
        ref: snackbarRef
      }, null, _parent));
      if (hasError.value) {
        _push(ssrRenderComponent(_sfc_main$1, { error: { statusCode: 500, message: "Something went wrong. Please try again later." } }, null, _parent));
      } else {
        _push(`<!---->`);
      }
      if (!hasError.value) {
        _push(`<div class="flex flex-col xl:flex-row max-w-8xl mx-auto px-1 sm:px-16 md:px-12 mb-10 justify-between" data-v-8d23881c><div class="flex-1 lg:flex-[0.5] px-6 lg:px-8 py-6 lg:py-8 bg-white" data-v-8d23881c><div class="flex items-center space-x-3 mb-8" data-v-8d23881c><button class="w-10 h-10 rounded-full border border-gray-300 flex items-center justify-center group cursor-pointer" data-v-8d23881c><svg xmlns="http://www.w3.org/2000/svg" class="w-7 h-7 text-purple-600 group-hover:text-purple-700 transition-colors" viewBox="0 0 24 24" fill="currentColor" data-v-8d23881c><path d="m6.523 12.5l3.735 3.735q.146.146.153.344q.006.198-.153.363q-.166.166-.357.168t-.357-.162l-4.382-4.383q-.243-.242-.243-.565t.243-.566l4.382-4.382q.147-.146.347-.153q.201-.007.367.159q.16.165.162.353q.003.189-.162.354L6.523 11.5h12.38q.214 0 .358.143t.143.357t-.143.357t-.357.143z" data-v-8d23881c></path></svg></button><h1 class="text-2xl font-semibold text-gray-900" data-v-8d23881c>Cart</h1></div><div class="mb-8" data-v-8d23881c><h2 class="text-lg font-semibold text-gray-900 mb-4" data-v-8d23881c>Enter Gift Card</h2><div class="flex flex-col sm:flex-row gap-3 items-stretch sm:items-center" data-v-8d23881c><input${ssrRenderAttr("value", giftCardCode.value)} type="text" placeholder="Enter gift card code" class="flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 bg-[#B0D4FC1F]" data-v-8d23881c><button class="px-8 py-3 bg-purple-600 text-white font-semibold rounded-lg hover:bg-purple-700 transition-colors disabled:bg-gray-300 disabled:cursor-not-allowed w-full sm:w-auto self-center sm:self-auto cursor-pointer"${ssrIncludeBooleanAttr(!giftCardCode.value) ? " disabled" : ""} data-v-8d23881c> Apply </button></div>`);
        if ((_a = appliedGiftCard.value) == null ? void 0 : _a.discount) {
          _push(`<p class="text-sm text-green-600 mt-2" data-v-8d23881c> Gift card applied: -\u20B9${ssrInterpolate(appliedGiftCard.value.discount)}</p>`);
        } else {
          _push(`<!---->`);
        }
        _push(`</div><div class="bg-[#F5F9FE] rounded-xl p-6 mb-8" data-v-8d23881c><h2 class="flex justify-between items-center font-semibold text-lg mb-4" data-v-8d23881c><span class="text-lg font-semibold text-black" data-v-8d23881c>Membership Discount</span></h2>`);
        if (membershipData.value) {
          _push(`<div class="space-y-2" data-v-8d23881c><p class="text-sm font-medium" data-v-8d23881c> You&#39;ve a membership for ${ssrInterpolate(membershipData.value.salonName)}: <span class="text-orange-600 font-semibold text-sm" data-v-8d23881c>${ssrInterpolate(membershipData.value.planName)}</span></p><p class="text-sm font-medium" data-v-8d23881c> Enjoy an exclusive ${ssrInterpolate(membershipData.value.discountPercent)}% discount on all services </p><ul class="list-disc list-inside items-center text-sm mt-2 space-y-0" data-v-8d23881c><li class="font-semibold" data-v-8d23881c> Membership Discount(${ssrInterpolate(membershipData.value.discountPercent)}%): <span class="inter-semibold font-[500]" data-v-8d23881c>-\u20B9${ssrInterpolate(membershipData.value.totalDiscount.toFixed(2))}</span>`);
          if (!membershipApplied.value) {
            _push(`<button class="ml-2 px-3 py-1 text-xs bg-purple-100 text-purple-700 rounded hover:bg-purple-200 font-semibold transition-colors cursor-pointer" data-v-8d23881c> Apply Now ! </button>`);
          } else {
            _push(`<!---->`);
          }
          _push(`</li>`);
          if (membershipData.value.services && membershipData.value.services.length > 0) {
            _push(`<li class="mt-2" data-v-8d23881c><span class="font-semibold" data-v-8d23881c>Discount Breakdown</span><div class="ml-4 space-y-1 mt-2" data-v-8d23881c><!--[-->`);
            ssrRenderList(membershipData.value.services, (service) => {
              _push(`<div class="flex justify-between text-xs text-gray-600 pl-2" data-v-8d23881c><span class="font-[500]" data-v-8d23881c>${ssrInterpolate(service.serviceName)}</span><span class="inter-semibold font-[500]" data-v-8d23881c>-\u20B9${ssrInterpolate(service.memberShipDiscountAmount.toFixed(2))}</span></div>`);
            });
            _push(`<!--]--></div></li>`);
          } else {
            _push(`<!---->`);
          }
          _push(`</ul></div>`);
        } else {
          _push(`<p class="text-sm text-gray-600" data-v-8d23881c>No membership available.</p>`);
        }
        _push(`</div></div>`);
        _push(ssrRenderComponent(_sfc_main$2, {
          ref_key: "bookingSummaryRef",
          ref: bookingSummaryRef,
          salon: salon.value,
          "selected-services": allServices.value,
          total: finalTotal.value,
          "can-continue": canContinue.value,
          "show-professional-names": unref(bookingStore).professionalSelectionType === "per-service",
          "professional-names": unref(bookingStore).selectedProfessionals,
          "show-order-summary": true,
          discounts: discounts.value,
          "other-charges": otherCharges.value,
          onContinue: proceedToPayment,
          onRemoveService: removeItem
        }, null, _parent));
        _push(`</div>`);
      } else {
        _push(`<!---->`);
      }
      if (showSuccessModal.value) {
        _push(`<div class="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-3 sm:p-4" data-v-8d23881c><div class="bg-white rounded-2xl sm:rounded-3xl w-full max-w-2xl sm:max-w-3xl mx-auto text-center relative overflow-hidden" style="${ssrRenderStyle({ "max-height": "90vh" })}" data-v-8d23881c><div class="p-6 sm:p-8" data-v-8d23881c><div class="relative mb-6 sm:mb-8 flex justify-center" data-v-8d23881c><div class="absolute -top-3 sm:-top-4 -left-6 sm:-left-8 w-2 sm:w-3 h-2 sm:h-3 bg-red-400 rounded-full animate-float-1" data-v-8d23881c></div><div class="absolute -top-4 sm:-top-6 left-8 sm:left-12 w-1.5 sm:w-2 h-1.5 sm:h-2 bg-orange-400 rounded-full animate-float-2" data-v-8d23881c></div><div class="absolute top-1 sm:top-2 -right-6 sm:-right-8 w-3 sm:w-4 h-3 sm:h-4 bg-pink-400 rounded-full animate-float-3" data-v-8d23881c></div><div class="absolute top-8 sm:top-12 -left-4 sm:-left-6 w-1.5 sm:w-2 h-1.5 sm:h-2 bg-blue-400 rounded-full animate-float-4" data-v-8d23881c></div><div class="absolute -bottom-1 sm:-bottom-2 -right-3 sm:-right-4 w-2 sm:w-3 h-2 sm:h-3 bg-green-400 rounded-full animate-float-5" data-v-8d23881c></div><div class="absolute -bottom-3 sm:-bottom-4 left-6 sm:left-8 w-1.5 sm:w-2 h-1.5 sm:h-2 bg-purple-400 rounded-full animate-float-6" data-v-8d23881c></div><div class="relative" data-v-8d23881c><div class="absolute inset-0 w-16 sm:w-20 h-16 sm:h-20 border-2 border-purple-200 rounded-full animate-pulse-ring opacity-30" data-v-8d23881c></div><div class="absolute inset-0.5 sm:inset-1 w-15 sm:w-18 h-15 sm:h-18 border-2 border-purple-300 rounded-full animate-pulse-ring-delayed opacity-20" data-v-8d23881c></div><div class="relative w-16 sm:w-20 h-16 sm:h-20 bg-gradient-to-br from-purple-500 to-purple-700 rounded-full flex items-center justify-center shadow-lg animate-scale-in" data-v-8d23881c><svg class="w-8 sm:w-10 h-8 sm:h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="3" data-v-8d23881c><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7" data-v-8d23881c></path></svg></div></div></div><h2 class="text-xl sm:text-2xl font-bold text-gray-900 mb-2 sm:mb-3 animate-fade-in-up leading-tight" data-v-8d23881c> Your Appointment Confirmed Successfully </h2><p class="text-gray-600 text-sm sm:text-base mb-6 sm:mb-8 leading-relaxed animate-fade-in-up-delayed px-2 sm:px-0" data-v-8d23881c> Thank you for booking with ${ssrInterpolate(salon.value.name)}. Your appointment is confirmed, and we&#39;ll see you soon! </p><button class="px-20 py-3 sm:py-4 bg-gradient-to-r from-purple-600 to-purple-700 text-white font-semibold text-sm sm:text-base rounded-full hover:from-purple-700 hover:to-purple-800 transition-all duration-200 transform hover:scale-105 shadow-lg animate-fade-in-up-delayed cursor-pointer" data-v-8d23881c> Continue </button></div></div></div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div>`);
    };
  }
});
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/booking/cart.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const cart = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-8d23881c"]]);

export { cart as default };
//# sourceMappingURL=cart-CGO601Bi.mjs.map
