import { defineComponent, ref, computed, mergeProps, unref, useSSRContext } from 'vue';
import { ssrRenderAttrs, ssrRenderComponent, ssrRenderStyle, ssrRenderList, ssrRenderClass, ssrInterpolate, ssrIncludeBooleanAttr } from 'vue/server-renderer';
import { useRoute, useRouter } from 'vue-router';
import { u as useBookingStore } from './booking-D2Du41fq.mjs';
import { c as useRuntimeConfig, b as useUserStore, d as useState, L as Loader, S as Snackbar, e as _sfc_main$1$1, i as useCookie } from './server.mjs';
import { u as useAppSeoMeta } from './useAppSeoMeta-HWukB6FN.mjs';
import { _ as _sfc_main$2 } from './BookingSummary-C7u1ob1e.mjs';
import { _ as _sfc_main$1 } from './Modal-BOTd-eEZ.mjs';
import 'pinia';
import '../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:url';
import '@iconify/utils';
import 'node:crypto';
import 'consola';
import 'node:path';
import 'better-sqlite3';
import 'ipx';
import 'cookie';
import '@iconify/vue';
import 'tailwindcss/colors';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import 'unhead/server';
import 'devalue';
import 'unhead/plugins';
import 'unhead/utils';

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "index",
  __ssrInlineRender: true,
  setup(__props) {
    const route = useRoute();
    const router = useRouter();
    const { public: { BASE_URL } } = useRuntimeConfig();
    const bookingStore = useBookingStore();
    const userStore = useUserStore();
    const bookingSummaryRef = ref(null);
    const isLoading = useState("globalLoader", () => false);
    const hasError = ref(false);
    const snackbarRef = ref(null);
    const selectedCategoryIndex = ref(0);
    const categories = ref([]);
    const currentServices = ref([]);
    const salonSlug = computed(() => decodeURIComponent(route.query.slug || ""));
    const cartServiceIds = ref([]);
    const showCartConflictModal = ref(false);
    const isLoadingServices = ref(false);
    const currentPage = ref(1);
    const totalPages = ref(1);
    const hasMoreServices = computed(() => currentPage.value < totalPages.value);
    const pendingCartServices = ref([]);
    const cartConflictMessage = ref("Your cart contains items from a different salon. Would you like to clear the cart and add these services?");
    const salon = ref({
      name: "Loading...",
      location: "MG Road, Mumbai",
      image: "/images/saloon-small-image.png"
    });
    const selectedServices = computed(() => {
      return bookingStore.selectedServices.map((service) => ({
        id: service.id,
        name: service.name,
        duration: service.duration,
        price: service.price,
        type: "service"
      }));
    });
    const selectedCategoryName = computed(() => {
      var _a;
      return ((_a = categories.value[selectedCategoryIndex.value]) == null ? void 0 : _a.name) || "Services";
    });
    const canContinue = computed(() => {
      return bookingStore.selectedServices.length > 0 || cartServiceIds.value.length > 0;
    });
    const fetchCartData = async () => {
      var _a, _b, _c, _d, _e, _f, _g;
      isLoading.value = true;
      const authToken = useCookie("authToken");
      const guestId = useCookie("guestId");
      try {
        const headers = {
          "Content-Type": "application/json"
        };
        if (authToken.value) headers["token"] = authToken.value;
        if (guestId.value) headers["guestid"] = guestId.value;
        const response = await fetch(`${BASE_URL}/v1/client/cart/get-cart`, {
          method: "GET",
          headers
        });
        const data = await response.json();
        if (data.status_code === 401) {
          (_a = snackbarRef.value) == null ? void 0 : _a.showSnackbar(data.msg || "Unauthorized. Logging out...");
          userStore.logout();
          return;
        }
        if (data.status_code === 400) {
          (_b = snackbarRef.value) == null ? void 0 : _b.showSnackbar(data.msg || "Error fetching data");
          return;
        }
        if (data.status && data.status_code === 200) {
          const services = ((_e = (_d = (_c = data.data) == null ? void 0 : _c.cart) == null ? void 0 : _d.bookingId) == null ? void 0 : _e.services) || [];
          cartServiceIds.value = services.map((s) => {
            var _a2;
            return (_a2 = s.businesssubserviceId) == null ? void 0 : _a2._id;
          }).filter(Boolean);
          bookingStore.resetServices();
          services.forEach((s) => {
            const subService = s.businesssubserviceId;
            if (subService) {
              bookingStore.addService({
                id: subService._id,
                name: subService.name,
                duration: subService.duration,
                price: subService.price,
                category: "General"
              });
            }
          });
          await ((_f = bookingSummaryRef.value) == null ? void 0 : _f.fetchCartData());
        } else {
          throw new _sfc_main$1$1(data.msg || "Failed to load cart data");
        }
      } catch (error) {
        console.error("Error fetching cart data:", error);
        (_g = snackbarRef.value) == null ? void 0 : _g.showSnackbar("Failed to load cart data.", 3e3);
      } finally {
        isLoading.value = false;
      }
    };
    function isServiceSelected(serviceId) {
      const inCart = cartServiceIds.value.includes(serviceId);
      const inStore = bookingStore.selectedServices.some((s) => s.id === serviceId);
      return inCart || inStore;
    }
    async function confirmAddServicesToCart() {
      var _a, _b, _c, _d, _e, _f;
      showCartConflictModal.value = false;
      isLoading.value = true;
      try {
        const headers = {
          "Content-Type": "application/json"
        };
        const authToken = useCookie("authToken");
        const guestId = useCookie("guestId");
        if (authToken.value) headers["token"] = authToken.value;
        if (guestId.value) headers["guestid"] = guestId.value;
        const body = {
          businessName: salonSlug.value,
          services: pendingCartServices.value.map((service) => ({
            businesssubserviceId: service._id,
            staffId: null
          })),
          deleteCart: true
        };
        const response = await fetch(`${BASE_URL}/v1/client/cart/add-services`, {
          method: "POST",
          headers,
          body: JSON.stringify(body)
        });
        const data = await response.json();
        if (data.status_code === 401) {
          (_a = snackbarRef.value) == null ? void 0 : _a.showSnackbar("Unauthorized. Logging out...", 5e3);
          userStore.logout();
          return;
        }
        if (data.status_code === 400) {
          (_b = snackbarRef.value) == null ? void 0 : _b.showSnackbar(data.msg || "Error fetching data");
          return;
        }
        if (data.status_code === 404) {
          (_c = snackbarRef.value) == null ? void 0 : _c.showSnackbar(data.msg || "Not found.", 3e3);
          return;
        }
        if (data.status && data.status_code === 200) {
          (_d = snackbarRef.value) == null ? void 0 : _d.showSnackbar("Services added to cart successfully.");
          bookingStore.resetServices();
          pendingCartServices.value.forEach((service) => {
            bookingStore.addService({
              id: service._id,
              name: service.name,
              duration: service.duration,
              price: service.price,
              category: service.category
            });
          });
          pendingCartServices.value = [];
          await fetchCartData();
          await ((_e = bookingSummaryRef.value) == null ? void 0 : _e.fetchCartData());
        } else {
          throw new _sfc_main$1$1(data.msg || "Failed to add services to cart.");
        }
      } catch (error) {
        console.error("Error adding services to cart:", error);
        (_f = snackbarRef.value) == null ? void 0 : _f.showSnackbar("Failed to add services to cart.", 3e3);
      } finally {
        isLoading.value = false;
      }
    }
    function removeService(serviceId) {
      bookingStore.removeService(serviceId);
      removeServiceFromCart(serviceId);
    }
    function formatPromoType(type) {
      return type.toLowerCase().split("_").map((word) => word.charAt(0).toUpperCase() + word.slice(1)).join(" ");
    }
    function goToNextStep() {
      if (canContinue.value) {
        router.push({ path: "/booking/professionals", query: { slug: salonSlug.value } });
      }
    }
    useAppSeoMeta({
      title: computed(() => `Book Salon Services | ${salon.value.name}`),
      meta: [
        {
          name: "description",
          content: computed(() => `Book haircuts, hairstyling, and other grooming services at ${salon.value.name}. Choose your preferred services and professionals easily online.`)
        },
        {
          name: "keywords",
          content: "salon booking, hair styling, grooming, beauty services"
        },
        {
          name: "robots",
          content: "index, follow"
        },
        {
          property: "og:title",
          content: computed(() => `Book Your Salon Appointment | ${salon.value.name}`)
        },
        {
          property: "og:description",
          content: computed(() => `Easily book salon services like haircut, grooming, and hairstyling at ${salon.value.name}.`)
        },
        {
          property: "og:type",
          content: "website"
        },
        {
          property: "og:url",
          content: computed(() => `https://www.Bookslotz.com${route.fullPath}`)
        },
        {
          property: "og:image",
          content: computed(() => salon.value.image)
        },
        {
          name: "twitter:card",
          content: "summary_large_image"
        },
        {
          name: "twitter:title",
          content: computed(() => `Book Salon Services at ${salon.value.name}`)
        },
        {
          name: "twitter:description",
          content: computed(() => `Choose your services and book appointments online at ${salon.value.name}.`)
        },
        {
          name: "twitter:image",
          content: computed(() => salon.value.image)
        }
      ],
      link: [
        {
          rel: "canonical",
          href: computed(() => `https://www.Bookslotz.com${route.fullPath}`)
        }
      ]
    });
    ref(null);
    const showLeftArrow = ref(false);
    const showRightArrow = ref(false);
    return (_ctx, _push, _parent, _attrs) => {
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "min-h-screen bg-white relative" }, _attrs))}>`);
      if (unref(isLoading)) {
        _push(ssrRenderComponent(Loader, null, null, _parent));
      } else {
        _push(`<!---->`);
      }
      _push(ssrRenderComponent(Snackbar, {
        ref_key: "snackbarRef",
        ref: snackbarRef
      }, null, _parent));
      _push(ssrRenderComponent(_sfc_main$1, {
        isOpen: showCartConflictModal.value,
        title: "Cart Conflict",
        message: cartConflictMessage.value,
        onCancel: ($event) => showCartConflictModal.value = false,
        onConfirm: confirmAddServicesToCart
      }, null, _parent));
      if (hasError.value) {
        _push(ssrRenderComponent(_sfc_main$1$1, { error: { statusCode: 500, message: "Something went wrong. Please try again later." } }, null, _parent));
      } else {
        _push(`<!---->`);
      }
      if (!hasError.value) {
        _push(`<div class="flex flex-col xl:flex-row max-w-8xl mx-auto px-1 sm:px-16 md:px-12 justify-between"><div class="flex-1 lg:flex-[0.5] px-6 lg:px-8 py-6 lg:py-8"><div class="flex items-center space-x-3 mb-6"><button class="w-10 h-10 rounded-full border border-gray-300 flex items-center justify-center group cursor-pointer"><svg xmlns="http://www.w3.org/2000/svg" class="w-7 h-7 text-purple-600 group-hover:text-purple-700 transition-colors" viewBox="0 0 24 24" fill="currentColor"><path d="m6.523 12.5l3.735 3.735q.146.146.153.344q.006.198-.153.363q-.166.166-.357.168t-.357-.162l-4.382-4.383q-.243-.242-.243-.565t.243-.566l4.382-4.382q.147-.146.347-.153q.201-.007.367.159q.16.165.162.353q.003.189-.162.354L6.523 11.5h12.38q.214 0.358.143t.143.357t-.143.357t-.357.143z"></path></svg></button><h1 class="text-[24px] font-semibold text-[700]">Select Services</h1></div><div class="relative mb-8"><button class="absolute left-0 top-1/2 -translate-y-1/2 z-10 bg-white border border-gray-300 rounded-full w-8 h-8 flex items-center justify-center shadow transition hover:bg-gray-100 cursor-pointer" style="${ssrRenderStyle([
          showLeftArrow.value ? null : { display: "none" },
          { "box-shadow": "0 2px 8px rgba(0,0,0,0.06)" }
        ])}"><svg class="w-5 h-5 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path></svg></button><div class="flex gap-2 flex-nowrap max-w-2xl sm:max-w-4xl xl:max-w-2xl overflow-x-auto hide-scrollbar" style="${ssrRenderStyle({ "scroll-behavior": "smooth" })}"><!--[-->`);
        ssrRenderList(categories.value, (cat, idx) => {
          _push(`<span class="${ssrRenderClass([{
            "text-white border-[#3A3A3A]": idx === selectedCategoryIndex.value,
            "bg-white text-black border-gray-300 hover:bg-gray-50": idx !== selectedCategoryIndex.value
          }, "text-[15px] text-[500] px-4 py-2 rounded-full whitespace-nowrap cursor-pointer transition-colors border flex-shrink-0"])}" style="${ssrRenderStyle(idx === selectedCategoryIndex.value ? { backgroundColor: "rgba(58, 58, 58, 1)" } : {})}">${ssrInterpolate(cat.name)}</span>`);
        });
        _push(`<!--]--></div><button class="absolute right-0 top-1/2 -translate-y-1/2 z-10 bg-white border border-gray-300 rounded-full w-8 h-8 flex items-center justify-center shadow transition hover:bg-gray-100 cursor-pointer" style="${ssrRenderStyle([
          showRightArrow.value ? null : { display: "none" },
          { "box-shadow": "0 2px 8px rgba(0,0,0,0.06)" }
        ])}"><svg class="w-5 h-5 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path></svg></button></div><div class="flex flex-col items-center sm:block"><h2 class="text-[20px] font-[700] mb-4 text-center sm:text-left">${ssrInterpolate(selectedCategoryName.value)}</h2><div class="space-y-3 w-full sm:w-auto">`);
        if (currentServices.value.length === 0) {
          _push(`<div class="text-center py-12"><div class="text-gray-400 mb-4"><svg class="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path></svg></div><p class="text-gray-500 text-lg">No Services Available</p></div>`);
        } else {
          _push(`<!--[-->`);
          ssrRenderList(currentServices.value, (service) => {
            _push(`<div class="flex flex-col sm:flex-row justify-between items-center p-4 rounded-lg bg-white hover:bg-gray-50 transition w-full sm:w-auto"><div class="flex-1 text-center sm:text-left"><h3 class="text-[18px] font-[500] dm-sanssemibold mb-1">${ssrInterpolate(service.name)}</h3><div class="flex items-center gap-1 mb-2 justify-center sm:justify-start"><svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 text-black" viewBox="0 0 24 24"><path fill="currentColor" d="M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2M12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8s8 3.58 8 8s-3.58 8-8 8"></path><path fill="currentColor" d="M12.5 7H11v6l5.25 3.15l.75-1.23l-4.5-2.67z"></path></svg><span class="text-[16px] font-[500] text-gray-500">${ssrInterpolate(service.duration)} mins</span></div><p class="text-[16px] font-[500] inter-semibold text-gray-900"> From `);
            if (service.promotionApplied) {
              _push(`<!--[--><span class="line-through text-gray-400 mr-2 font-[500] inter-semibold">\u20B9${ssrInterpolate(service.originalPrice)}</span><span class="text-green-600 font-[500] inter-semibold">\u20B9${ssrInterpolate(service.finalPrice)}</span>`);
              if (service.promotionApplied.type) {
                _push(`<span class="ml-2 text-xs font-semibold px-2 py-0.5 rounded-full" style="${ssrRenderStyle({ "background": "linear-gradient(90deg, #e0c3fc 0%, #8ec5fc 100%)", "color": "#5a189a" })}">${ssrInterpolate(formatPromoType(service.promotionApplied.type))}</span>`);
              } else {
                _push(`<!---->`);
              }
              _push(`<!--]-->`);
            } else {
              _push(`<!--[--> \u20B9${ssrInterpolate(service.price)}<!--]-->`);
            }
            _push(`</p></div><button class="${ssrRenderClass([isServiceSelected(service._id) ? "bg-purple-600 text-white hover:bg-purple-700" : "bg-white border border-gray-300 text-gray-700 hover:bg-gray-50", "mt-4 sm:mt-0 px-10 py-2 font-semibold rounded-full transition-colors text-[16px] text-[600] cursor-pointer"])}">${ssrInterpolate(isServiceSelected(service._id) ? "Added" : "Book")}</button></div>`);
          });
          _push(`<!--]-->`);
        }
        if (hasMoreServices.value) {
          _push(`<div class="flex justify-center mt-6"><button class="bg-white hover:bg-purple-600 text-black hover:text-white font-semibold py-2 px-6 rounded-full transition border border-gray-300 flex items-center gap-2 cursor-pointer"${ssrIncludeBooleanAttr(isLoadingServices.value) ? " disabled" : ""}>`);
          if (isLoadingServices.value) {
            _push(`<span>Loading...</span>`);
          } else {
            _push(`<!--[--><span>Load More</span><svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path></svg><!--]-->`);
          }
          _push(`</button></div>`);
        } else {
          _push(`<!---->`);
        }
        _push(`</div></div></div>`);
        _push(ssrRenderComponent(_sfc_main$2, {
          ref_key: "bookingSummaryRef",
          ref: bookingSummaryRef,
          salon: salon.value,
          "selected-services": selectedServices.value,
          total: unref(bookingStore).total,
          "can-continue": canContinue.value,
          "show-order-summary": false,
          "show-professional-names": false,
          "professional-names": null,
          onContinue: goToNextStep,
          onRemoveService: removeService
        }, null, _parent));
        _push(`</div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div>`);
    };
  }
});
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/booking/index.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};

export { _sfc_main as default };
//# sourceMappingURL=index-C6IdsG3t.mjs.map
