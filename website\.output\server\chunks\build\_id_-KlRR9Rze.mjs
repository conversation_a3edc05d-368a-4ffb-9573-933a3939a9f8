import { _ as _export_sfc, c as useRuntimeConfig, b as useUserStore, d as useState, i as useCookie, L as Loader, S as Snackbar, e as _sfc_main$1$1, a as __nuxt_component_0$2 } from './server.mjs';
import { defineComponent, ref, computed, watch, mergeProps, unref, withCtx, createTextVNode, toDisplayString, useSSRContext } from 'vue';
import { ssrRenderAttrs, ssrRenderComponent, ssrInterpolate, ssrRenderStyle, ssrRenderList, ssrRenderAttr, ssrRenderClass, ssrIncludeBooleanAttr } from 'vue/server-renderer';
import { useRoute, useRouter } from 'vue-router';
import { u as useBookingStore } from './booking-D2Du41fq.mjs';
import { _ as _sfc_main$1 } from './Modal-BOTd-eEZ.mjs';
import { u as useAppSeoMeta } from './useAppSeoMeta-HWukB6FN.mjs';
import '../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:url';
import '@iconify/utils';
import 'node:crypto';
import 'consola';
import 'node:path';
import 'better-sqlite3';
import 'ipx';
import 'pinia';
import 'cookie';
import '@iconify/vue';
import 'tailwindcss/colors';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import 'unhead/server';
import 'devalue';
import 'unhead/plugins';
import 'unhead/utils';

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "[id]",
  __ssrInlineRender: true,
  setup(__props) {
    const route = useRoute();
    const router = useRouter();
    const { public: { BASE_URL } } = useRuntimeConfig();
    const userStore = useUserStore();
    const bookingStore = useBookingStore();
    const isLoading = useState("globalLoader", () => false);
    const isWishlisted = ref(false);
    const salonId = ref("");
    const showAllExperts = ref(false);
    const displayedExperts = computed(() => {
      if (showAllExperts.value) {
        return salonData.value.experts;
      }
      return salonData.value.experts.slice(0, 8);
    });
    const isLoadingServices = ref(false);
    const currentPage = ref(1);
    const totalPages = ref(1);
    const hasMoreServices = computed(() => currentPage.value < totalPages.value);
    const hasError = ref(false);
    const snackbarRef = ref(null);
    const salonSlug = computed(() => decodeURIComponent(route.params.id || ""));
    const showCartConflictModal = ref(false);
    ref(null);
    const showLeftArrow = ref(false);
    const showRightArrow = ref(false);
    const cartConflictMessage = ref("Your cart contains items from a different salon. Would you like to clear the cart and add these services?");
    const categories = ref([]);
    const authToken = useCookie("authToken");
    const guestId = useCookie("guestId");
    const selectedCategoryIndex = ref(0);
    const salonData = ref({
      name: "",
      images: [
        { src: "/images/saloon-banner1.png", alt: "Modern salon interior with styling chairs", id: 1 },
        { src: "/images/saloon-banner2.png", alt: "Professional hair styling station", id: 2 },
        { src: "/images/saloon-banner3.png", alt: "Client getting hair treatment", id: 3 },
        { src: "/images/saloon-banner3.png", alt: "Salon reception area", id: 4 },
        { src: "/images/saloon-banner2.png", alt: "Hair washing station", id: 5 },
        { src: "/images/saloon-banner1.png", alt: "Salon product display", id: 6 }
      ],
      rating: "4.5",
      reviews: "43",
      location: "MG Road, Mumbai",
      categories: [],
      services: [
        { _id: "1", name: "Haircut for Men", duration: "1hr - 45 mins", price: 499 },
        { _id: "2", name: "Hair Spa", duration: "1hr", price: 799 },
        { _id: "3", name: "Beard Styling", duration: "30 mins", price: 299 },
        { _id: "4", name: "Hair Coloring", duration: "1hr 30 mins", price: 1299 }
      ],
      about: "Experience professional nail services in a cozy, comfortable home setting. Our salon specializes in creating beautiful nails with a personal touch.",
      amenities: [
        { name: "Parking Available" },
        { name: "Baby-Friendly Environment" },
        { name: "Parking Available" },
        { name: "Baby-Friendly Environment" }
      ],
      experts: [
        { name: "Jacob Jones", role: "Haircut & Styling", img: "/images/expert1.png", rating: "4.5", reviews: 3 },
        { name: "Wade Warren", role: "Hair Coloring Expert", img: "/images/expert2.png", rating: "4.5", reviews: 3 },
        { name: "Jane Cooper", role: "Bridal Hairstylist", img: "/images/expert3.png", rating: "4.5", reviews: 3 },
        { name: "Kathryn Murphy", role: "Haircut & Styling", img: "/images/expert4.png", rating: "4.5", reviews: 3 }
      ],
      timings: {
        Sunday: "8:00 AM to 6:00 PM",
        Monday: "8:00 AM to 6:00 PM",
        Tuesday: "8:00 AM to 6:00 PM",
        Wednesday: "8:00 AM to 6:00 PM",
        Thursday: "8:00 AM to 6:00 PM",
        Friday: "8:00 AM to 6:00 PM"
      },
      closingTime: "10:00 pm",
      lat: 9.5916,
      lng: 76.5222
    });
    const selectedServices = ref([]);
    const category = computed(() => route.query.category || "");
    ref([]);
    const currentIndex = ref(0);
    const isMobile = ref(false);
    const pendingCartServices = ref([]);
    const visibleImages = computed(() => isMobile.value ? 1 : 3);
    const canGoPrevious = computed(() => currentIndex.value > 0);
    const canGoNext = computed(() => currentIndex.value < salonData.value.images.length - visibleImages.value);
    const totalSlides = computed(() => Math.max(0, salonData.value.images.length - visibleImages.value + 1));
    const showDots = computed(() => salonData.value.images.length > visibleImages.value);
    function formatPromoType(type) {
      return type.toLowerCase().split("_").map((word) => word.charAt(0).toUpperCase() + word.slice(1)).join(" ");
    }
    const fetchCategories = async () => {
      var _a, _b, _c;
      isLoading.value = true;
      hasError.value = false;
      try {
        const headers = {
          "Content-Type": "application/json"
        };
        if (userStore.token) headers["token"] = userStore.token;
        if (guestId.value) headers["guestId"] = guestId.value;
        const response = await fetch(`${BASE_URL}/v1/client/business/services/category-list?slug=${encodeURIComponent(salonSlug.value)}`, {
          method: "GET",
          headers
        });
        const data = await response.json();
        if (data.status_code === 401) {
          (_a = snackbarRef.value) == null ? void 0 : _a.showSnackbar(data.msg || "Unauthorized. Logging out...");
          userStore.logout();
          return;
        }
        if (data.status && data.status_code === 200) {
          categories.value = data.data.data.map((cat) => ({
            _id: cat._id,
            name: cat.name,
            slug: cat.slug
          }));
          (_b = snackbarRef.value) == null ? void 0 : _b.showSnackbar("Categories loaded successfully");
          if (categories.value.length > 0) {
            await fetchServicesByCategory(categories.value[0]._id, 0);
          }
        } else {
          throw new _sfc_main$1$1(data.msg || "Failed to load categories");
        }
      } catch (error) {
        console.error("Error fetching categories:", error);
        hasError.value = true;
        (_c = snackbarRef.value) == null ? void 0 : _c.showSnackbar("Failed to load categories. Please try again.", 3e3);
      } finally {
        isLoading.value = false;
      }
    };
    const fetchServicesByCategory = async (categoryId, index, page = 1) => {
      var _a, _b, _c;
      isLoadingServices.value = page === 1 ? true : false;
      isLoading.value = page === 1 ? true : false;
      hasError.value = false;
      selectedCategoryIndex.value = index;
      if (page === 1) {
        salonData.value.services = [];
        currentPage.value = 1;
      }
      try {
        const url = new URL(`${BASE_URL}/v1/client/business/services/salon-list`);
        url.searchParams.append("businessMainServiceId", categoryId);
        url.searchParams.append("slug", encodeURIComponent(salonSlug.value));
        url.searchParams.append("page", page.toString());
        url.searchParams.append("limit", "10");
        const headers = {
          "Content-Type": "application/json"
        };
        if (userStore.token) headers["token"] = userStore.token;
        if (guestId.value) headers["guestId"] = guestId.value;
        const response = await fetch(url, {
          method: "GET",
          headers
        });
        const data = await response.json();
        if (data.status_code === 401) {
          (_a = snackbarRef.value) == null ? void 0 : _a.showSnackbar(data.msg || "Unauthorized. Logging out...");
          userStore.logout();
          return;
        }
        if (data.status && data.status_code === 200) {
          const newServices = data.data.data.map((service) => ({
            _id: service._id,
            name: service.name,
            duration: service.duration,
            price: service.originalPrice,
            originalPrice: service.originalPrice,
            finalPrice: service.finalPrice,
            promotionApplied: service.promotionApplied
          }));
          salonData.value.services = page === 1 ? newServices : [...salonData.value.services, ...newServices];
          currentPage.value = data.data.page;
          totalPages.value = data.data.totalPage;
          (_b = snackbarRef.value) == null ? void 0 : _b.showSnackbar("Services loaded successfully");
        } else {
          throw new _sfc_main$1$1(data.msg || "Failed to load services");
        }
      } catch (error) {
        console.error("Error fetching services:", error);
        hasError.value = true;
        (_c = snackbarRef.value) == null ? void 0 : _c.showSnackbar("Failed to load services. Please try again.", 3e3);
      } finally {
        isLoading.value = false;
        isLoadingServices.value = false;
      }
    };
    const fetchSalonData = async () => {
      var _a, _b, _c, _d, _e, _f, _g;
      isLoading.value = true;
      hasError.value = false;
      try {
        const headers = {
          "Content-Type": "application/json"
        };
        if (userStore.token) headers["token"] = userStore.token;
        if (guestId.value) headers["guestId"] = guestId.value;
        const response = await fetch(`${BASE_URL}/v1/client/business/salon-view/${encodeURIComponent(salonSlug.value)}`, {
          method: "GET",
          headers
        });
        const data = await response.json();
        if (data.status_code === 401) {
          (_a = snackbarRef.value) == null ? void 0 : _a.showSnackbar(data.msg || "Unauthorized. Logging out...");
          userStore.logout();
          return;
        }
        if (data.status && data.status_code === 200) {
          const salon = data.data.business;
          const salondataview = data.data;
          const workingDays = data.data.workingDays;
          salonId.value = salon._id;
          isWishlisted.value = data.data.wishlist || false;
          const timings = {};
          let closingTime = "10:00 pm";
          const currentDay = (/* @__PURE__ */ new Date()).toLocaleString("en-US", { weekday: "long" });
          workingDays.forEach((day) => {
            if (day.isClosed) {
              timings[day.day] = "Closed";
            } else {
              timings[day.day] = `${day.startTimeString} to ${day.endTimeString}`;
              if (day.day === currentDay) {
                closingTime = day.endTimeString;
              }
            }
          });
          if (!timings[currentDay]) {
            const sunday = workingDays.find((day) => day.day === "Sunday");
            if (sunday && !sunday.isClosed) {
              closingTime = sunday.endTimeString;
            }
          }
          salonData.value = {
            name: salon.salonname || salon.businessname || salonSlug.value,
            images: ((_b = salon.workspaceimages) == null ? void 0 : _b.map((img, index) => ({
              src: getImageUrl(img),
              alt: `Salon image ${index + 1}`,
              id: index
            }))) || salonData.value.images,
            rating: salon.rating || "4.5",
            reviews: salon.reviews || "43",
            location: salon.salonAddress || salon.address || "MG Road, Mumbai",
            categories: [],
            services: ((_c = salon.services) == null ? void 0 : _c.map((service) => ({
              _id: service._id,
              name: service.name,
              duration: service.duration,
              price: service.price
            }))) || salonData.value.services,
            about: salon.description || salonData.value.about,
            amenities: ((_d = salon.amenitiesIds) == null ? void 0 : _d.map((amenity) => ({ name: amenity.name }))) || salonData.value.amenities,
            experts: ((_e = salondataview.staffs) == null ? void 0 : _e.map((expert) => {
              var _a2;
              return {
                name: expert.name,
                role: expert.proffession || "Staff",
                img: ((_a2 = expert.loginId) == null ? void 0 : _a2.profileImage) ? getImageUrl(expert.loginId.profileImage) : "/images/expert1.png",
                rating: expert.rating || "4.5",
                reviews: expert.reviews || 3
              };
            })) || salonData.value.experts,
            timings,
            closingTime,
            lat: salon.salonlocation[0] || 9.5916,
            lng: salon.salonlocation[1] || 76.5222
          };
          (_f = snackbarRef.value) == null ? void 0 : _f.showSnackbar(data.msg || "Salon data loaded successfully");
        } else {
          throw new _sfc_main$1$1(data.msg || "Failed to load salon data");
        }
      } catch (error) {
        console.error("Error fetching salon data:", error);
        hasError.value = true;
        (_g = snackbarRef.value) == null ? void 0 : _g.showSnackbar("Failed to load salon data. Please try again.", 3e3);
      } finally {
        isLoading.value = false;
      }
    };
    const getImageUrl = (image) => {
      if (!image) return "/icon/saloon-image.jpg";
      if (image.startsWith("http")) return image;
      return `${BASE_URL}/wp/${image}`;
    };
    ref(false);
    function isServiceSelected(serviceId) {
      return selectedServices.value.some((service) => service._id === serviceId);
    }
    const confirmAddServicesToCart = async () => {
      var _a, _b, _c, _d;
      showCartConflictModal.value = false;
      isLoading.value = true;
      try {
        const headers = {
          "Content-Type": "application/json"
        };
        if (authToken.value) headers["token"] = authToken.value;
        if (guestId.value) headers["guestid"] = guestId.value;
        const body = {
          businessName: salonSlug.value,
          services: pendingCartServices.value.map((service) => ({
            businesssubserviceId: service._id,
            staffId: null
          })),
          deleteCart: true
        };
        const response = await fetch(`${BASE_URL}/v1/client/cart/add-services`, {
          method: "POST",
          headers,
          body: JSON.stringify(body)
        });
        const data = await response.json();
        if (data.status_code === 401) {
          (_a = snackbarRef.value) == null ? void 0 : _a.showSnackbar(data.msg || "Unauthorized. Logging out...");
          userStore.logout();
          return;
        }
        if (data.status_code === 404) {
          (_b = snackbarRef.value) == null ? void 0 : _b.showSnackbar(data.msg || "Not found.", 3e3);
          return;
        }
        if (data.status && data.status_code === 200) {
          (_c = snackbarRef.value) == null ? void 0 : _c.showSnackbar("Services added to cart successfully");
          bookingStore.resetServices();
          pendingCartServices.value.forEach((service) => {
            var _a2;
            bookingStore.addService({
              id: service._id,
              name: service.name,
              duration: service.duration,
              price: service.price,
              category: ((_a2 = categories.value[selectedCategoryIndex.value]) == null ? void 0 : _a2.name) || "Unknown"
            });
          });
          pendingCartServices.value = [];
          router.push({ path: "/booking", query: { slug: salonSlug.value } });
        } else {
          throw new _sfc_main$1$1(data.msg || "Failed to add services to cart");
        }
      } catch (error) {
        console.error("Error adding services to cart:", error);
        (_d = snackbarRef.value) == null ? void 0 : _d.showSnackbar("Failed to add services to cart. Please try again.", 3e3);
      } finally {
        isLoading.value = false;
      }
    };
    let mapInstance = null;
    watch(
      () => [salonData.value.lat, salonData.value.lng],
      () => {
        if ((void 0).google && mapInstance) ;
      }
    );
    const reviewFilters = ["ALL", "CLEANLINESS", "SERVICE QUALITY", "STAFF BEHAVIOR", "VALUE FOR MONEY"];
    const selectedReviewFilter = ref(0);
    const reviews = ref([
      {
        id: 1,
        name: "Samantha Payne",
        avatar: "/images/review-image.png",
        rating: 4.5,
        date: "19/02/2025",
        text: "Lorem ipsum dolor sit amet, consectetur adipiscing elit ut aliquam, purus sit amet luctus venenatis, lectus magna fringilla urna, porttitor rhoncus dolor purus non enim praesent elementum facilisis leo, vel",
        scores: ["Ambience & Cleanliness 3/5", "Staff Behavior 3/5", "Staff Behavior 3/5"]
      },
      {
        id: 2,
        name: "Samantha Payne",
        avatar: "/images/review-image.png",
        rating: 4.5,
        date: "19/02/2025",
        text: "Lorem ipsum dolor sit amet, consectetur adipiscing elit ut aliquam, purus sit amet luctus venenatis, lectus magna fringilla urna, porttitor rhoncus dolor purus non enim praesent elementum facilisis leo, vel",
        scores: ["Ambience & Cleanliness 3/5", "Staff Behavior 3/5", "Staff Behavior 3/5"]
      }
    ]);
    const filteredReviews = computed(() => {
      if (selectedReviewFilter.value === 0) return reviews.value;
      const filter = reviewFilters[selectedReviewFilter.value].toLowerCase();
      return reviews.value.filter((r) => r.scores.some((s) => s.toLowerCase().includes(filter)));
    });
    useAppSeoMeta({
      title: computed(() => `${salonData.value.name || salonSlug.value} | Bookslotz`),
      description: computed(() => `Book appointments at ${salonData.value.name || salonSlug.value}. View services, timings, and more at Bookslotz.`),
      image: computed(() => {
        var _a;
        return ((_a = salonData.value.images[0]) == null ? void 0 : _a.src) || "/og-image.jpg";
      }),
      url: computed(() => `https://www.Bookslotz.com${route.fullPath}`)
    });
    watch(() => route.params.id, (newId, oldId) => {
      if (newId !== oldId) {
        fetchSalonData();
        fetchCategories();
      }
    });
    return (_ctx, _push, _parent, _attrs) => {
      const _component_NuxtLink = __nuxt_component_0$2;
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "min-h-screen bg-white relative" }, _attrs))} data-v-2d535f29>`);
      if (unref(isLoading)) {
        _push(ssrRenderComponent(Loader, null, null, _parent));
      } else {
        _push(`<!---->`);
      }
      _push(ssrRenderComponent(Snackbar, {
        ref_key: "snackbarRef",
        ref: snackbarRef
      }, null, _parent));
      _push(ssrRenderComponent(_sfc_main$1, {
        isOpen: showCartConflictModal.value,
        title: "Cart Conflict",
        message: cartConflictMessage.value,
        onCancel: ($event) => showCartConflictModal.value = false,
        onConfirm: confirmAddServicesToCart
      }, null, _parent));
      if (hasError.value) {
        _push(ssrRenderComponent(_sfc_main$1$1, { error: { statusCode: 500, message: "Something went wrong. Please try again later." } }, null, _parent));
      } else {
        _push(`<!---->`);
      }
      if (!hasError.value) {
        _push(`<section class="bg-white pt-6 pb-2" data-v-2d535f29><nav class="text-sm text-gray-700 mb-4 flex items-center space-x-2 px-20" data-v-2d535f29>`);
        _push(ssrRenderComponent(_component_NuxtLink, {
          to: "/",
          class: "hover:underline cursor-pointer text-gray-500"
        }, {
          default: withCtx((_, _push2, _parent2, _scopeId) => {
            if (_push2) {
              _push2(`Home`);
            } else {
              return [
                createTextVNode("Home")
              ];
            }
          }),
          _: 1
        }, _parent));
        _push(`<span data-v-2d535f29>&gt;</span>`);
        if (category.value) {
          _push(ssrRenderComponent(_component_NuxtLink, {
            to: `/category/${category.value}`,
            class: "hover:underline cursor-pointer text-gray-500"
          }, {
            default: withCtx((_, _push2, _parent2, _scopeId) => {
              if (_push2) {
                _push2(`${ssrInterpolate(_ctx.formatCategory(category.value))}`);
              } else {
                return [
                  createTextVNode(toDisplayString(_ctx.formatCategory(category.value)), 1)
                ];
              }
            }),
            _: 1
          }, _parent));
        } else {
          _push(`<!---->`);
        }
        if (category.value) {
          _push(`<span data-v-2d535f29>&gt;</span>`);
        } else {
          _push(`<!---->`);
        }
        _push(`<span class="font-semibold text-black" data-v-2d535f29>${ssrInterpolate(salonData.value.name || "Loading...")}</span></nav><div class="relative" data-v-2d535f29><div class="overflow-hidden rounded-lg" data-v-2d535f29><div class="flex transition-transform duration-300 ease-in-out gap-2" style="${ssrRenderStyle({ transform: `translateX(-${currentIndex.value * (isMobile.value ? 100 : 100 / 3)}%)` })}" data-v-2d535f29><!--[-->`);
        ssrRenderList(salonData.value.images, (image, index) => {
          _push(`<div class="flex-shrink-0 w-full md:w-1/3" data-v-2d535f29><img${ssrRenderAttr("src", image.src)}${ssrRenderAttr("alt", image.alt)} class="w-full h-full object-contain rounded-lg" loading="lazy" format="webp" data-v-2d535f29></div>`);
        });
        _push(`<!--]--></div></div>`);
        if (canGoPrevious.value) {
          _push(`<button class="absolute left-1 top-1/2 transform -translate-y-1/2 bg-white rounded-full shadow-lg p-1 transition-all duration-200 hover:scale-110 z-10" aria-label="Previous images" data-v-2d535f29><svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6 text-purple-500" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="3" data-v-2d535f29><path stroke-linecap="round" stroke-linejoin="round" d="M15 19l-7-7 7-7" data-v-2d535f29></path></svg></button>`);
        } else {
          _push(`<!---->`);
        }
        if (canGoNext.value) {
          _push(`<button class="absolute right-1 top-1/2 transform -translate-y-1/2 bg-white/80 hover:bg-white rounded-full shadow-lg p-1 transition-all duration-200 hover:scale-110 z-10" aria-label="Next images" data-v-2d535f29><svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6 text-purple-500" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="3" data-v-2d535f29><path stroke-linecap="round" stroke-linejoin="round" d="M9 5l7 7-7 7" data-v-2d535f29></path></svg></button>`);
        } else {
          _push(`<!---->`);
        }
        if (showDots.value) {
          _push(`<div class="flex justify-center mt-4 space-x-2" data-v-2d535f29><!--[-->`);
          ssrRenderList(totalSlides.value, (dot, index) => {
            _push(`<button class="${ssrRenderClass([currentIndex.value === index ? "bg-gray-800" : "bg-gray-300 hover:bg-gray-400", "w-2 h-2 rounded-full transition-all duration-200"])}"${ssrRenderAttr("aria-label", `Go to slide ${index + 1}`)} data-v-2d535f29></button>`);
          });
          _push(`<!--]--></div>`);
        } else {
          _push(`<!---->`);
        }
        _push(`</div></section>`);
      } else {
        _push(`<!---->`);
      }
      _push(`<section class="max-w-8xl mx-auto px-4 sm:px-16 md:px-20 py-8" data-v-2d535f29><div class="flex flex-col lg:flex-row gap-8" data-v-2d535f29><div class="flex-[2] min-w-0 w-full" data-v-2d535f29><div class="mb-2 max-w-3xl" data-v-2d535f29><div class="flex items-center justify-between" data-v-2d535f29><span class="text-3xl md:text-[45px] font-[700] dm-sansmedium" data-v-2d535f29>${ssrInterpolate(salonData.value.name || "Loading...")}</span><div class="hidden sm:flex gap-3" data-v-2d535f29><button class="w-12 h-12 flex items-center justify-center border border-gray-300 rounded-xl hover:bg-gray-100 transition" aria-label="Share" data-v-2d535f29><svg xmlns="http://www.w3.org/2000/svg" width="23" height="23" viewBox="0 0 24 24" fill="currentColor" data-v-2d535f29><path d="M18 16.08c-.76 0-1.44.3-1.96.77L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81
                    c1.66 0 3-1.34 3-3s-1.34-3-3-3s-3 1.34-3 3c0 .24.04.47.09.7L8.04 9.81C7.5 9.31 6.79 9 6 9c-1.66 0-3 1.34-3 3s1.34 3 
                    3 3c.79 0 1.5-.31 2.04-.81l7.12 4.16c-.05.21-.08.43-.08.65c0 1.61 1.31 2.92 2.92 2.92s2.92-1.31 2.92-2.92
                    s-1.31-2.92-2.92-2.92" data-v-2d535f29></path></svg></button><button class="w-12 h-12 flex items-center justify-center border border-gray-300 rounded-xl hover:bg-gray-100 transition cursor-pointer" aria-label="Favorite" data-v-2d535f29><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="${ssrRenderClass([isWishlisted.value ? "text-red-500" : "text-gray-400", "w-6 h-6"])}"${ssrRenderAttr("fill", isWishlisted.value ? "currentColor" : "none")} stroke="currentColor" stroke-width="2" data-v-2d535f29><path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 
        4.42 3 7.5 3c1.74 0 3.41 0.81 4.5 2.09C13.09 3.81 
        14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 
        3.78-3.4 6.86-8.55 11.54L12 21.35z" data-v-2d535f29></path></svg></button></div></div><div class="flex gap-3 mt-3 sm:hidden" data-v-2d535f29><button class="w-12 h-12 flex items-center justify-center border border-gray-300 rounded-xl hover:bg-gray-100 transition" aria-label="Share" data-v-2d535f29><svg xmlns="http://www.w3.org/2000/svg" width="23" height="23" viewBox="0 0 24 24" fill="currentColor" data-v-2d535f29><path d="M18 16.08c-.76 0-1.44.3-1.96.77L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81
                  c1.66 0 3-1.34 3-3s-1.34-3-3-3s-3 1.34-3 3c0 .24.04.47.09.7L8.04 9.81C7.5 9.31 6.79 9 6 9c-1.66 0-3 1.34-3 3s1.34 3 
                  3 3c.79 0 1.5-.31 2.04-.81l7.12 4.16c-.05.21-.08.43-.08.65c0 1.61 1.31 2.92 2.92 2.92s2.92-1.31 2.92-2.92
                    s-1.31-2.92-2.92-2.92" data-v-2d535f29></path></svg></button><button class="w-12 h-12 flex items-center justify-center border border-gray-300 rounded-xl hover:bg-gray-100 transition cursor-pointer" aria-label="Favorite" data-v-2d535f29><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="${ssrRenderClass([isWishlisted.value ? "text-red-500" : "text-gray-400", "w-6 h-6"])}"${ssrRenderAttr("fill", isWishlisted.value ? "currentColor" : "none")} stroke="currentColor" stroke-width="2" data-v-2d535f29><path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 
      4.42 3 7.5 3c1.74 0 3.41 0.81 4.5 2.09C13.09 3.81 
      14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 
      3.78-3.4 6.86-8.55 11.54L12 21.35z" data-v-2d535f29></path></svg></button></div></div><div class="flex flex-col sm:flex-row sm:items-center gap-2 py-3 mb-2" data-v-2d535f29><div class="flex items-center gap-3" data-v-2d535f29><span class="rounded-full font-[700] dm-sansmedium" style="${ssrRenderStyle({ "background": "rgba(237,241,255,1)", "color": "rgba(25,118,210,1)", "font-size": "14px", "padding": "6px 24px" })}" data-v-2d535f29> SALOON </span><span class="flex items-center gap-1" data-v-2d535f29><svg class="w-7 h-7" fill="#FFC107" viewBox="0 0 24 24" data-v-2d535f29><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" data-v-2d535f29></path></svg><span class="text-black font-medium text-[19px] leading-none" data-v-2d535f29>${ssrInterpolate(salonData.value.rating || "4.5")} (${ssrInterpolate(salonData.value.reviews || "43")})</span></span></div><span class="flex items-center gap-1 text-gray-600 text-[16px] font-[400]" data-v-2d535f29><svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6" viewBox="0 0 24 24" data-v-2d535f29><path fill="currentColor" d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7M7 9c0-2.76 2.24-5 5-5s5 2.24 5 5c0 2.88-2.88 7.19-5 9.88C9.92 16.21 7 11.85 7 9" data-v-2d535f29></path><circle cx="12" cy="9" r="2.5" fill="currentColor" data-v-2d535f29></circle></svg> ${ssrInterpolate(salonData.value.location || "MG Road, Mumbai")}</span></div><div class="flex flex-wrap" data-v-2d535f29><span class="text-[24px] text-[700] font-semibold mb-3" data-v-2d535f29>Services</span></div><div class="relative mb-4 max-w-3xl" data-v-2d535f29><button class="absolute left-0 top-1/2 -translate-y-1/2 z-10 bg-white border border-gray-300 rounded-full w-8 h-8 flex items-center justify-center shadow transition hover:bg-gray-100 cursor-pointer" style="${ssrRenderStyle([
        showLeftArrow.value ? null : { display: "none" },
        { "box-shadow": "0 2px 8px rgba(0,0,0,0.06)" }
      ])}" data-v-2d535f29><svg class="w-5 h-5 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-2d535f29><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" data-v-2d535f29></path></svg></button><div class="flex overflow-x-auto scroll-smooth gap-3 hide-scrollbar" style="${ssrRenderStyle({ "scroll-behavior": "smooth" })}" data-v-2d535f29><!--[-->`);
      ssrRenderList(categories.value, (cat, idx) => {
        _push(`<button class="${ssrRenderClass([[
          "text-[15px] font-[500] px-4 py-2 rounded-full transition border",
          selectedCategoryIndex.value === idx ? "text-white border-gray-800" : "text-black border-gray-400 bg-white"
        ], "cursor-pointer flex-shrink-0"])}" style="${ssrRenderStyle(selectedCategoryIndex.value === idx ? "background:rgba(58,58,58,1);" : "")}" data-v-2d535f29>${ssrInterpolate(cat.name)}</button>`);
      });
      _push(`<!--]--></div><button class="absolute right-0 top-1/2 -translate-y-1/2 z-10 bg-white border border-gray-300 rounded-full w-8 h-8 flex items-center justify-center shadow transition hover:bg-gray-100 cursor-pointer" style="${ssrRenderStyle([
        showRightArrow.value ? null : { display: "none" },
        { "box-shadow": "0 2px 8px rgba(0,0,0,0.06)" }
      ])}" data-v-2d535f29><svg class="w-5 h-5 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-2d535f29><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" data-v-2d535f29></path></svg></button></div><div class="space-y-4 max-w-3xl flex flex-col items-center sm:block" data-v-2d535f29>`);
      if (salonData.value.services.length === 0) {
        _push(`<div class="text-[16px] text-gray-500 text-center p-4" data-v-2d535f29> No Services Available </div>`);
      } else {
        _push(`<!--[-->`);
        ssrRenderList(salonData.value.services, (service) => {
          _push(`<div class="rounded-lg p-4 flex flex-col sm:flex-row items-center justify-between transition-colors duration-200 hover:bg-gray-100 w-full sm:w-auto" data-v-2d535f29><div class="flex-1 text-center sm:text-left" data-v-2d535f29><div class="font-[500] dm-sanssemibold text-[18px] mb-2" data-v-2d535f29>${ssrInterpolate(service.name)}</div><div class="text-[16px] font-[500] text-gray-500 flex items-center gap-2 mb-2 justify-center sm:justify-start" data-v-2d535f29><svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 inline" viewBox="0 0 24 24" fill="#000" data-v-2d535f29><path fill="#000" d="M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2M12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8s8 3.58 8 8s-3.58 8-8 8" data-v-2d535f29></path><path fill="#000" d="M12.5 7H11v6l5.25 3.15l.75-1.23l-4.5-2.67z" data-v-2d535f29></path></svg> ${ssrInterpolate(service.duration)} mins </div><div class="text-[16px] font-[500] inter-semibold mt-1" data-v-2d535f29> From `);
          if (service.promotionApplied) {
            _push(`<!--[--><span class="line-through text-gray-400 mr-2 font-[500] inter-semibold" data-v-2d535f29>\u20B9${ssrInterpolate(service.originalPrice)}</span><span class="text-green-600 font-[500] inter-semibold" data-v-2d535f29>\u20B9${ssrInterpolate(service.finalPrice)}</span>`);
            if (service.promotionApplied.type) {
              _push(`<span class="ml-2 text-xs font-semibold px-2 py-0.5 rounded-full" style="${ssrRenderStyle({ "background": "linear-gradient(90deg, #e0c3fc 0%, #8ec5fc 100%)", "color": "#5a189a" })}" data-v-2d535f29>${ssrInterpolate(formatPromoType(service.promotionApplied.type))}</span>`);
            } else {
              _push(`<!---->`);
            }
            _push(`<!--]-->`);
          } else {
            _push(`<!--[--> \u20B9${ssrInterpolate(service.price)}<!--]-->`);
          }
          _push(`</div></div><button class="${ssrRenderClass([isServiceSelected(service._id) ? "bg-purple-600 text-white hover:bg-purple-700" : "bg-white hover:bg-gray-100 border border-gray-300 text-gray-700", "px-10 py-1 rounded-full font-semibold text-[600] text-[16px] transition mt-4 sm:mt-0"])}" data-v-2d535f29>${ssrInterpolate(isServiceSelected(service._id) ? "Added" : "Book")}</button></div>`);
        });
        _push(`<!--]-->`);
      }
      if (hasMoreServices.value) {
        _push(`<div class="flex justify-center mt-6" data-v-2d535f29><button class="bg-white hover:bg-purple-600 text-black hover:text-white font-semibold py-2 px-6 rounded-full transition border border-gray-300 flex items-center gap-2 cursor-pointer"${ssrIncludeBooleanAttr(isLoadingServices.value) ? " disabled" : ""} data-v-2d535f29>`);
        if (isLoadingServices.value) {
          _push(`<span data-v-2d535f29>Loading...</span>`);
        } else {
          _push(`<!--[--><span data-v-2d535f29>Load More</span><svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-2d535f29><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" data-v-2d535f29></path></svg><!--]-->`);
        }
        _push(`</button></div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div><div class="mt-10 max-w-3xl" data-v-2d535f29><h2 class="text-[24px] text-[700] font-bold mb-3" data-v-2d535f29>About</h2><p class="text-[16px] text-[400] mb-6 leading-relaxed" data-v-2d535f29>${ssrInterpolate(salonData.value.about || "Experience professional services in a cozy, comfortable home setting.")}</p><span class="font-semibold text-[18px] text-[700] mb-2" data-v-2d535f29>Amenities</span><ul class="mb-6 space-y-2 mt-3" data-v-2d535f29><!--[-->`);
      ssrRenderList(salonData.value.amenities, (amenity, idx) => {
        _push(`<li class="flex items-center gap-2 text-[16px] text-[400]" data-v-2d535f29><svg class="w-4 h-4 text-gray-400" viewBox="0 0 24 24" fill="currentColor" data-v-2d535f29><rect x="6" y="6" width="12" height="12" rx="2" transform="rotate(45 12 12)" data-v-2d535f29></rect></svg> ${ssrInterpolate(amenity.name)}</li>`);
      });
      _push(`<!--]--></ul><div class="rounded-lg overflow-hidden mt-4 relative" style="${ssrRenderStyle({ "max-width": "100%", "height": "260px" })}" data-v-2d535f29><div id="custom-map" style="${ssrRenderStyle({ "width": "100%", "height": "100%" })}" data-v-2d535f29></div><button class="absolute bottom-4 right-4 bg-white px-4 py-1 rounded shadow text-sm font-medium" data-v-2d535f29> Open Map </button></div><div class="mt-10" data-v-2d535f29><h2 class="text-2xl font-bold mb-6" data-v-2d535f29>Our Expertise</h2><div class="flex flex-wrap gap-10 justify-start" data-v-2d535f29><!--[-->`);
      ssrRenderList(displayedExperts.value, (expert) => {
        _push(`<div class="flex flex-col items-center w-1/2 sm:w-35" data-v-2d535f29><img${ssrRenderAttr("src", expert.img)}${ssrRenderAttr("alt", expert.name)} class="w-24 h-24 rounded-full object-cover mb-3" data-v-2d535f29><div class="text-[16px] text-[600] font-semibold text-center" data-v-2d535f29>${ssrInterpolate(expert.name)}</div><div class="text-gray-500 text-center text-[14px] text-[400] mb-2" data-v-2d535f29>${ssrInterpolate(expert.role)}</div><div class="flex items-center justify-center gap-1" data-v-2d535f29><svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 24 24" data-v-2d535f29><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" data-v-2d535f29></path></svg><span class="text-[14px] text-[400]" data-v-2d535f29>${ssrInterpolate(expert.rating)}(${ssrInterpolate(expert.reviews)})</span></div></div>`);
      });
      _push(`<!--]--></div>`);
      if (salonData.value.experts.length > 4) {
        _push(`<div class="flex justify-center mt-6" data-v-2d535f29><button class="bg-white hover:bg-purple-600 text-black hover:text-white font-semibold py-2 px-6 rounded-full transition border border-gray-300 flex items-center gap-2 cursor-pointer" data-v-2d535f29><span data-v-2d535f29>${ssrInterpolate(showAllExperts.value ? "Show Less" : "Show More")}</span><svg class="${ssrRenderClass([{ "rotate-180": showAllExperts.value }, "w-5 h-5 transform transition-transform duration-200"])}" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-2d535f29><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" data-v-2d535f29></path></svg></button></div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div><div class="mt-12" data-v-2d535f29><h2 class="text-[24px] text-[700] font-bold mb-6" data-v-2d535f29>Reviews</h2><div class="flex flex-wrap gap-3 mb-6" data-v-2d535f29><!--[-->`);
      ssrRenderList(reviewFilters, (filter, idx) => {
        _push(`<button class="${ssrRenderClass([
          "px-5 py-1 rounded-full border font-medium text-[12px] transition",
          selectedReviewFilter.value === idx ? "text-white border-black" : "text-black border-gray-400 bg-white hover:bg-gray-100"
        ])}" style="${ssrRenderStyle(selectedReviewFilter.value === idx ? "background:rgba(58,58,58,1);" : "")}" data-v-2d535f29>${ssrInterpolate(filter)}</button>`);
      });
      _push(`<!--]--></div><div class="grid grid-cols-1 md:grid-cols-2 gap-8" data-v-2d535f29><!--[-->`);
      ssrRenderList(filteredReviews.value, (review) => {
        _push(`<div class="bg-white rounded-xl" data-v-2d535f29><div class="flex items-center gap-4 mb-2" data-v-2d535f29><img${ssrRenderAttr("src", review.avatar)} alt="" class="w-12 h-12 rounded-full object-cover" data-v-2d535f29><div data-v-2d535f29><div class="font-semibold text-[16px]" data-v-2d535f29>${ssrInterpolate(review.name)}</div><div class="flex items-center gap-1 mt-1" data-v-2d535f29><!--[-->`);
        ssrRenderList(5, (n) => {
          _push(`<span data-v-2d535f29>`);
          if (n <= Math.floor(review.rating)) {
            _push(`<svg class="w-4 h-4 text-yellow-400 inline" fill="currentColor" viewBox="0 0 24 24" data-v-2d535f29><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" data-v-2d535f29></path></svg>`);
          } else {
            _push(`<svg class="w-4 h-4 text-gray-300 inline" fill="currentColor" viewBox="0 0 24 24" data-v-2d535f29><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" data-v-2d535f29></path></svg>`);
          }
          _push(`</span>`);
        });
        _push(`<!--]--><span class="ml-auto text-gray-400 text-sm" data-v-2d535f29>${ssrInterpolate(review.date)}</span></div></div></div><div class="text-[16px] text-[400] mb-3 leading-relaxed" data-v-2d535f29>${ssrInterpolate(review.text)}</div><div class="flex flex-wrap gap-3 text-[500] text-[14px]" data-v-2d535f29><!--[-->`);
        ssrRenderList(review.scores, (score, idx) => {
          _push(`<span class="italic" style="${ssrRenderStyle({ "color": "rgba(25, 29, 35, 0.8)" })}" data-v-2d535f29>\u2022 ${ssrInterpolate(score)}</span>`);
        });
        _push(`<!--]--></div></div>`);
      });
      _push(`<!--]--></div></div></div></div><aside class="w-full lg:max-w-sm lg:flex-shrink-0" data-v-2d535f29><div class="bg-white rounded-2xl shadow-lg p-6" data-v-2d535f29><div class="flex items-center gap-4 mb-2" data-v-2d535f29><span class="text-[18px] text-[700] font-semibold" style="${ssrRenderStyle({ "color": "rgba(62, 192, 95, 1)" })}" data-v-2d535f29>Open Now</span><span class="text-black-500 text-[16px] text-[500] font-medium" data-v-2d535f29>until ${ssrInterpolate(salonData.value.closingTime || "10:00 pm")}</span></div><ul class="mb-6" data-v-2d535f29><!--[-->`);
      ssrRenderList(salonData.value.timings, (timing, day) => {
        _push(`<li class="flex flex-col sm:flex-row sm:items-center sm:justify-start py-3" data-v-2d535f29><span class="flex items-center gap-2 sm:min-w-[120px]" data-v-2d535f29><svg xmlns="http://www.w3.org/2000/svg" class="w-2 h-2" viewBox="0 0 24 24" style="${ssrRenderStyle({ fill: "rgba(62, 192, 95, 1)" })}" data-v-2d535f29><circle cx="12" cy="12" r="8" data-v-2d535f29></circle></svg><span class="text-[16px] font-semibold" data-v-2d535f29>${ssrInterpolate(day)}</span></span><span class="flex items-center gap-1 text-gray-500 text-[16px] mt-1 sm:mt-0 sm:ml-2" data-v-2d535f29><svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 inline" viewBox="0 0 24 24" fill="#6B7280" data-v-2d535f29><path fill="#6B7280" d="M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2M12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8s8 3.58 8 8s-3.58 8-8 8" data-v-2d535f29></path><path fill="#6B7280" d="M12.5 7H11v6l5.25 3.15l.75-1.23l-4.5-2.67z" data-v-2d535f29></path></svg> ${ssrInterpolate(timing || "Not Available")}</span></li>`);
      });
      _push(`<!--]--></ul><button class="w-full bg-purple-600 hover:bg-purple-700 text-white font-semibold py-3 rounded-full mb-4 transition" data-v-2d535f29>Book Service Now</button></div></aside></div></section>`);
      if (selectedServices.value.length > 0) {
        _push(`<div class="fixed bottom-0 left-0 right-0 bg-white shadow-lg border-t border-gray-200 py-3 px-0 sm:px-6 md:px-0 lg:px-20 flex items-center justify-between z-50" data-v-2d535f29><div class="flex items-center" data-v-2d535f29><span class="text-[600] text-[18px]" data-v-2d535f29>${ssrInterpolate(selectedServices.value.length)} service${ssrInterpolate(selectedServices.value.length !== 1 ? "s" : "")} added </span></div><button class="bg-[#7926e1] hover:bg-[#6a1fd1] text-white font-medium py-2 px-6 rounded-full transition" data-v-2d535f29> Book Service Now </button></div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div>`);
    };
  }
});
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/salons/[id].vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const _id_ = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-2d535f29"]]);

export { _id_ as default };
//# sourceMappingURL=_id_-KlRR9Rze.mjs.map
