import { defineComponent, ref, mergeProps, unref, useSSRContext } from 'vue';
import { ssrRenderAttrs, ssrRenderList, ssrRenderClass, ssrRenderStyle, ssrInterpolate, ssrRenderAttr, ssrIncludeBooleanAttr } from 'vue/server-renderer';
import { useRouter } from 'vue-router';
import { u as useBookingStore } from './booking-D2Du41fq.mjs';
import { g as useHead } from './server.mjs';
import 'pinia';
import '../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:url';
import '@iconify/utils';
import 'node:crypto';
import 'consola';
import 'node:path';
import 'better-sqlite3';
import 'ipx';
import 'cookie';
import '@iconify/vue';
import 'tailwindcss/colors';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import 'unhead/server';
import 'devalue';
import 'unhead/plugins';
import 'unhead/utils';

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "index",
  __ssrInlineRender: true,
  setup(__props) {
    useRouter();
    const bookingStore = useBookingStore();
    const selectedMembership = ref(null);
    const membershipOptions = [
      {
        id: 1,
        name: "Glow & Shine Salon",
        type: "Premium Glow",
        useCount: 10,
        expiry: "1 Month",
        price: 3e3,
        gradient: "linear-gradient(102.71deg, #E73A27 -9.07%, #812116 128.46%)",
        salon: {
          id: 1,
          name: "Glow & Shine Salon",
          location: "MG Road, Mumbai",
          image: "/images/saloon-small-image.png"
        }
      },
      {
        id: 2,
        name: "Glow & Shine Salon1",
        type: "Royal Purple",
        useCount: 15,
        expiry: "2 Months",
        price: 4500,
        gradient: "linear-gradient(105.72deg, #B427E7 12.31%, #260C47 172.66%)",
        salon: {
          id: 1,
          name: "Glow & Shine Salon",
          location: "MG Road, Mumbai",
          image: "/images/saloon-small-image.png"
        }
      },
      {
        id: 3,
        name: "Glow & Shine Salon",
        type: "Elite Blue",
        useCount: 20,
        expiry: "3 Months",
        price: 6e3,
        gradient: "linear-gradient(173.74deg, #61AAFB -4.78%, #6A4EDC 50.79%, #7B40D9 100.15%)",
        salon: {
          id: 1,
          name: "Glow & Shine Salon",
          location: "MG Road, Mumbai",
          image: "/images/saloon-small-image.png"
        }
      },
      {
        id: 4,
        name: "Glow & Shine Salon",
        type: "Oceanic Cyan",
        useCount: 25,
        expiry: "6 Months",
        price: 8e3,
        gradient: "linear-gradient(105.72deg, #27C4E7 12.31%, #03535D 172.66%)",
        salon: {
          id: 1,
          name: "Glow & Shine Salon",
          location: "MG Road, Mumbai",
          image: "/images/saloon-small-image.png"
        }
      }
    ];
    useHead({
      title: "Buy Membership | Glow & Shine Salon",
      meta: [
        {
          name: "description",
          content: "Purchase premium salon membership at Glow & Shine Salon. Get exclusive access to premium services with our membership plans."
        },
        {
          name: "keywords",
          content: "salon membership, premium membership, beauty salon, Glow & Shine Salon, Mumbai salon"
        }
      ]
    });
    return (_ctx, _push, _parent, _attrs) => {
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "min-h-screen bg-white" }, _attrs))}><div class="flex flex-col xl:flex-row max-w-8xl mx-auto px-1 sm:px-12 xl:justify-center xl:min-h-screen"><div class="flex-1 lg:flex-[0.4] px-6 lg:px-8 py-6 lg:py-8"><div class="flex items-center space-x-3 mb-3"><button class="w-10 h-10 rounded-full border border-gray-300 flex items-center justify-center group cursor-pointer"><svg xmlns="http://www.w3.org/2000/svg" class="w-7 h-7 text-purple-600 group-hover:text-purple-700 transition-colors" viewBox="0 0 24 24" fill="currentColor"><path d="m6.523 12.5l3.735 3.735q.146.146.153.344q.006.198-.153.363q-.166.166-.357.168t-.357-.162l-4.382-4.383q-.243-.242-.243-.565t.243-.566l4.382-4.382q.147-.146.347-.153q.201-.007.367.159q.16.165.162.353q.003.189-.162.354L6.523 11.5h12.38q.214 0 .358.143t.143.357t-.143.357t-.357.143z"></path></svg></button><h1 class="text-[25px] font-semibold text-[700]">Buy Membership</h1></div><div class="border border-gray-200 rounded-2xl p-6"><h2 class="text-[20px] font-semibold text-[700] mb-6"> Available Membership options </h2><div class="space-y-4"><!--[-->`);
      ssrRenderList(membershipOptions, (option, index) => {
        var _a;
        _push(`<div class="${ssrRenderClass([((_a = selectedMembership.value) == null ? void 0 : _a.id) === option.id ? "ring-2 ring-purple-500 ring-offset-2" : "", "p-5 rounded-2xl cursor-pointer transition-all duration-200 hover:shadow-md"])}" style="${ssrRenderStyle({ background: option.gradient })}"><div class="text-white flex flex-col items-center sm:items-start text-center sm:text-left"><h3 class="text-[20px] font-semibold mb-2">${ssrInterpolate(option.name)}</h3><p class="text-[16px] mb-1 opacity-90">${ssrInterpolate(option.type)}</p><p class="text-[16px] mb-1 opacity-90"> Total Use count: ${ssrInterpolate(option.useCount)}</p><div class="flex flex-col sm:flex-row sm:justify-between sm:items-center w-full text-[16px] opacity-90 mt-2"><span>Expiry: ${ssrInterpolate(option.expiry)}</span><span class="text-[20px] font-semibold text-white sm:ml-auto">\u20B9${ssrInterpolate(option.price)}</span></div></div></div>`);
      });
      _push(`<!--]--></div></div></div><div class="w-full lg:flex-[0.4] xl:w-[400px] bg-white p-6 lg:p-8 xl:p-0 mt-0 xl:mt-20"><div class="bg-white rounded-2xl shadow-sm border border-gray-100 p-6 space-y-6">`);
      if (selectedMembership.value) {
        _push(`<div><div class="flex items-center gap-3 p-3 rounded-lg bg-white border border-gray-100"><div class="w-12 h-12 rounded-lg bg-gray-200 flex items-center justify-center overflow-hidden flex-shrink-0"><img${ssrRenderAttr("src", selectedMembership.value.salon.image)} alt="salon" class="w-full h-full object-cover"></div><div class="flex-1 min-w-0"><h3 class="text-[18px] font-semibold text-[700] truncate">${ssrInterpolate(selectedMembership.value.salon.name)}</h3><div class="flex items-center gap-1 text-[14px] text-[400] text-gray-500"><svg class="w-3 h-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path></svg><span class="truncate">${ssrInterpolate(selectedMembership.value.salon.location)}</span></div></div></div></div>`);
      } else {
        _push(`<div class="text-center py-8"><div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4"><svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path></svg></div><p class="text-gray-500">Select a membership to continue</p></div>`);
      }
      if (selectedMembership.value) {
        _push(`<hr class="border-gray-200">`);
      } else {
        _push(`<!---->`);
      }
      if (selectedMembership.value) {
        _push(`<div class="flex justify-between items-center"><span class="text-[18px] font-semibold text-gray-900">Total</span><span class="text-[18px] font-semibold text-gray-900"> \u20B9${ssrInterpolate(unref(bookingStore).total)}</span></div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`<button${ssrIncludeBooleanAttr(!unref(bookingStore).isReadyForNextStep) ? " disabled" : ""} class="${ssrRenderClass([unref(bookingStore).isReadyForNextStep ? "bg-purple-600 hover:bg-purple-700" : "bg-gray-300 cursor-not-allowed", "w-full py-3 rounded-full text-white font-semibold text-[16px] transition-colors cursor-pointer"])}"> Continue </button></div></div></div></div>`);
    };
  }
});
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/membership/index.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};

export { _sfc_main as default };
//# sourceMappingURL=index-ASd3MSXJ.mjs.map
