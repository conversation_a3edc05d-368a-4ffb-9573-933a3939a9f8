import { _ as _export_sfc, o as __nuxt_component_0, b as useUserStore, f as useRoute, i as useCookie, c as useRuntimeConfig, a as __nuxt_component_0$2 } from './server.mjs';
import { defineComponent, mergeProps, ref, computed, watch, withCtx, createVNode, unref, createTextVNode, useSSRContext } from 'vue';
import { ssrRenderAttrs, ssrRenderComponent, ssrRenderAttr, ssrInterpolate, ssrRenderStyle, ssrRenderList, ssrIncludeBooleanAttr } from 'vue/server-renderer';
import { p as publicAssetsURL } from '../_/nitro.mjs';
import { _ as _imports_2$1 } from './virtual_public-Cy4CASs_.mjs';
import { u as useSearchStore } from './search-CN1Jpzjq.mjs';
import 'pinia';
import 'vue-router';
import 'cookie';
import '@iconify/vue';
import 'tailwindcss/colors';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import 'unhead/server';
import 'devalue';
import 'unhead/plugins';
import 'unhead/utils';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:url';
import '@iconify/utils';
import 'node:crypto';
import 'consola';
import 'node:path';
import 'better-sqlite3';
import 'ipx';

const _imports_0 = publicAssetsURL("/logo/trimlie.svg");
const _imports_1$1 = publicAssetsURL("/icon/notification-bell.svg");
const _sfc_main$2 = /* @__PURE__ */ defineComponent({
  __name: "Header",
  __ssrInlineRender: true,
  setup(__props) {
    const userStore = useUserStore();
    const searchStore = useSearchStore();
    const route = useRoute();
    const isLoading = ref(true);
    const menuOpen = ref(false);
    const showProfileModal = ref(false);
    const showLocationModal = ref(false);
    ref(null);
    const searchQuery = ref("");
    ref(null);
    const selectedAddress = ref("");
    const selectedLatLng = ref(null);
    const showNotificationModal = ref(false);
    ref(null);
    const location = ref({
      lat: 0,
      lng: 0,
      city: ""
    });
    const autocompleteError = ref("");
    ref(false);
    ref(null);
    ref(null);
    ref(false);
    const displayedNotifications = computed(() => {
      return notifications.value.slice(0, 3);
    });
    const notifications = ref([
      {
        id: 1,
        title: "Appointment Confirmed",
        message: "Your appointment with Elite Beauty Saloon is confirmed for 20th March 2024 at 4:30 pm See you soon!",
        time: "23 min",
        isRead: false
      },
      {
        id: 2,
        title: "Appointment Confirmed",
        message: "Your appointment with Elite Beauty Saloon is confirmed for 20th March 2024 at 4:30 pm See you soon!",
        time: "23 min",
        isRead: false
      },
      {
        id: 3,
        title: "Appointment Confirmed",
        message: "Your appointment with Elite Beauty Saloon is confirmed for 20th March 2024 at 4:30 pm See you soon!",
        time: "23 min",
        isRead: false
      },
      {
        id: 4,
        title: "Appointment Confirmed",
        message: "Your appointment with Elite Beauty Saloon is confirmed for 20th March 2024 at 4:30 pm See you soon!",
        time: "23 min",
        isRead: false
      }
    ]);
    useCookie("user_location", {
      maxAge: 30 * 24 * 60 * 60,
      // 30 days
      sameSite: "strict"
    });
    const selectedLocation = computed(() => !!selectedAddress.value && !!selectedLatLng.value);
    const canCloseModal = computed(() => true);
    searchQuery.value = route.params.query ? decodeURIComponent(route.params.query) : "";
    watch(
      () => route.params.query,
      (newQuery) => {
        searchQuery.value = newQuery ? decodeURIComponent(newQuery) : "";
      }
    );
    const { public: { BASE_URL } } = useRuntimeConfig();
    const profileImageUrl = ref("");
    const fetchHeaderProfile = async () => {
      if (!userStore.loggedIn || !userStore.token) return;
      try {
        const guestId = useCookie("guestId").value;
        const body = new URLSearchParams();
        body.append("guestId", guestId || "");
        const response = await fetch(`${BASE_URL}/v1/client/auth/profile`, {
          method: "POST",
          headers: {
            "Content-Type": "application/x-www-form-urlencoded",
            token: `${userStore.token}`
          },
          body
        });
        const data = await response.json();
        if (data.status && data.status_code === 200 && data.data) {
          const c = data.data.clientData;
          profileImageUrl.value = c.profileImage ? `${BASE_URL}/wp/${c.profileImage}` : "";
        }
      } catch (error) {
        profileImageUrl.value = "";
      }
    };
    watch(
      () => userStore.loggedIn,
      async (loggedIn) => {
        if (loggedIn) await fetchHeaderProfile();
        else profileImageUrl.value = "";
      }
    );
    return (_ctx, _push, _parent, _attrs) => {
      const _component_NuxtLink = __nuxt_component_0$2;
      if (!isLoading.value) {
        _push(`<header${ssrRenderAttrs(mergeProps({
          class: "bg-white border-b border-gray-200 shadow-sm relative sticky top-0 z-50",
          style: { "min-height": "64px" }
        }, _attrs))} data-v-c340e611><div class="flex items-center justify-between px-6 sm:px-8 md:px-16 lg:px-20 xl:px-20 2xl:px-20 py-3 sm:py-2 min-h-[64px]" data-v-c340e611><button class="sm:hidden" data-v-c340e611><svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24" data-v-c340e611><path stroke-linecap="round" stroke-linejoin="round" d="M4 6h16M4 12h16M4 18h16" data-v-c340e611></path></svg></button><div class="absolute left-1/2 -translate-x-1/2 sm:static sm:translate-x-0" data-v-c340e611>`);
        _push(ssrRenderComponent(_component_NuxtLink, { to: "/" }, {
          default: withCtx((_, _push2, _parent2, _scopeId) => {
            if (_push2) {
              _push2(`<img${ssrRenderAttr("src", _imports_0)} alt="headerlogo" class="h-13 w-auto" data-v-c340e611${_scopeId}>`);
            } else {
              return [
                createVNode("img", {
                  src: _imports_0,
                  alt: "headerlogo",
                  class: "h-13 w-auto"
                })
              ];
            }
          }),
          _: 1
        }, _parent));
        _push(`</div><div class="w-6 sm:hidden" data-v-c340e611></div><div class="hidden sm:flex items-center justify-between w-full" data-v-c340e611><div class="flex-grow max-w-xs mx-6" data-v-c340e611><div class="flex items-center px-4 py-2 border border-gray-300 rounded-full h-10" data-v-c340e611><svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" data-v-c340e611><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-4.35-4.35m0 0A7.5 7.5 0 1110.5 3a7.5 7.5 0 016.15 13.65z" data-v-c340e611></path></svg><input type="text" placeholder="Salons" class="ml-2 w-full focus:outline-none text-sm"${ssrRenderAttr("value", unref(searchStore).searchQuery)} data-v-c340e611></div></div>`);
        if (unref(userStore).loggedIn) {
          _push(`<div class="flex items-center gap-4" data-v-c340e611><div class="flex items-center bg-purple-50 text-purple-600 rounded-full px-3 py-1 space-x-1 text-sm h-10 cursor-pointer" data-v-c340e611><svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" viewBox="0 0 24 24" fill="currentColor" data-v-c340e611><path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7m0 9.5a2.5 2.5 0 0 1 0-5a2.5 2.5 0 0 1 0 5" data-v-c340e611></path></svg><span class="text-black text-base font-normal" data-v-c340e611>${ssrInterpolate(location.value.city || "Fetching...")}</span><svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" viewBox="0 0 24 24" data-v-c340e611><path d="m12 15.4l-6-6L7.4 8l4.6 4.6L16.6 8L18 9.4z" data-v-c340e611></path></svg></div><div class="relative" data-v-c340e611><button class="relative cursor-pointer" data-v-c340e611><img${ssrRenderAttr("src", _imports_1$1)} alt="Notification" class="w-5 h-5 text-purple-600" data-v-c340e611>`);
          if (notifications.value.filter((n) => !n.isRead).length > 0) {
            _push(`<span class="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full flex items-center justify-center" data-v-c340e611><span class="text-white text-xs font-bold" data-v-c340e611>${ssrInterpolate(notifications.value.filter((n) => !n.isRead).length)}</span></span>`);
          } else {
            _push(`<!---->`);
          }
          _push(`</button>`);
          if (showNotificationModal.value) {
            _push(`<div class="absolute top-full mt-2 bg-white rounded-2xl shadow-xl sm:w-120 md:w-100 lg:w-140 z-50" style="${ssrRenderStyle({ "box-shadow": "0 8px 32px rgba(80, 36, 143, 0.12)", "right": "0" })}" data-v-c340e611><div class="px-6 py-4 border-b border-gray-100" data-v-c340e611><h3 class="text-lg font-semibold text-gray-900" data-v-c340e611>Notifications</h3></div><div class="md:max-h-85 xl:max-h-100 overflow-y-auto scrollbar-hide" data-v-c340e611><!--[-->`);
            ssrRenderList(displayedNotifications.value, (notification) => {
              _push(`<div class="px-6 py-4 border-b border-gray-50 hover:bg-gray-50 cursor-pointer" data-v-c340e611><div class="flex items-start gap-3" data-v-c340e611><div class="flex-shrink-0 w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center" data-v-c340e611><svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24" data-v-c340e611><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7" data-v-c340e611></path></svg></div><div class="flex-1 min-w-0" data-v-c340e611><div class="flex items-start justify-between mb-1" data-v-c340e611><h4 class="text-sm font-semibold text-gray-900" data-v-c340e611>${ssrInterpolate(notification.title)}</h4><span class="text-xs text-gray-500 ml-2 flex-shrink-0" data-v-c340e611>${ssrInterpolate(notification.time)}</span></div><p class="text-sm text-gray-600 leading-relaxed mb-3" data-v-c340e611>${ssrInterpolate(notification.message)}</p><button class="text-purple-600 text-sm font-medium hover:text-purple-700 bg-transparent border-none p-0" data-v-c340e611> View </button></div>`);
              if (!notification.isRead) {
                _push(`<div class="flex-shrink-0 w-2 h-2 bg-purple-600 rounded-full mt-1" data-v-c340e611></div>`);
              } else {
                _push(`<!---->`);
              }
              _push(`</div></div>`);
            });
            _push(`<!--]--></div><div class="px-6 py-4 border-t border-gray-100 bg-gray-50" data-v-c340e611>`);
            if (notifications.value.length > 3) {
              _push(`<button class="w-full text-center text-purple-600 text-sm font-medium hover:text-purple-700 py-2" data-v-c340e611> View All Notifications (${ssrInterpolate(notifications.value.length - 3)} more) </button>`);
            } else {
              _push(`<button class="w-full text-center text-gray-400 text-sm font-medium py-2 cursor-not-allowed" data-v-c340e611> All notifications viewed </button>`);
            }
            _push(`</div></div>`);
          } else {
            _push(`<!---->`);
          }
          _push(`</div><div class="flex items-center gap-2 cursor-pointer relative" data-v-c340e611><div class="w-8 h-8 rounded-full bg-purple-100 flex items-center justify-center overflow-hidden" data-v-c340e611>`);
          if (profileImageUrl.value) {
            _push(`<img${ssrRenderAttr("src", profileImageUrl.value)}${ssrRenderAttr("alt", unref(userStore).displayName)} class="w-full h-full object-cover" data-v-c340e611>`);
          } else {
            _push(`<svg class="w-5 h-5 text-purple-600" fill="currentColor" viewBox="0 0 24 24" data-v-c340e611><path d="M12 12c2.7 0 5-2.3 5-5s-2.3-5-5-5-5 2.3-5 5 2.3 5 5 5Zm0 2c-3.3 0-10 1.7-10 5v3h20v-3c0-3.3-6.7-5-10-5Z" data-v-c340e611></path></svg>`);
          }
          _push(`</div><div class="text-sm leading-tight" data-v-c340e611><div class="font-semibold" data-v-c340e611>${ssrInterpolate(unref(userStore).displayName)}</div><div class="text-gray-500 text-xs" data-v-c340e611>${ssrInterpolate(unref(userStore).email)}</div></div>`);
          if (!showProfileModal.value) {
            _push(`<svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" data-v-c340e611><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 9l6 6 6-6" data-v-c340e611></path></svg>`);
          } else {
            _push(`<svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" data-v-c340e611><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 15l-6-6-6 6" data-v-c340e611></path></svg>`);
          }
          if (showProfileModal.value) {
            _push(`<div class="absolute top-full mt-2 bg-white rounded-2xl shadow-xl w-80 p-6 z-50" style="${ssrRenderStyle({ "box-shadow": "0 8px 32px rgba(80, 36, 143, 0.12)", "right": "0", "margin-right": "16px" })}" data-v-c340e611><div class="flex flex-col items-center mb-6 p-5 rounded-2xl" style="${ssrRenderStyle({ "background-color": "#7B27E70D" })}" data-v-c340e611><div class="w-16 h-16 rounded-full bg-purple-100 mb-2 flex items-center justify-center overflow-hidden" data-v-c340e611>`);
            if (profileImageUrl.value) {
              _push(`<img${ssrRenderAttr("src", profileImageUrl.value)}${ssrRenderAttr("alt", unref(userStore).displayName)} class="w-full h-full object-cover" data-v-c340e611>`);
            } else {
              _push(`<svg class="w-8 h-8 text-purple-600" fill="currentColor" viewBox="0 0 24 24" data-v-c340e611><path d="M12 12c2.7 0 5-2.3 5-5s-2.3-5-5-5-5 2.3-5 5 2.3 5 5 5Zm0 2c-3.3 0-10 1.7-10 5v3h20v-3c0-3.3-6.7-5-10-5Z" data-v-c340e611></path></svg>`);
            }
            _push(`</div><div class="font-semibold text-lg" data-v-c340e611>${ssrInterpolate(unref(userStore).displayName)}</div><div class="text-gray-500 text-sm" data-v-c340e611>${ssrInterpolate(unref(userStore).email)}</div></div><div class="flex flex-col gap-4" data-v-c340e611><button class="flex items-center gap-3 text-gray-900 hover:text-purple-600" data-v-c340e611><span data-v-c340e611><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" data-v-c340e611><path fill="currentColor" d="M12 6c1.1 0 2 .9 2 2s-.9 2-2 2s-2-.9-2-2s.9-2 2-2m0 10c2.7 0 5.8 1.29 6 2H6c.23-.72 3.31-2 6-2m0-12C9.79 4 8 5.79 8 8s1.79 4 4 4s4-1.79 4-4s-1.79-4-4-4m0 10c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4" data-v-c340e611></path></svg></span> Profile </button><button class="flex items-center gap-3 text-gray-900 hover:text-purple-600" data-v-c340e611><span data-v-c340e611><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" data-v-c340e611><path fill="currentColor" d="M15.55 13c.75 0 1.41-.41 1.75-1.03l3.58-6.49A.996.996 0 0 0 20.01 4H5.21l-.94-2H1v2h2l3.6 7.59l-1.35 2.44C4.52 15.37 5.48 17 7 17h12v-2H7l1.1-2zM6.16 6h12.15l-2.76 5H8.53zM7 18c-1.1 0-1.99.9-1.99 2S5.9 22 7 22s2-.9 2-2s-.9-2-2-2m10 0c-1.1 0-1.99.9-1.99 2s.89 2 1.99 2s2-.9 2-2s-.9-2-2-2" data-v-c340e611></path></svg></span> Cart </button><button class="flex items-center gap-3 text-gray-900 hover:text-purple-600" data-v-c340e611><span data-v-c340e611><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" data-v-c340e611><path fill="currentColor" d="M16.5 3c-1.74 0-3.41.81-4.5 2.09C10.91 3.81 9.24 3 7.5 3C4.42 3 2 5.42 2 8.5c0 3.78 3.4 6.86 8.55 11.54L12 21.35l1.45-1.32C18.6 15.36 22 12.28 22 8.5C22 5.42 19.58 3 16.5 3m-4.4 15.55l-.1.1l-.1-.1C7.14 14.24 4 11.39 4 8.5C4 6.5 5.5 5 7.5 5c1.54 0 3.04.99 3.57 2.36h1.87C13.46 5.99 14.96 5 16.5 5c2 0 3.5 1.5 3.5 3.5c0 2.89-3.14 5.74-7.9 10.05" data-v-c340e611></path></svg></span> Wishlist </button><button class="flex items-center gap-3 text-gray-900 hover:text-purple-600" data-v-c340e611><span data-v-c340e611><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" data-v-c340e611><path fill="currentColor" d="M14.8 8L16 9.2L9.2 16L8 14.8zM4 4h16c1.11 0 2 .89 2 2v4a2 2 0 1 0 0 4v4c0 1.11-.89 2-2 2H4a2 2 0 0 1-2-2v-4c1.11 0 2-.89 2-2a2 2 0 0 0-2-2V6a2 2 0 0 1 2-2m0 2v2.54a3.994 3.994 0 0 1 0 6.92V18h16v-2.54a3.994 3.994 0 0 1 0-6.92V6zm5.5 2c.83 0 1.5.67 1.5 1.5S10.33 11 9.5 11S8 10.33 8 9.5S8.67 8 9.5 8m5 5c.83 0 1.5.67 1.5 1.5s-.67 1.5-1.5 1.5s-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5" data-v-c340e611></path></svg></span> Gift cards </button><button class="flex items-center gap-3 text-gray-900 hover:text-purple-600" data-v-c340e611><span data-v-c340e611><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" data-v-c340e611><path fill="currentColor" d="M21 3H3c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h18c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2m0 16H3V5h18zM5 10h9v2H5zm0-3h9v2H5z" data-v-c340e611></path></svg></span> Orders </button><button class="flex items-center gap-3 text-red-500 hover:text-red-700 mt-2" data-v-c340e611><span data-v-c340e611><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" data-v-c340e611><path fill="currentColor" d="m17 7l-1.41 1.41L18.17 11H8v2h10.17l-2.58 2.58L17 17l5-5zM4 5h8V3H4c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h8v-2H4z" data-v-c340e611></path></svg></span> Log out </button></div></div>`);
          } else {
            _push(`<!---->`);
          }
          _push(`</div></div>`);
        } else {
          _push(`<div class="flex items-center gap-4" data-v-c340e611><div class="flex items-center bg-purple-50 text-purple-600 rounded-full px-3 py-1 space-x-1 text-sm h-10 cursor-pointer" data-v-c340e611><svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" viewBox="0 0 24 24" fill="currentColor" data-v-c340e611><path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7m0 9.5a2.5 2.5 0 0 1 0-5a2.5 2.5 0 0 1 0 5" data-v-c340e611></path></svg><span class="text-black text-base font-normal" data-v-c340e611>${ssrInterpolate(location.value.city || "Fetching...")}</span><svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" viewBox="0 0 24 24" data-v-c340e611><path d="m12 15.4l-6-6L7.4 8l4.6 4.6L16.6 8L18 9.4z" data-v-c340e611></path></svg></div>`);
          _push(ssrRenderComponent(_component_NuxtLink, {
            to: "/auth/login",
            class: "text-[#7B27E7] text-base font-semibold flex items-center h-10"
          }, {
            default: withCtx((_, _push2, _parent2, _scopeId) => {
              if (_push2) {
                _push2(`Login `);
              } else {
                return [
                  createTextVNode("Login ")
                ];
              }
            }),
            _: 1
          }, _parent));
          _push(ssrRenderComponent(_component_NuxtLink, {
            to: "/",
            class: "bg-purple-600 text-white text-base font-normal px-5 py-2 rounded-full flex items-center h-10"
          }, {
            default: withCtx((_, _push2, _parent2, _scopeId) => {
              if (_push2) {
                _push2(` List your business`);
              } else {
                return [
                  createTextVNode(" List your business")
                ];
              }
            }),
            _: 1
          }, _parent));
          _push(`</div>`);
        }
        _push(`</div>`);
        if (showLocationModal.value) {
          _push(`<div class="fixed inset-0 bg-white z-50 p-6 sm:p-8 flex flex-col" data-v-c340e611><div class="flex justify-between items-center mb-6" data-v-c340e611><h2 class="text-xl sm:text-2xl font-semibold" data-v-c340e611>Select Location</h2><button${ssrIncludeBooleanAttr(!canCloseModal.value) ? " disabled" : ""} class="disabled:opacity-50" data-v-c340e611><svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24" data-v-c340e611><path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" data-v-c340e611></path></svg></button></div><div class="flex-grow flex flex-col items-center" data-v-c340e611><button class="flex items-center gap-2 text-purple-600 mb-4 cursor-pointer" data-v-c340e611><svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" viewBox="0 0 24 24" fill="currentColor" data-v-c340e611><path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7m0 9.5a2.5 2.5 0 0 1 0-5a2.5 2.5 0 0 1 0 5" data-v-c340e611></path></svg> Use Current Location </button><div class="w-full max-w-sm" data-v-c340e611><input type="text" placeholder="Enter city" class="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#7B27E7] bg-[#B0D4FC1F] text-sm sm:text-base" data-v-c340e611>`);
          if (autocompleteError.value) {
            _push(`<p class="text-red-500 text-sm mt-2" data-v-c340e611>${ssrInterpolate(autocompleteError.value)}</p>`);
          } else {
            _push(`<!---->`);
          }
          _push(`</div>`);
          if (selectedAddress.value) {
            _push(`<div class="my-2 p-3 rounded-md text-md text-[#000000]" data-v-c340e611>${ssrInterpolate(selectedAddress.value)}</div>`);
          } else {
            _push(`<!---->`);
          }
          if (selectedLatLng.value) {
            _push(`<div id="map" class="w-full max-w-sm h-64 rounded-md border border-gray-300 mt-4" data-v-c340e611></div>`);
          } else {
            _push(`<!---->`);
          }
          _push(`</div><div class="flex justify-center mt-6" data-v-c340e611><button class="bg-[#7B27E7] hover:bg-purple-700 text-white py-2 px-10 rounded-full"${ssrIncludeBooleanAttr(!selectedLocation.value) ? " disabled" : ""} data-v-c340e611> Save </button></div></div>`);
        } else {
          _push(`<!---->`);
        }
        _push(`</div>`);
        if (menuOpen.value) {
          _push(`<div class="fixed inset-0 bg-white z-50 p-6 sm:hidden" data-v-c340e611><div class="flex justify-between items-center mb-6" data-v-c340e611>`);
          _push(ssrRenderComponent(_component_NuxtLink, { to: "/" }, {
            default: withCtx((_, _push2, _parent2, _scopeId) => {
              if (_push2) {
                _push2(`<img${ssrRenderAttr("src", _imports_2$1)} alt="modal-logo" class="h-10 w-auto" data-v-c340e611${_scopeId}>`);
              } else {
                return [
                  createVNode("img", {
                    src: _imports_2$1,
                    alt: "modal-logo",
                    class: "h-10 w-auto"
                  })
                ];
              }
            }),
            _: 1
          }, _parent));
          _push(`<button data-v-c340e611><svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24" data-v-c340e611><path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" data-v-c340e611></path></svg></button></div>`);
          if (unref(userStore).loggedIn) {
            _push(`<div data-v-c340e611><div class="flex flex-col items-center mb-6 p-5 rounded-2xl" style="${ssrRenderStyle({ "background-color": "#7B27E70D" })}" data-v-c340e611><div class="w-16 h-16 rounded-full bg-purple-100 mb-2 flex items-center justify-center overflow-hidden" data-v-c340e611>`);
            if (profileImageUrl.value) {
              _push(`<img${ssrRenderAttr("src", profileImageUrl.value)}${ssrRenderAttr("alt", unref(userStore).displayName)} class="w-full h-full object-cover" data-v-c340e611>`);
            } else {
              _push(`<svg class="w-8 h-8 text-purple-600" fill="currentColor" viewBox="0 0 24 24" data-v-c340e611><path d="M12 12c2.7 0 5-2.3 5-5s-2.3-5-5-5-5 2.3-5 5 2.3 5 5 5Zm0 2c-3.3 0-10 1.7-10 5v3h20v-3c0-3.3-6.7-5-10-5" data-v-c340e611></path></svg>`);
            }
            _push(`</div><div class="font-semibold text-sm" data-v-c340e611>${ssrInterpolate(unref(userStore).displayName)}</div><div class="text-gray-500 text-sm" data-v-c340e611>${ssrInterpolate(unref(userStore).email)}</div></div><div class="flex flex-col gap-4" data-v-c340e611><button class="flex items-center gap-3 text-gray-900 hover:text-purple-600" data-v-c340e611><span data-v-c340e611><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" data-v-c340e611><path fill="currentColor" d="M12 6c1.1 0 2 .9 2 2s-.9 2-2 2s-2-.9-2-2s.9-2 2-2m0 10c2.7 0 5.8 1.29 6 2H6c.23-.72 3.31-2 6-2m0-12C9.79 4 8 5.79 8 8s1.79 4 4 4s4-1.79 4-4s-1.79-4-4-4m0 10c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4" data-v-c340e611></path></svg></span> Profile </button><button class="flex items-center gap-3 text-gray-900 hover:text-purple-600" data-v-c340e611><span data-v-c340e611><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" data-v-c340e611><path fill="currentColor" d="M15.55 13c.75 0 1.41-.41 1.75-1.03l3.58-6.49A.996.996 0 0 0 20.01 4H5.21l-.94-2H1v2h2l3.6 7.59l-1.35 2.44C4.52 15.37 5.48 17 7 17h12v-2H7l1.1-2zM6.16 6h12.15l-2.76 5H8.53zM7 18c-1.1 0-1.99.9-1.99 2S5.9 22 7 22s2-.9 2-2s-.9-2-2-2m10 0c-1.1 0-1.99.9-1.99 2s.89 2 1.99 2s2-.9 2-2s-.9-2-2-2" data-v-c340e611></path></svg></span> Cart </button><button class="flex items-center gap-3 text-gray-900 hover:text-purple-600" data-v-c340e611><span data-v-c340e611><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" data-v-c340e611><path fill="currentColor" d="M16.5 3c-1.74 0-3.41.81-4.5 2.09C10.91 3.81 9.24 3 7.5 3C4.42 3 2 5.42 2 8.5c0 3.78 3.4 6.86 8.55 11.54L12 21.35l1.45-1.32C18.6 15.36 22 12.28 22 8.5C22 5.42 19.58 3 16.5 3m-4.4 15.55l-.1.1l-.1-.1C7.14 14.24 4 11.39 4 8.5C4 6.5 5.5 5 7.5 5c1.54 0 3.04.99 3.57 2.36h1.87C13.46 5.99 14.96 5 16.5 5c2 0 3.5 1.5 3.5 3.5c0 2.89-3.14 5.74-7.9 10.05" data-v-c340e611></path></svg></span> Wishlist </button><button class="flex items-center gap-3 text-gray-900 hover:text-purple-600" data-v-c340e611><span data-v-c340e611><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" data-v-c340e611><path fill="currentColor" d="M14.8 8L16 9.2L9.2 16L8 14.8zM4 4h16c1.11 0 2 .89 2 2v4a2 2 0 1 0 0 4v4c0 1.11-.89 2-2 2H4a2 2 0 0 1-2-2v-4c1.11 0 2-.89 2-2a2 2 0 0 0-2-2V6a2 2 0 0 1 2-2m0 2v2.54a3.994 3.994 0 0 1 0 6.92V18h16v-2.54a3.994 3.994 0 0 1 0-6.92V6zm5.5 2c.83 0 1.5.67 1.5 1.5S10.33 11 9.5 11S8 10.33 8 9.5S8.67 8 9.5 8m5 5c.83 0 1.5.67 1.5 1.5s-.67 1.5-1.5 1.5s-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5" data-v-c340e611></path></svg></span> Gift cards </button><button class="flex items-center gap-3 text-gray-900 hover:text-purple-600" data-v-c340e611><span data-v-c340e611><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" data-v-c340e611><path fill="currentColor" d="M21 3H3c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h18c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2m0 16H3V5h18zM5 10h9v2H5zm0-3h9v2H5z" data-v-c340e611></path></svg></span> Orders </button><button class="flex items-center gap-3 text-red-500 hover:text-red-700 mt-2" data-v-c340e611><span data-v-c340e611><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" data-v-c340e611><path fill="currentColor" d="m17 7l-1.41 1.41L18.17 11H8v2h10.17l-2.58 2.58L17 17l5-5zM4 5h8V3H4c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h8v-2H4z" data-v-c340e611></path></svg></span> Log out </button></div></div>`);
          } else {
            _push(`<div data-v-c340e611><div class="flex flex-col gap-4" data-v-c340e611><div class="flex items-center space-x-1 text-purple-600" data-v-c340e611><svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" viewBox="0 0 24 24" fill="currentColor" data-v-c340e611><path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7m0 9.5a2.5 2.5 0 0 1 0-5a2.5 2.5 0 0 1 0 5" data-v-c340e611></path></svg><span class="text-black text-base font-normal" data-v-c340e611>${ssrInterpolate(location.value.city || "Fetching...")}</span></div>`);
            _push(ssrRenderComponent(_component_NuxtLink, {
              to: "/auth/login",
              class: "text-[#7B27E7] text-base font-semibold text-center"
            }, {
              default: withCtx((_, _push2, _parent2, _scopeId) => {
                if (_push2) {
                  _push2(`Login `);
                } else {
                  return [
                    createTextVNode("Login ")
                  ];
                }
              }),
              _: 1
            }, _parent));
            _push(ssrRenderComponent(_component_NuxtLink, {
              to: "/",
              class: "bg-purple-600 text-white text-base font-normal px-5 py-2 rounded-full text-center"
            }, {
              default: withCtx((_, _push2, _parent2, _scopeId) => {
                if (_push2) {
                  _push2(` List your business`);
                } else {
                  return [
                    createTextVNode(" List your business")
                  ];
                }
              }),
              _: 1
            }, _parent));
            _push(`</div></div>`);
          }
          _push(`</div>`);
        } else {
          _push(`<!---->`);
        }
        _push(`</header>`);
      } else {
        _push(`<!---->`);
      }
    };
  }
});
const _sfc_setup$2 = _sfc_main$2.setup;
_sfc_main$2.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("components/Header.vue");
  return _sfc_setup$2 ? _sfc_setup$2(props, ctx) : void 0;
};
const Header = /* @__PURE__ */ _export_sfc(_sfc_main$2, [["__scopeId", "data-v-c340e611"]]);
const _imports_1 = publicAssetsURL("/icon/play-store.svg");
const _imports_2 = publicAssetsURL("/icon/app-store.svg");
const _imports_3 = publicAssetsURL("/icon/facebook-icon.svg");
const _imports_4 = publicAssetsURL("/icon/twitter-icon.svg");
const _imports_5 = publicAssetsURL("/icon/linkedin-icon.svg");
const _imports_6 = publicAssetsURL("/icon/instagram-icon.svg");
const _sfc_main$1 = {};
function _sfc_ssrRender(_ctx, _push, _parent, _attrs) {
  const _component_NuxtLink = __nuxt_component_0$2;
  _push(`<footer${ssrRenderAttrs(mergeProps({ class: "bg-purple-50 text-gray-700 text-sm pt-10" }, _attrs))}><div class="max-w-screen-2xl mx-auto px-6 sm:px-8 md:px-16 lg:px-20 xl:px-20 2xl:px-20"><div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-4 gap-12 pb-4 items-start"><div><div class="flex items-center gap-2 mb-4"><img${ssrRenderAttr("src", _imports_0)} alt="footer-logo"></div><p class="mb-4 leading-relaxed text-[16px] text-[#000000]"> Bookslotz lets you book salon and spa services online\u2014quick, easy, and hassle-free. </p><div class="flex sm:flex-row flex-col gap-2"><a href="#" class="flex items-center justify-center sm:justify-start border border-gray-300 rounded-lg px-4 py-2 hover:shadow transition" style="${ssrRenderStyle({ "min-width": "160px" })}"><img${ssrRenderAttr("src", _imports_1)} alt="PlayStore" class="h-6 w-6 mr-2"><span class="font-medium text-[15px]">Google Play</span></a><a href="#" class="flex items-center justify-center sm:justify-start border border-gray-300 rounded-lg px-4 py-2 hover:shadow transition" style="${ssrRenderStyle({ "min-width": "160px" })}"><img${ssrRenderAttr("src", _imports_2)} alt="AppStore" class="h-6 w-6 mr-2"><span class="font-medium text-[15px]">App Store</span></a></div></div><div><h3 class="font-bold text-[19px] mb-3">Quick Access</h3><ul class="space-y-2 text-[15px] text-[#595959]"><li>`);
  _push(ssrRenderComponent(_component_NuxtLink, { to: "/" }, {
    default: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(`Home`);
      } else {
        return [
          createTextVNode("Home")
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(`</li><li>`);
  _push(ssrRenderComponent(_component_NuxtLink, { to: "/" }, {
    default: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(`Account`);
      } else {
        return [
          createTextVNode("Account")
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(`</li><li>`);
  _push(ssrRenderComponent(_component_NuxtLink, { to: "/terms-and-condition" }, {
    default: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(`Terms &amp; Uses`);
      } else {
        return [
          createTextVNode("Terms & Uses")
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(`</li><li>`);
  _push(ssrRenderComponent(_component_NuxtLink, { to: "/privacy-policy" }, {
    default: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(`Privacy Policy`);
      } else {
        return [
          createTextVNode("Privacy Policy")
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(`</li></ul></div><div><h3 class="font-bold text-[19px] mb-3">Account</h3><ul class="space-y-2 text-[15px] text-[#595959]"><li>`);
  _push(ssrRenderComponent(_component_NuxtLink, { to: "/contact-us" }, {
    default: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(`Contact Us`);
      } else {
        return [
          createTextVNode("Contact Us")
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(`</li><li>`);
  _push(ssrRenderComponent(_component_NuxtLink, { to: "/search" }, {
    default: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(`Services`);
      } else {
        return [
          createTextVNode("Services")
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(`</li><li>`);
  _push(ssrRenderComponent(_component_NuxtLink, { to: "/auth/signup" }, {
    default: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(`Signup`);
      } else {
        return [
          createTextVNode("Signup")
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(`</li><li>`);
  _push(ssrRenderComponent(_component_NuxtLink, { to: "/auth/login" }, {
    default: withCtx((_, _push2, _parent2, _scopeId) => {
      if (_push2) {
        _push2(`Login`);
      } else {
        return [
          createTextVNode("Login")
        ];
      }
    }),
    _: 1
  }, _parent));
  _push(`</li></ul></div><div><h3 class="font-bold text-[19px] mb-3">Contact Us</h3><ul class="space-y-2 text-[15px] text-[#595959] mb-4"><li class="flex items-start gap-2 text-[15px]"><span class="flex items-center" style="${ssrRenderStyle({ "color": "#7B27E7" })}"><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"><path fill="currentColor" d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7m0 9.5a2.5 2.5 0 0 1 0-5a2.5 2.5 0 0 1 0 5"></path></svg></span> NEXASSIST TECHNOLOGY PRIVATE LIMITED First Floor, No.52, SPD Plaza, Jyoti Niwas College Road, Near Jyothi Niwas College, Koramangala, Bengaluru, Karnataka\xA0\u2013\xA0560034 </li><li class="flex items-center gap-2"><span class="flex items-center" style="${ssrRenderStyle({ "color": "#7B27E7" })}"><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"><path fill="currentColor" d="M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24c1.12.37 2.33.57 3.57.57c.55 0 1 .45 1 1V20c0 .55-.45 1-1 1c-9.39 0-17-7.61-17-17c0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1c0 1.25.2 2.45.57 3.57c.11.35.03.74-.25 1.02z"></path></svg></span> +91-9980248021 </li><li class="flex items-center gap-2"><span class="flex items-center" style="${ssrRenderStyle({ "color": "#7B27E7" })}"><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M3.172 5.172C2 6.343 2 8.229 2 12s0 5.657 1.172 6.828S6.229 20 10 20h4c3.771 0 5.657 0 6.828-1.172S22 15.771 22 12s0-5.657-1.172-6.828S17.771 4 14 4h-4C6.229 4 4.343 4 3.172 5.172M18.576 7.52a.75.75 0 0 1-.096 1.056l-2.196 1.83c-.887.74-1.605 1.338-2.24 1.746c-.66.425-1.303.693-2.044.693s-1.384-.269-2.045-.693c-.634-.408-1.352-1.007-2.239-1.745L5.52 8.577a.75.75 0 0 1 .96-1.153l2.16 1.799c.933.777 1.58 1.315 2.128 1.667c.529.34.888.455 1.233.455s.704-.114 1.233-.455c.547-.352 1.195-.89 2.128-1.667l2.159-1.8a.75.75 0 0 1 1.056.097" clip-rule="evenodd"></path></svg></span><a href="mailto:<EMAIL>" class="hover:underline"><EMAIL></a></li></ul><div class="flex gap-3"><a href="#" class="rounded flex items-center justify-center"><img${ssrRenderAttr("src", _imports_3)} alt="Facebook" class="h-8 w-8"></a><a href="#" class="rounded flex items-center justify-center"><img${ssrRenderAttr("src", _imports_4)} alt="X (Twitter)" class="h-8 w-8"></a><a href="#" class="rounded flex items-center justify-center"><img${ssrRenderAttr("src", _imports_5)} alt="LinkedIn" class="h-8 w-8"></a><a href="#" class="rounded flex items-center justify-center"><img${ssrRenderAttr("src", _imports_6)} alt="Instagram" class="h-8 w-8"></a></div></div></div><div class="border-t border-gray-200 py-4 flex flex-col md:flex-row justify-between items-center text-[15px] text-[#000000] space-y-2 md:space-y-0 text-center md:text-left"><p>\xA9 Copyright 2025 Bookslotz, All rights reserved.</p><p>Designed &amp; Developed by Leopard Tech Labs Pvt Ltd</p></div></div></footer>`);
}
const _sfc_setup$1 = _sfc_main$1.setup;
_sfc_main$1.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("components/Footer.vue");
  return _sfc_setup$1 ? _sfc_setup$1(props, ctx) : void 0;
};
const Footer = /* @__PURE__ */ _export_sfc(_sfc_main$1, [["ssrRender", _sfc_ssrRender]]);
const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "default",
  __ssrInlineRender: true,
  setup(__props) {
    return (_ctx, _push, _parent, _attrs) => {
      const _component_NuxtPage = __nuxt_component_0;
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "min-h-screen flex flex-col" }, _attrs))}>`);
      _push(ssrRenderComponent(Header, null, null, _parent));
      _push(`<main class="flex-1">`);
      _push(ssrRenderComponent(_component_NuxtPage, null, null, _parent));
      _push(`</main>`);
      _push(ssrRenderComponent(Footer, null, null, _parent));
      _push(`</div>`);
    };
  }
});
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("layouts/default.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};

export { _sfc_main as default };
//# sourceMappingURL=default-BJzAe-5q.mjs.map
