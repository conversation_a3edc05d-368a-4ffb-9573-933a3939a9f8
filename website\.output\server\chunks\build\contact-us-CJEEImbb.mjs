import { mergeProps, useSSRContext } from 'vue';
import { ssrRenderAttrs } from 'vue/server-renderer';
import { _ as _export_sfc } from './server.mjs';
import '../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:url';
import '@iconify/utils';
import 'node:crypto';
import 'consola';
import 'node:path';
import 'better-sqlite3';
import 'ipx';
import 'pinia';
import 'vue-router';
import 'cookie';
import '@iconify/vue';
import 'tailwindcss/colors';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import 'unhead/server';
import 'devalue';
import 'unhead/plugins';
import 'unhead/utils';

const _sfc_main = {};
function _sfc_ssrRender(_ctx, _push, _parent, _attrs) {
  _push(`<div${ssrRenderAttrs(mergeProps({ class: "min-h-screen bg-white flex items-center justify-center" }, _attrs))}><div class="w-full max-w-7xl mx-auto px-6 sm:px-8 md:px-16 lg:px-20 xl:px-20 2xl:px-20 py-16"><div class="flex flex-col md:flex-row gap-12 md:gap-20 items-stretch"><div class="flex-1 flex flex-col justify-center"><h2 class="text-[36px] font-bold mb-1"> Contact Us <span class="text-[#7B27E7]">!</span></h2><p class="text-gray-600 text-[16px] mb-8"> If you have any questions or concerns about this Cookie Policy, please contact us: </p><div class="space-y-6"><div class="flex items-start gap-4"><span class="flex-shrink-0 w-10 h-10 rounded-full bg-[#7B27E7] flex items-center justify-center"><svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7z"></path><circle cx="12" cy="9" r="2.5" fill="white"></circle></svg></span><div><div class="text-[18px] font-semibold">Address</div><div class="text-gray-700 text-[16px] leading-relaxed"> NEXASSIST TECHNOLOGY PRIVATE LIMITED,<br> First Floor, No.52, SPD Plaza<br> Near Jyothi Niwas College<br> Koramangala, Bengaluru<br> Karnataka\xA0\u2013\xA0560034 </div></div></div><div class="flex items-start gap-4"><span class="flex-shrink-0 w-10 h-10 rounded-full bg-[#7B27E7] flex items-center justify-center"><svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" d="M6.62 10.79a15.05 15.05 0 006.59 6.59l2.2-2.2a1 1 0 011.02-.24c1.12.37 2.33.57 3.57.57a1 1 0 011 1V20a1 1 0 01-1 1c-9.39 0-17-7.61-17-17a1 1 0 011-1h3.5a1 1 0 011 1c0 1.25.2 2.45.57 3.57a1 1 0 01-.25 1.02l-2.2 2.2z"></path></svg></span><div><div class="text-[18px] font-semibold">Phone Number</div><div class="text-gray-700 text-[16px]">+91-9980248021</div></div></div><div class="flex items-start gap-4"><span class="flex-shrink-0 w-10 h-10 rounded-full bg-[#7B27E7] flex items-center justify-center"><svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path></svg></span><div><div class="text-[18px] font-semibold">E-mail</div><div class="text-gray-700 text-[16px]"><a href="mailto:<EMAIL>" class="hover:underline"><EMAIL></a></div></div></div></div></div><div class="flex-1"><form class="bg-white rounded-2xl shadow-lg border border-gray-100 p-8 space-y-6 max-w-md mx-auto"><h3 class="text-[30px] font-bold mb-1"> Send us a <span class="text-[#7B27E7]">Message</span></h3><p class="text-gray-600 mb-4 text-[18px]">Please enter your queries</p><div><label class="block dm-sansmedium text-[16px] font-[500] mb-1">Name</label><input type="text" class="w-full bg-[#F5F9FE] border border-gray-100 rounded-md p-3 focus:outline-none focus:ring-2 focus:ring-[#7B27E7]"></div><div><label class="block dm-sansmedium text-[16px] font-[500] mb-1">Email</label><input type="email" class="w-full bg-[#F5F9FE] border border-gray-100 rounded-md p-3 focus:outline-none focus:ring-2 focus:ring-[#7B27E7]"></div><div><label class="block dm-sansmedium text-[16px] font-[500] mb-1">Message</label><textarea rows="4" class="w-full bg-[#F5F9FE] border border-gray-100 rounded-md p-3 focus:outline-none focus:ring-2 focus:ring-[#7B27E7]"></textarea></div><button type="submit" class="w-full bg-[#7B27E7] hover:bg-purple-700 text-white font-semibold py-2.5 rounded-full transition-all"> Submit </button></form></div></div></div></div>`);
}
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/contact-us.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const contactUs = /* @__PURE__ */ _export_sfc(_sfc_main, [["ssrRender", _sfc_ssrRender]]);

export { contactUs as default };
//# sourceMappingURL=contact-us-CJEEImbb.mjs.map
