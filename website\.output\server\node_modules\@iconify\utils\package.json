{"name": "@iconify/utils", "type": "module", "description": "Common functions for working with Iconify icon sets used by various packages.", "author": "<PERSON><PERSON><PERSON><PERSON>", "version": "2.3.0", "license": "MIT", "bugs": "https://github.com/iconify/iconify/issues", "homepage": "https://iconify.design/docs/libraries/utils/", "repository": {"type": "git", "url": "https://github.com/iconify/iconify.git", "directory": "packages/utils"}, "sideEffects": false, "main": "lib/index.cjs", "module": "lib/index.mjs", "types": "lib/index.d.ts", "exports": {".": {"types": "./lib/index.d.ts", "require": "./lib/index.cjs", "import": "./lib/index.mjs"}, "./*": "./*", "./lib/colors": {"types": "./lib/colors/index.d.ts", "require": "./lib/colors/index.cjs", "import": "./lib/colors/index.mjs"}, "./lib/colors/index": {"types": "./lib/colors/index.d.ts", "require": "./lib/colors/index.cjs", "import": "./lib/colors/index.mjs"}, "./lib/colors/keywords": {"types": "./lib/colors/keywords.d.ts", "require": "./lib/colors/keywords.cjs", "import": "./lib/colors/keywords.mjs"}, "./lib/colors/types": {"types": "./lib/colors/types.d.ts", "require": "./lib/colors/types.cjs", "import": "./lib/colors/types.mjs"}, "./lib/css/common": {"types": "./lib/css/common.d.ts", "require": "./lib/css/common.cjs", "import": "./lib/css/common.mjs"}, "./lib/css/format": {"types": "./lib/css/format.d.ts", "require": "./lib/css/format.cjs", "import": "./lib/css/format.mjs"}, "./lib/css/icon": {"types": "./lib/css/icon.d.ts", "require": "./lib/css/icon.cjs", "import": "./lib/css/icon.mjs"}, "./lib/css/icons": {"types": "./lib/css/icons.d.ts", "require": "./lib/css/icons.cjs", "import": "./lib/css/icons.mjs"}, "./lib/css/types": {"types": "./lib/css/types.d.ts", "require": "./lib/css/types.cjs", "import": "./lib/css/types.mjs"}, "./lib/customisations/bool": {"types": "./lib/customisations/bool.d.ts", "require": "./lib/customisations/bool.cjs", "import": "./lib/customisations/bool.mjs"}, "./lib/customisations/defaults": {"types": "./lib/customisations/defaults.d.ts", "require": "./lib/customisations/defaults.cjs", "import": "./lib/customisations/defaults.mjs"}, "./lib/customisations/flip": {"types": "./lib/customisations/flip.d.ts", "require": "./lib/customisations/flip.cjs", "import": "./lib/customisations/flip.mjs"}, "./lib/customisations/merge": {"types": "./lib/customisations/merge.d.ts", "require": "./lib/customisations/merge.cjs", "import": "./lib/customisations/merge.mjs"}, "./lib/customisations/rotate": {"types": "./lib/customisations/rotate.d.ts", "require": "./lib/customisations/rotate.cjs", "import": "./lib/customisations/rotate.mjs"}, "./lib/emoji/regex/base": {"types": "./lib/emoji/regex/base.d.ts", "require": "./lib/emoji/regex/base.cjs", "import": "./lib/emoji/regex/base.mjs"}, "./lib/emoji/regex/create": {"types": "./lib/emoji/regex/create.d.ts", "require": "./lib/emoji/regex/create.cjs", "import": "./lib/emoji/regex/create.mjs"}, "./lib/emoji/regex/numbers": {"types": "./lib/emoji/regex/numbers.d.ts", "require": "./lib/emoji/regex/numbers.cjs", "import": "./lib/emoji/regex/numbers.mjs"}, "./lib/emoji/regex/similar": {"types": "./lib/emoji/regex/similar.d.ts", "require": "./lib/emoji/regex/similar.cjs", "import": "./lib/emoji/regex/similar.mjs"}, "./lib/emoji/regex/tree": {"types": "./lib/emoji/regex/tree.d.ts", "require": "./lib/emoji/regex/tree.cjs", "import": "./lib/emoji/regex/tree.mjs"}, "./lib/emoji/replace/find": {"types": "./lib/emoji/replace/find.d.ts", "require": "./lib/emoji/replace/find.cjs", "import": "./lib/emoji/replace/find.mjs"}, "./lib/emoji/replace/replace": {"types": "./lib/emoji/replace/replace.d.ts", "require": "./lib/emoji/replace/replace.cjs", "import": "./lib/emoji/replace/replace.mjs"}, "./lib/emoji/test/components": {"types": "./lib/emoji/test/components.d.ts", "require": "./lib/emoji/test/components.cjs", "import": "./lib/emoji/test/components.mjs"}, "./lib/emoji/test/missing": {"types": "./lib/emoji/test/missing.d.ts", "require": "./lib/emoji/test/missing.cjs", "import": "./lib/emoji/test/missing.mjs"}, "./lib/emoji/test/name": {"types": "./lib/emoji/test/name.d.ts", "require": "./lib/emoji/test/name.cjs", "import": "./lib/emoji/test/name.mjs"}, "./lib/emoji/test/parse": {"types": "./lib/emoji/test/parse.d.ts", "require": "./lib/emoji/test/parse.cjs", "import": "./lib/emoji/test/parse.mjs"}, "./lib/emoji/test/tree": {"types": "./lib/emoji/test/tree.d.ts", "require": "./lib/emoji/test/tree.cjs", "import": "./lib/emoji/test/tree.mjs"}, "./lib/emoji/test/similar": {"types": "./lib/emoji/test/similar.d.ts", "require": "./lib/emoji/test/similar.cjs", "import": "./lib/emoji/test/similar.mjs"}, "./lib/emoji/test/variations": {"types": "./lib/emoji/test/variations.d.ts", "require": "./lib/emoji/test/variations.cjs", "import": "./lib/emoji/test/variations.mjs"}, "./lib/emoji/cleanup": {"types": "./lib/emoji/cleanup.d.ts", "require": "./lib/emoji/cleanup.cjs", "import": "./lib/emoji/cleanup.mjs"}, "./lib/emoji/convert": {"types": "./lib/emoji/convert.d.ts", "require": "./lib/emoji/convert.cjs", "import": "./lib/emoji/convert.mjs"}, "./lib/emoji/data": {"types": "./lib/emoji/data.d.ts", "require": "./lib/emoji/data.cjs", "import": "./lib/emoji/data.mjs"}, "./lib/emoji/format": {"types": "./lib/emoji/format.d.ts", "require": "./lib/emoji/format.cjs", "import": "./lib/emoji/format.mjs"}, "./lib/emoji/parse": {"types": "./lib/emoji/parse.d.ts", "require": "./lib/emoji/parse.cjs", "import": "./lib/emoji/parse.mjs"}, "./lib/icon-set/convert-info": {"types": "./lib/icon-set/convert-info.d.ts", "require": "./lib/icon-set/convert-info.cjs", "import": "./lib/icon-set/convert-info.mjs"}, "./lib/icon-set/expand": {"types": "./lib/icon-set/expand.d.ts", "require": "./lib/icon-set/expand.cjs", "import": "./lib/icon-set/expand.mjs"}, "./lib/icon-set/get-icon": {"types": "./lib/icon-set/get-icon.d.ts", "require": "./lib/icon-set/get-icon.cjs", "import": "./lib/icon-set/get-icon.mjs"}, "./lib/icon-set/get-icons": {"types": "./lib/icon-set/get-icons.d.ts", "require": "./lib/icon-set/get-icons.cjs", "import": "./lib/icon-set/get-icons.mjs"}, "./lib/icon-set/minify": {"types": "./lib/icon-set/minify.d.ts", "require": "./lib/icon-set/minify.cjs", "import": "./lib/icon-set/minify.mjs"}, "./lib/icon-set/parse": {"types": "./lib/icon-set/parse.d.ts", "require": "./lib/icon-set/parse.cjs", "import": "./lib/icon-set/parse.mjs"}, "./lib/icon-set/tree": {"types": "./lib/icon-set/tree.d.ts", "require": "./lib/icon-set/tree.cjs", "import": "./lib/icon-set/tree.mjs"}, "./lib/icon-set/validate": {"types": "./lib/icon-set/validate.d.ts", "require": "./lib/icon-set/validate.cjs", "import": "./lib/icon-set/validate.mjs"}, "./lib/icon-set/validate-basic": {"types": "./lib/icon-set/validate-basic.d.ts", "require": "./lib/icon-set/validate-basic.cjs", "import": "./lib/icon-set/validate-basic.mjs"}, "./lib/icon/defaults": {"types": "./lib/icon/defaults.d.ts", "require": "./lib/icon/defaults.cjs", "import": "./lib/icon/defaults.mjs"}, "./lib/icon/merge": {"types": "./lib/icon/merge.d.ts", "require": "./lib/icon/merge.cjs", "import": "./lib/icon/merge.mjs"}, "./lib/icon/name": {"types": "./lib/icon/name.d.ts", "require": "./lib/icon/name.cjs", "import": "./lib/icon/name.mjs"}, "./lib/icon/square": {"types": "./lib/icon/square.d.ts", "require": "./lib/icon/square.cjs", "import": "./lib/icon/square.mjs"}, "./lib/icon/transformations": {"types": "./lib/icon/transformations.d.ts", "require": "./lib/icon/transformations.cjs", "import": "./lib/icon/transformations.mjs"}, "./lib": {"types": "./lib/index.d.ts", "require": "./lib/index.cjs", "import": "./lib/index.mjs"}, "./lib/index": {"types": "./lib/index.d.ts", "require": "./lib/index.cjs", "import": "./lib/index.mjs"}, "./lib/loader/custom": {"types": "./lib/loader/custom.d.ts", "require": "./lib/loader/custom.cjs", "import": "./lib/loader/custom.mjs"}, "./lib/loader/external-pkg": {"types": "./lib/loader/external-pkg.d.ts", "require": "./lib/loader/external-pkg.cjs", "import": "./lib/loader/external-pkg.mjs"}, "./lib/loader/fs": {"types": "./lib/loader/fs.d.ts", "require": "./lib/loader/fs.cjs", "import": "./lib/loader/fs.mjs"}, "./lib/loader/install-pkg": {"types": "./lib/loader/install-pkg.d.ts", "require": "./lib/loader/install-pkg.cjs", "import": "./lib/loader/install-pkg.mjs"}, "./lib/loader/loader": {"types": "./lib/loader/loader.d.ts", "require": "./lib/loader/loader.cjs", "import": "./lib/loader/loader.mjs"}, "./lib/loader/modern": {"types": "./lib/loader/modern.d.ts", "require": "./lib/loader/modern.cjs", "import": "./lib/loader/modern.mjs"}, "./lib/loader/node-loader": {"types": "./lib/loader/node-loader.d.ts", "require": "./lib/loader/node-loader.cjs", "import": "./lib/loader/node-loader.mjs"}, "./lib/loader/node-loaders": {"types": "./lib/loader/node-loaders.d.ts", "require": "./lib/loader/node-loaders.cjs", "import": "./lib/loader/node-loaders.mjs"}, "./lib/loader/types": {"types": "./lib/loader/types.d.ts", "require": "./lib/loader/types.cjs", "import": "./lib/loader/types.mjs"}, "./lib/loader/utils": {"types": "./lib/loader/utils.d.ts", "require": "./lib/loader/utils.cjs", "import": "./lib/loader/utils.mjs"}, "./lib/loader/warn": {"types": "./lib/loader/warn.d.ts", "require": "./lib/loader/warn.cjs", "import": "./lib/loader/warn.mjs"}, "./lib/misc/strings": {"types": "./lib/misc/strings.d.ts", "require": "./lib/misc/strings.cjs", "import": "./lib/misc/strings.mjs"}, "./lib/misc/title": {"types": "./lib/misc/title.d.ts", "require": "./lib/misc/title.cjs", "import": "./lib/misc/title.mjs"}, "./lib/misc/licenses": {"types": "./lib/misc/licenses.d.ts", "require": "./lib/misc/licenses.cjs", "import": "./lib/misc/licenses.mjs"}, "./lib/misc/objects": {"types": "./lib/misc/objects.d.ts", "require": "./lib/misc/objects.cjs", "import": "./lib/misc/objects.mjs"}, "./lib/svg/build": {"types": "./lib/svg/build.d.ts", "require": "./lib/svg/build.cjs", "import": "./lib/svg/build.mjs"}, "./lib/svg/defs": {"types": "./lib/svg/defs.d.ts", "require": "./lib/svg/defs.cjs", "import": "./lib/svg/defs.mjs"}, "./lib/svg/encode-svg-for-css": {"types": "./lib/svg/encode-svg-for-css.d.ts", "require": "./lib/svg/encode-svg-for-css.cjs", "import": "./lib/svg/encode-svg-for-css.mjs"}, "./lib/svg/html": {"types": "./lib/svg/html.d.ts", "require": "./lib/svg/html.cjs", "import": "./lib/svg/html.mjs"}, "./lib/svg/id": {"types": "./lib/svg/id.d.ts", "require": "./lib/svg/id.cjs", "import": "./lib/svg/id.mjs"}, "./lib/svg/inner-html": {"types": "./lib/svg/inner-html.d.ts", "require": "./lib/svg/inner-html.cjs", "import": "./lib/svg/inner-html.mjs"}, "./lib/svg/parse": {"types": "./lib/svg/parse.d.ts", "require": "./lib/svg/parse.cjs", "import": "./lib/svg/parse.mjs"}, "./lib/svg/pretty": {"types": "./lib/svg/pretty.d.ts", "require": "./lib/svg/pretty.cjs", "import": "./lib/svg/pretty.mjs"}, "./lib/svg/size": {"types": "./lib/svg/size.d.ts", "require": "./lib/svg/size.cjs", "import": "./lib/svg/size.mjs"}, "./lib/svg/trim": {"types": "./lib/svg/trim.d.ts", "require": "./lib/svg/trim.cjs", "import": "./lib/svg/trim.mjs"}, "./lib/svg/url": {"types": "./lib/svg/url.d.ts", "require": "./lib/svg/url.cjs", "import": "./lib/svg/url.mjs"}, "./lib/svg/viewbox": {"types": "./lib/svg/viewbox.d.ts", "require": "./lib/svg/viewbox.cjs", "import": "./lib/svg/viewbox.mjs"}}, "files": ["lib", "*.d.ts"], "dependencies": {"@antfu/install-pkg": "^1.0.0", "@antfu/utils": "^8.1.0", "debug": "^4.4.0", "globals": "^15.14.0", "kolorist": "^1.8.0", "local-pkg": "^1.0.0", "mlly": "^1.7.4", "@iconify/types": "^2.0.0"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.19.0", "@iconify-json/fa6-regular": "^1.2.3", "@iconify-json/flat-color-icons": "^1.2.1", "@types/debug": "^4.1.12", "@types/jest": "^29.5.14", "@types/node": "^18.19.74", "@typescript-eslint/eslint-plugin": "^8.21.0", "eslint": "^9.19.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.3", "rimraf": "^6.0.1", "typescript": "^5.7.3", "unbuild": "^2.0.0", "vitest": "^2.1.8"}, "scripts": {"clean": "rimraf lib tsconfig.tsbuildinfo", "lint": "eslint --fix src/**/*.ts", "prebuild": "pnpm run lint && pnpm run clean", "build": "unbuild", "test:cjs": "vitest --config vitest.config.cjs", "test:esm": "vitest --config vitest.config.mjs", "test": "node ./scripts/prepare-tests.mjs && pnpm run test:cjs && pnpm run test:esm"}}