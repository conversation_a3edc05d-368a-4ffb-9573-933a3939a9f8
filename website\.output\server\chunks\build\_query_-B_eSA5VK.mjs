import { defineComponent, ref, mergeProps, unref, useSSRContext } from 'vue';
import { ssrRenderAttrs, ssrRenderComponent, ssrRenderClass, ssrInterpolate, ssrRenderList, ssrRenderAttr } from 'vue/server-renderer';
import { useRoute, useRouter } from 'vue-router';
import { b as useUserStore, c as useRuntimeConfig, d as useState, L as Loader, S as Snackbar, e as _sfc_main$1 } from './server.mjs';
import { u as useSearchStore } from './search-CN1Jpzjq.mjs';
import { u as useAppSeoMeta } from './useAppSeoMeta-HWukB6FN.mjs';
import '../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:url';
import '@iconify/utils';
import 'node:crypto';
import 'consola';
import 'node:path';
import 'better-sqlite3';
import 'ipx';
import 'pinia';
import 'cookie';
import '@iconify/vue';
import 'tailwindcss/colors';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import 'unhead/server';
import 'devalue';
import 'unhead/plugins';
import 'unhead/utils';

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "[query]",
  __ssrInlineRender: true,
  setup(__props) {
    const route = useRoute();
    useRouter();
    useUserStore();
    useSearchStore();
    const { public: { BASE_URL } } = useRuntimeConfig();
    const isLoading = useState("globalLoader", () => false);
    const hasError = ref(false);
    const snackbarRef = ref(null);
    const showFilterModal = ref(false);
    const mobileView = ref("list");
    ref(null);
    ref(null);
    const salons = ref([]);
    useAppSeoMeta({
      title: `Search Results for "${route.params.query}" | Bookslotz`,
      ogTitle: `Search Results for "${route.params.query}" | Bookslotz`,
      description: `Find the best salons matching your search for "${route.params.query}". Browse salons, view ratings, and book appointments on Bookslotz.`,
      ogDescription: `Find the best salons matching your search for "${route.params.query}". Browse salons, view ratings, and book appointments on Bookslotz.`
    });
    const sortOptions = ["Top-Rated", "Recommended", "Trending"];
    const selectedSort = ref("Top-Rated");
    const price = ref(2e3);
    const venueTypes = ["All", "Male", "Female", "Child"];
    const venueTypeSelected = ref(["All"]);
    const categories = ref([{ name: "All", slug: "all" }]);
    const categorySelected = ref(["all"]);
    return (_ctx, _push, _parent, _attrs) => {
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "min-h-screen bg-gray-50 px-4 sm:px-12 lg:px-6" }, _attrs))}>`);
      if (unref(isLoading)) {
        _push(ssrRenderComponent(Loader, null, null, _parent));
      } else {
        _push(`<!---->`);
      }
      _push(ssrRenderComponent(Snackbar, {
        ref_key: "snackbarRef",
        ref: snackbarRef
      }, null, _parent));
      if (hasError.value) {
        _push(ssrRenderComponent(_sfc_main$1, { error: { statusCode: 500, message: "Something went wrong. Please try again later." } }, null, _parent));
      } else {
        _push(`<!---->`);
      }
      if (!hasError.value) {
        _push(`<div class="lg:hidden px-4 py-4"><div class="flex items-center gap-2 mb-4"><button class="px-8 py-2 bg-purple-600 text-white text-sm font-medium rounded-lg"> Salon </button><button class="px-4 py-2 text-gray-600 text-sm font-medium hover:bg-gray-100 rounded-full"> clear results </button></div><div class="flex bg-gray-100 rounded-lg p-1 mb-4"><button class="${ssrRenderClass([
          "flex-1 py-2 text-sm font-medium rounded-md transition-colors",
          mobileView.value === "list" ? "bg-white text-purple-600 shadow-sm" : "text-gray-600"
        ])}"> List View </button><button class="${ssrRenderClass([
          "flex-1 py-2 text-sm font-medium rounded-md transition-colors",
          mobileView.value === "map" ? "bg-white text-purple-600 shadow-sm" : "text-gray-600"
        ])}"> Map View </button></div></div>`);
      } else {
        _push(`<!---->`);
      }
      if (!hasError.value) {
        _push(`<div class="flex flex-col lg:flex-row lg:py-8 gap-3 lg:gap-20 px-4 sm:px-6 lg:px-8 xl:px-12"><div class="${ssrRenderClass([
          "w-full lg:w-2/5",
          mobileView.value === "list" ? "block" : "hidden lg:block"
        ])}"><div class="hidden lg:flex items-center gap-2 mb-6"><button class="px-8 py-2 bg-purple-600 text-white text-sm font-medium rounded-lg"> Salon </button><button class="px-4 py-2 text-gray-600 text-sm font-medium hover:bg-gray-100 rounded-full"> clear results </button></div><div class="bg-white rounded-lg p-4 lg:p-6 shadow-sm"><div class="flex items-center justify-between mb-4"><span class="text-gray-900 font-medium text-sm">${ssrInterpolate(salons.value.length)} Results have found </span><button class="flex items-center gap-2 border-2 border-purple-600 rounded-full px-4 lg:px-6 py-2 text-sm font-medium text-gray-900 hover:bg-purple-50 transition-colors duration-150 cursor-pointer"> Filter <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path></svg></button></div><div class="border-b border-gray-200 mb-6 -mx-4 lg:-mx-6"></div><div class="space-y-4"><!--[-->`);
        ssrRenderList(salons.value, (salon) => {
          _push(`<div class="bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden hover:shadow-md transition-shadow cursor-pointer"><div class="relative h-40 sm:h-48"><img${ssrRenderAttr("src", salon.image)}${ssrRenderAttr("alt", salon.name)} class="w-full h-full object-cover"><button class="absolute bottom-3 right-3 w-8 h-8 bg-white rounded-full flex items-center justify-center shadow hover:shadow-md transition">`);
          if (salon.favorited) {
            _push(`<svg class="w-5 h-5 text-red-500" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd"></path></svg>`);
          } else {
            _push(`<svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path></svg>`);
          }
          _push(`</button></div><div class="p-4"><h3 class="font-semibold text-gray-900 text-base lg:text-lg mb-2">${ssrInterpolate(salon.name)}</h3><div class="flex items-center text-gray-500 text-sm mb-2"><svg class="w-4 h-4 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path></svg> ${ssrInterpolate(salon.location)}</div><div class="flex items-center mb-4"><svg class="w-4 h-4 text-yellow-400 fill-current mr-1" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3 .921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784 .57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81 .588-1.81h3.461a1 0 00.951-.69l1.07-3.292z"></path></svg><span class="font-semibold text-gray-900 text-sm mr-1">${ssrInterpolate(salon.rating)}</span><span class="text-gray-500 text-sm">(${ssrInterpolate(salon.reviews)})</span></div></div></div>`);
        });
        _push(`<!--]--></div></div></div><div class="${ssrRenderClass([[
          "w-full lg:w-3/5",
          mobileView.value === "map" ? "block" : "hidden lg:block",
          "h-[calc(100vh-200px)] lg:h-auto"
        ], "mt-15"])}"><div class="bg-white rounded-lg shadow-sm h-full lg:min-h-[600px] overflow-hidden"><div id="map" class="w-full h-full"></div></div></div></div>`);
      } else {
        _push(`<!---->`);
      }
      if (showFilterModal.value) {
        _push(`<div class="fixed inset-0 z-80 bg-black/50 bg-opacity-60 flex items-center justify-center px-4"><div class="bg-white rounded-2xl w-full max-w-md p-4 sm:p-10 relative max-h-[98vh] overflow-y-auto"><div class="flex items-center justify-between mb-3"><h2 class="text-[25px] font-semibold">Filter</h2><button class="text-gray-600 hover:text-black text-2xl leading-none cursor-pointer" aria-label="Close"> \u2715 </button></div><div class="border-b border-gray-200 mb-3"></div><div class="mb-3"><h3 class="text-[18px] font-semibold mb-2">Sort by</h3><div class="space-y-2"><!--[-->`);
        ssrRenderList(sortOptions, (option) => {
          _push(`<div class="flex items-center justify-between cursor-pointer px-0 py-1 rounded-md hover:bg-gray-50"><span class="text-[16px]">${ssrInterpolate(option)}</span><span class="${ssrRenderClass([
            selectedSort.value === option ? "border-purple-600 bg-purple-600 text-white" : "border-gray-300 bg-white text-white",
            "w-5 h-5 flex items-center justify-center border-2 rounded-full"
          ])}">`);
          if (selectedSort.value === option) {
            _push(`<svg xmlns="http://www.w3.org/2000/svg" class="w-7 h-7" viewBox="0 0 24 24"><path fill="currentColor" d="m10.5 16.2l-4-4l1.4-1.4l2.6 2.6l5.6-5.6l1.4 1.4l-7 7Z"></path></svg>`);
          } else {
            _push(`<!---->`);
          }
          _push(`</span></div>`);
        });
        _push(`<!--]--></div></div><div class="mb-4"><h3 class="text-[18px] font-semibold mb-2">Price</h3><div class="flex items-center justify-between mb-3"><label class="block text-[16px] text-[400]">Range</label><span class="text-[16px] text-black-600">\u20B9${ssrInterpolate(price.value)}</span></div><input type="range" min="0" max="5000"${ssrRenderAttr("value", price.value)} class="w-full accent-[#7B27E7]"></div><div class="mb-4"><h3 class="text-[18px] font-semibold mb-3">Venue type</h3>`);
        if (venueTypeSelected.value) {
          _push(`<div class="flex gap-2 flex-wrap"><!--[-->`);
          ssrRenderList(venueTypes, (type) => {
            _push(`<button class="${ssrRenderClass([
              venueTypeSelected.value.includes(type) ? "bg-[#7B27E7] text-white" : "bg-gray-100 text-gray-600",
              "px-3 py-1 text-[16px] rounded-full cursor-pointer"
            ])}">${ssrInterpolate(type)}</button>`);
          });
          _push(`<!--]--></div>`);
        } else {
          _push(`<div class="text-red-500 text-sm"> Venue types are loading... </div>`);
        }
        _push(`</div><div class="mb-6"><h3 class="text-[18px] font-semibold mb-2">Category</h3>`);
        if (categorySelected.value) {
          _push(`<div class="flex gap-2 flex-wrap"><!--[-->`);
          ssrRenderList(categories.value, (cat) => {
            _push(`<button class="${ssrRenderClass([
              categorySelected.value.includes(cat.slug) ? "bg-[#7B27E7] text-white" : "bg-gray-100 text-gray-600",
              "px-3 py-1 text-[16px] rounded-full cursor-pointer"
            ])}">${ssrInterpolate(cat.name)}</button>`);
          });
          _push(`<!--]--></div>`);
        } else {
          _push(`<div class="text-red-500 text-sm"> Categories are loading... </div>`);
        }
        _push(`</div><div class="flex justify-end gap-2"><button class="px-6 py-2 rounded-full bg-[#7B27E733] text-purple-600 cursor-pointer"> Cancel </button><button class="px-6 py-2 rounded-full bg-[#7B27E7] text-white cursor-pointer"> Apply </button></div></div></div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div>`);
    };
  }
});
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/salon/[query].vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};

export { _sfc_main as default };
//# sourceMappingURL=_query_-B_eSA5VK.mjs.map
