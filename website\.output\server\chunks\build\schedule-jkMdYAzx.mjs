import { defineComponent, ref, computed, mergeProps, unref, useSSRContext } from 'vue';
import { ssrRenderAttrs, ssrRenderComponent, ssrInterpolate, ssrRenderList, ssrRenderClass, ssrRenderAttr, ssrIncludeBooleanAttr, ssrLooseContain, ssrLooseEqual } from 'vue/server-renderer';
import { u as useBookingStore } from './booking-D2Du41fq.mjs';
import { useRouter, useRoute } from 'vue-router';
import { u as useAppSeoMeta } from './useAppSeoMeta-HWukB6FN.mjs';
import { _ as _sfc_main$2 } from './BookingSummary-C7u1ob1e.mjs';
import { c as useRuntimeConfig, d as useState, L as Loader, S as Snackbar, e as _sfc_main$1 } from './server.mjs';
import 'pinia';
import '../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:url';
import '@iconify/utils';
import 'node:crypto';
import 'consola';
import 'node:path';
import 'better-sqlite3';
import 'ipx';
import 'cookie';
import '@iconify/vue';
import 'tailwindcss/colors';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import 'unhead/server';
import 'devalue';
import 'unhead/plugins';
import 'unhead/utils';

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "schedule",
  __ssrInlineRender: true,
  setup(__props) {
    useAppSeoMeta({
      title: "Select Schedule | Elite Beauty Salon"
    });
    const bookingStore = useBookingStore();
    const router = useRouter();
    const route = useRoute();
    const bookingSummaryRef = ref(null);
    const { public: { BASE_URL } } = useRuntimeConfig();
    const isLoading = useState("globalLoader", () => false);
    const hasError = ref(false);
    const snackbarRef = ref(null);
    const salon = ref({
      name: "Loading...",
      location: "MG Road, Mumbai",
      image: "/images/saloon-small-image.png"
    });
    const startDate = ref(/* @__PURE__ */ new Date());
    const selectedDate = ref(null);
    const selectedTimeSlot = ref(null);
    const showCalendarModal = ref(false);
    const calendarYear = ref((/* @__PURE__ */ new Date()).getFullYear());
    const calendarMonth = ref((/* @__PURE__ */ new Date()).getMonth());
    const selectedCalendarDate = ref(null);
    const calendarDays = ref([]);
    const monthNames = ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"];
    ref(null);
    const availableTimeSlots = ref([]);
    const salonSlug = computed(() => decodeURIComponent(route.query.slug || ""));
    const availableYears = computed(() => {
      const currentYear = (/* @__PURE__ */ new Date()).getFullYear();
      return Array.from({ length: 4 }, (_, i) => currentYear - 1 + i);
    });
    const servicesWithType = computed(() => {
      return bookingStore.selectedServices.map((service) => ({
        ...service,
        type: "service"
      }));
    });
    const currentMonthYear = computed(() => {
      return startDate.value.toLocaleDateString("en-US", { month: "long", year: "numeric" });
    });
    const weekDays = computed(() => {
      var _a;
      const days = [];
      const dayNames = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];
      const today = /* @__PURE__ */ new Date();
      for (let i = 0; i < 7; i++) {
        const date = new Date(startDate.value);
        date.setDate(startDate.value.getDate() + i);
        days.push({
          date: date.getDate(),
          dayName: dayNames[date.getDay()],
          fullDate: date,
          isToday: date.toDateString() === today.toDateString(),
          isSelected: ((_a = selectedDate.value) == null ? void 0 : _a.fullDate.toDateString()) === date.toDateString(),
          key: `${date.getFullYear()}-${date.getMonth()}-${date.getDate()}`
        });
      }
      return days;
    });
    const canContinue = computed(() => {
      return bookingStore.selectedServices.length > 0 && !!selectedDate.value && !!selectedTimeSlot.value;
    });
    function getDayStyles(day) {
      return day.isSelected ? "border-purple-600 bg-purple-600 text-white shadow-lg" : day.isToday ? "border-purple-300 bg-purple-50 text-purple-600" : "border-gray-200 bg-white text-gray-900 hover:border-gray-300 hover:bg-gray-50";
    }
    function getCalendarDayStyles(day) {
      if (!day.isCurrentMonth) return "text-gray-300 cursor-not-allowed";
      return day.isSelected ? "bg-purple-600 text-white font-semibold" : day.isToday ? "bg-purple-100 text-purple-600 font-semibold" : "text-gray-900 hover:bg-gray-100";
    }
    function getTimeSlotStyles(slot) {
      var _a;
      return [
        ((_a = selectedTimeSlot.value) == null ? void 0 : _a.id) === slot.id ? "border-purple-600 bg-purple-600 text-white shadow-sm" : "border-gray-200 bg-white text-gray-900 hover:border-gray-300 hover:bg-gray-50",
        slot.available ? "" : "opacity-50 cursor-not-allowed"
      ].join(" ");
    }
    function goToNextStep() {
      if (canContinue.value) {
        router.push({ path: "/booking/addons", query: { slug: salonSlug.value } });
      }
    }
    function removeService(serviceId) {
      bookingStore.removeService(serviceId);
    }
    return (_ctx, _push, _parent, _attrs) => {
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "min-h-screen bg-white relative" }, _attrs))}>`);
      if (unref(isLoading)) {
        _push(ssrRenderComponent(Loader, null, null, _parent));
      } else {
        _push(`<!---->`);
      }
      _push(ssrRenderComponent(Snackbar, {
        ref_key: "snackbarRef",
        ref: snackbarRef
      }, null, _parent));
      if (hasError.value) {
        _push(ssrRenderComponent(_sfc_main$1, { error: { statusCode: 500, message: "Something went wrong. Please try again later." } }, null, _parent));
      } else {
        _push(`<!---->`);
      }
      if (!hasError.value) {
        _push(`<div class="flex flex-col xl:flex-row max-w-8xl mx-auto px-1 sm:px-16 md:px-12 justify-between"><div class="flex-1 lg:flex-[0.5] px-6 lg:px-8 py-6 lg:py-8"><div class="flex items-center space-x-3 mb-8"><button class="w-10 h-10 rounded-full border border-gray-300 flex items-center justify-center group cursor-pointer"><svg xmlns="http://www.w3.org/2000/svg" class="w-7 h-7 text-purple-600 group-hover:text-purple-700 transition-colors" viewBox="0 0 24 24" fill="currentColor"><path d="m6.523 12.5l3.735 3.735q.146.146.153.344q.006.198-.153.363q-.166.166-.357.168t-.357-.162l-4.382-4.383q-.243-.242-.243-.565t.243-.566l4.382-4.382q.147-.146.347-.153q.201-.007.367.159q.16.165.162.353q.003.189-.162.354L6.523 11.5h12.38q.214 0 .358.143t.143.357t-.143.357t-.357.143z"></path></svg></button><h1 class="text-2xl font-semibold text-gray-900">Select Schedule</h1></div><div class="mb-8"><div class="flex items-center justify-between mb-6"><h2 class="text-lg font-semibold text-gray-900">${ssrInterpolate(currentMonthYear.value)}</h2><div class="flex items-center gap-2"><button class="w-8 h-8 flex items-center justify-center hover:bg-gray-50 transition-colors cursor-pointer"><svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6" viewBox="0 0 24 24" fill="currentColor"><path d="M7.85 13l2.85 2.85q.3.3.288.7t-.288.7q-.3.3-.712.313t-.713-.288L4.7 12.7q-.3-.3-.3-.7t.3-.7l4.575-4.575q.3-.3.713-.287t.712.312q.275.3.288.7t-.288.7L7.85 11H19q.425 0 .713.288T20 12t-.288.713T19 13z"></path></svg></button><button class="w-8 h-8 flex items-center justify-center hover:bg-gray-50 transition-colors cursor-pointer"><svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6" viewBox="0 0 24 24" fill="currentColor"><path d="M16.15 13H5q-.425 0-.712-.288T4 12t.288-.712T5 11h11.15L13.3 8.15q-.3-.3-.288-.7t.288-.7q.3-.3.713-.312t.712.287L19.3 11.3q.15.15.213.325t.062.375t-.062.375t-.213.325l-4.575 4.575q-.3.3-.712.288t-.713-.313q-.275-.3-.288-.7t.288-.7z"></path></svg></button><button class="w-8 h-8 flex items-center justify-center hover:bg-gray-50 transition-colors ml-2 cursor-pointer"><svg class="w-6 h-6 text-[#1976D2]" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path></svg></button></div></div><div class="mb-8"><div class="sm:hidden border border-gray-300 rounded-lg p-2"><div class="flex gap-2 overflow-x-auto pb-2 scrollbar-hide"><!--[-->`);
        ssrRenderList(weekDays.value, (day) => {
          _push(`<div class="${ssrRenderClass([getDayStyles(day), "flex flex-col items-center justify-center p-3 rounded-lg cursor-pointer transition-all duration-200 border-2 flex-shrink-0 min-w-[60px]"])}"><span class="text-xl font-semibold mb-1">${ssrInterpolate(day.date)}</span><span class="text-xs font-medium uppercase tracking-wide">${ssrInterpolate(day.dayName.slice(0, 3))}</span></div>`);
        });
        _push(`<!--]--></div></div><div class="hidden sm:grid grid-cols-7 gap-3 border border-gray-300 rounded-lg p-2"><!--[-->`);
        ssrRenderList(weekDays.value, (day) => {
          _push(`<div class="${ssrRenderClass([getDayStyles(day), "flex flex-col items-center justify-center p-4 rounded-lg cursor-pointer transition-all duration-200 border-2"])}"><span class="text-2xl font-semibold mb-2">${ssrInterpolate(day.date)}</span><span class="text-sm font-medium uppercase tracking-wide">${ssrInterpolate(day.dayName)}</span></div>`);
        });
        _push(`<!--]--></div></div></div>`);
        if (selectedDate.value) {
          _push(`<div class="space-y-4"><div class="space-y-3"><!--[-->`);
          ssrRenderList(availableTimeSlots.value, (slot) => {
            _push(`<div class="${ssrRenderClass([getTimeSlotStyles(slot), "flex items-center justify-between p-4 rounded-lg cursor-pointer transition-all duration-200 border"])}"><span class="text-base font-medium">${ssrInterpolate(slot.time)}</span></div>`);
          });
          _push(`<!--]-->`);
          if (!availableTimeSlots.value.length) {
            _push(`<div class="text-base text-gray-600"> No available time slots for this date. </div>`);
          } else {
            _push(`<!---->`);
          }
          _push(`</div></div>`);
        } else {
          _push(`<!---->`);
        }
        if (showCalendarModal.value) {
          _push(`<div class="fixed inset-0 bg-black/50 flex items-center justify-center z-50"><div class="bg-white rounded-xl shadow-2xl max-w-xs w-full mx-4 p-4"><div class="flex items-center justify-between mb-4"><h3 class="text-lg font-semibold text-gray-900">Select Date</h3><button class="w-7 h-7 rounded-full hover:bg-gray-100 flex items-center justify-center transition-colors cursor-pointer"><svg class="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg></button></div><div class="flex items-center justify-between mb-4"><div class="flex items-center gap-1"><select class="px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-purple-500"><!--[-->`);
          ssrRenderList(availableYears.value, (year) => {
            _push(`<option${ssrRenderAttr("value", year)}${ssrIncludeBooleanAttr(Array.isArray(calendarYear.value) ? ssrLooseContain(calendarYear.value, year) : ssrLooseEqual(calendarYear.value, year)) ? " selected" : ""}>${ssrInterpolate(year)}</option>`);
          });
          _push(`<!--]--></select><select class="px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-purple-500"><!--[-->`);
          ssrRenderList(monthNames, (month, index) => {
            _push(`<option${ssrRenderAttr("value", index)}${ssrIncludeBooleanAttr(Array.isArray(calendarMonth.value) ? ssrLooseContain(calendarMonth.value, index) : ssrLooseEqual(calendarMonth.value, index)) ? " selected" : ""}>${ssrInterpolate(month.slice(0, 3))}</option>`);
          });
          _push(`<!--]--></select></div><button class="px-2 py-1 text-xs bg-purple-100 text-purple-600 rounded hover:bg-purple-200 transition-colors cursor-pointer"> Today </button></div><div class="grid grid-cols-7 gap-1 mb-2"><!--[-->`);
          ssrRenderList(["S", "M", "T", "W", "T", "F", "S"], (day) => {
            _push(`<div class="h-6 flex items-center justify-center text-xs font-semibold text-gray-600">${ssrInterpolate(day)}</div>`);
          });
          _push(`<!--]--></div><div class="grid grid-cols-7 gap-1 mb-4"><!--[-->`);
          ssrRenderList(calendarDays.value, (day) => {
            _push(`<div class="${ssrRenderClass([getCalendarDayStyles(day), "h-8 flex items-center justify-center text-sm cursor-pointer rounded-full transition-all duration-200"])}">${ssrInterpolate(day.date)}</div>`);
          });
          _push(`<!--]--></div><div class="flex gap-2"><button class="flex-1 py-2 px-3 border border-gray-300 text-gray-700 rounded-md text-sm hover:bg-gray-50 transition-colors cursor-pointer"> Cancel </button><button${ssrIncludeBooleanAttr(!selectedCalendarDate.value) ? " disabled" : ""} class="${ssrRenderClass([selectedCalendarDate.value ? "bg-purple-600 hover:bg-purple-700" : "bg-gray-300 cursor-not-allowed", "flex-1 py-2 px-3 rounded-md text-sm text-white font-medium transition-colors cursor-pointer"])}"> Select </button></div></div></div>`);
        } else {
          _push(`<!---->`);
        }
        _push(`</div>`);
        _push(ssrRenderComponent(_sfc_main$2, {
          ref_key: "bookingSummaryRef",
          ref: bookingSummaryRef,
          salon: salon.value,
          "selected-services": servicesWithType.value,
          total: unref(bookingStore).total,
          "can-continue": canContinue.value,
          "show-professional-names": unref(bookingStore).professionalSelectionType === "per-service",
          "professional-names": unref(bookingStore).selectedProfessionals || {},
          "show-order-summary": false,
          onContinue: goToNextStep,
          onRemoveService: removeService
        }, null, _parent));
        _push(`</div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div>`);
    };
  }
});
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/booking/schedule.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};

export { _sfc_main as default };
//# sourceMappingURL=schedule-jkMdYAzx.mjs.map
