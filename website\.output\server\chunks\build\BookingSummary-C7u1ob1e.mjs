import { defineComponent, ref, computed, watch, mergeProps, useSSRContext } from 'vue';
import { ssrRenderAttrs, ssrRenderAttr, ssrInterpolate, ssrRenderList, ssrRenderClass, ssrIncludeBooleanAttr } from 'vue/server-renderer';
import { u as useBookingStore } from './booking-D2Du41fq.mjs';
import { c as useRuntimeConfig, i as useCookie } from './server.mjs';

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "BookingSummary",
  __ssrInlineRender: true,
  props: {
    salon: {},
    selectedServices: {},
    total: {},
    canContinue: { type: Boolean },
    showProfessionalNames: { type: Boolean },
    professionalNames: {},
    discounts: {},
    otherCharges: {},
    showOrderSummary: { type: Boolean },
    refreshTrigger: {}
  },
  emits: ["continue", "remove-service"],
  setup(__props, { expose: __expose, emit: __emit }) {
    __expose({
      fetchCartData,
      getCartId
    });
    const props = __props;
    useBookingStore();
    const { public: { BASE_URL } } = useRuntimeConfig();
    const cartServices = ref([]);
    const totalAmount = ref(0);
    const selectedDate = ref("");
    const selectedTime = ref("");
    const cartId = ref(null);
    const orderAmount = computed(() => {
      return cartServices.value.reduce((sum, item) => sum + item.price, 0);
    });
    const totalDurationMinutes = computed(() => {
      return cartServices.value.reduce((total, service) => {
        const duration = parseInt(service.duration) || 0;
        return total + duration;
      }, 0);
    });
    const formattedDuration = computed(() => {
      const totalMinutes = totalDurationMinutes.value;
      if (totalMinutes === 0) return "0 mins";
      const hours = Math.floor(totalMinutes / 60);
      const minutes = totalMinutes % 60;
      if (hours === 0) {
        return `${minutes} mins`;
      } else if (minutes === 0) {
        return hours === 1 ? "1 hour" : `${hours} hours`;
      } else {
        const hourText = hours === 1 ? "1 hour" : `${hours} hours`;
        return `${hourText} ${minutes} mins`;
      }
    });
    async function fetchCartData() {
      var _a, _b, _c;
      const guestId = useCookie("guestId").value;
      const authToken = useCookie("authToken").value;
      try {
        const headers = {
          "Content-Type": "application/json"
        };
        if (authToken) headers["token"] = authToken;
        if (guestId) headers["guestid"] = guestId;
        const response = await fetch(`${BASE_URL}/v1/client/cart/get-cart`, {
          method: "GET",
          headers
        });
        const data = await response.json();
        if (data.status && data.status_code === 200) {
          cartId.value = ((_a = data.data.cart) == null ? void 0 : _a._id) || null;
          const booking = (_b = data.data.cart) == null ? void 0 : _b.bookingId;
          totalAmount.value = ((_c = data.data.cart) == null ? void 0 : _c.totalamount) || 0;
          cartServices.value = (booking == null ? void 0 : booking.services.map((s) => {
            var _a2;
            const sub = s.businesssubserviceId || {};
            return {
              _id: s._id,
              name: sub.name,
              duration: sub.duration,
              price: s.totalDiscount && s.totalDiscount > 0 ? s.serviceAmountAfterDiscount : s.serviceAmount,
              staffName: ((_a2 = s.staffId) == null ? void 0 : _a2.name) || "Any Professional",
              serviceAmount: s.serviceAmount,
              serviceAmountAfterDiscount: s.serviceAmountAfterDiscount,
              totalDiscount: s.totalDiscount
            };
          })) || [];
          selectedTime.value = (booking == null ? void 0 : booking.bookingStartStrinng) || "";
          if (booking == null ? void 0 : booking.bookingDate) {
            const dateObj = new Date(booking.bookingDate);
            selectedDate.value = dateObj.toLocaleDateString("en-US", {
              weekday: "long",
              day: "numeric",
              month: "long"
            });
          }
        } else {
          throw new Error(data.msg || "Failed to fetch cart data");
        }
      } catch (error) {
        console.error("Error fetching cart data:", error);
        totalAmount.value = 0;
        cartServices.value = [];
        cartId.value = null;
      }
    }
    function getCartId() {
      return cartId.value;
    }
    watch(() => props.refreshTrigger, (newValue, oldValue) => {
      if (newValue !== oldValue && newValue !== void 0) {
        fetchCartData();
      }
    });
    return (_ctx, _push, _parent, _attrs) => {
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "w-full lg:flex-[0.4] xl:w-[400px] bg-white p-6 lg:p-8 xl:p-0 mt-0 xl:mt-20 xl:mb-30" }, _attrs))}><div class="bg-white rounded-2xl shadow-lg border border-gray-100 p-6 space-y-6"><div><p class="text-[16px] font-[500] dm-sanssemibold uppercase tracking-wide mb-3">SELECTED SALON</p><div class="flex items-center gap-3 p-3 rounded-lg bg-white border border-gray-100"><div class="w-12 h-12 rounded-lg bg-gray-200 flex items-center justify-center overflow-hidden flex-shrink-0"><img${ssrRenderAttr("src", _ctx.salon.image)}${ssrRenderAttr("alt", _ctx.salon.name)} class="w-full h-full object-cover"></div><div class="flex-1 min-w-0">`);
      if (_ctx.salon && _ctx.salon.name) {
        _push(`<h3 class="text-[18px] font-semibold text-gray-900 truncate">${ssrInterpolate(_ctx.salon.name)}</h3>`);
      } else {
        _push(`<!---->`);
      }
      _push(`<div class="flex items-center gap-1 text-sm text-gray-700 text-[14px]"><svg class="w-4 h-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.625-4.625a8 8 0 1111.314 0z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path></svg><span>${ssrInterpolate(_ctx.salon.location)}</span></div></div></div></div>`);
      if (selectedDate.value && selectedTime.value) {
        _push(`<div class="space-y-3 sm:space-y-0 sm:flex sm:items-center sm:gap-4"><div class="flex items-center gap-2"><svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 text-gray-900" viewBox="0 0 24 24"><path fill="currentColor" d="M5 22q-.825 0-1.412-.587T3 20V6q0-.825.588-1.412T5 4h1V2h2v2h8V2h2v2h1q.825 0 1.413.588T21 6v14q0 .825-.587 1.413T19 22zm0-2h14V10H5zM5 8h14V6H5zm0 0V6z"></path></svg><span class="text-sm font-medium text-gray-500">${ssrInterpolate(selectedDate.value)}</span></div><div class="flex items-center gap-2"><svg xmlns="http://www.w3.org/2000/svg" width="17" height="17" viewBox="0 0 24 24"><path fill="currentColor" d="M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2M12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8s8 3.58 8 8s-3.58 8-8 8"></path><path fill="currentColor" d="M12.5 7H11v6l5.25 3.15l.75-1.23l-4.5-2.67z"></path></svg><span class="text-sm font-medium text-gray-500">${ssrInterpolate(selectedTime.value)} (${ssrInterpolate(formattedDuration.value)})</span></div></div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`<div class="space-y-4">`);
      if (cartServices.value.length > 0) {
        _push(`<div class="space-y-4"><!--[-->`);
        ssrRenderList(cartServices.value, (item) => {
          _push(`<div class="flex justify-between items-start gap-3"><div class="flex-1 min-w-0"><h4 class="text-[16px] font-[500] mb-1 truncate dm-sanssemibold">${ssrInterpolate(item.name)}</h4><div class="flex items-center gap-1"><svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 24 24"><path fill="currentColor" d="M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2M12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8s8 3.58 8 8s-3.58 8-8 8"></path><path fill="currentColor" d="M12.5 7H11v6l5.25 3.15l.75-1.23l-4.5-2.67z"></path></svg><span class="text-[15px] text-gray-500 font-medium">${ssrInterpolate(item.duration)} mins</span>`);
          if (item.staffName) {
            _push(`<span class="text-black"> \u2022 <span class="text-[15px] text-[#7B27E7] font-[500] dm-sanssemibold">${ssrInterpolate(item.staffName)}</span></span>`);
          } else {
            _push(`<!---->`);
          }
          _push(`</div></div><div class="flex items-center gap-3 flex-shrink-0"><p class="text-[15px] inter-semibold font-[500]">`);
          if (item.totalDiscount && item.totalDiscount > 0) {
            _push(`<span><span class="text-gray-400 line-through mr-1 inter-semibold">\u20B9${ssrInterpolate(item.serviceAmount)}</span><span class="text-green-600 inter-semibold">\u20B9${ssrInterpolate(item.serviceAmountAfterDiscount)}</span></span>`);
          } else {
            _push(`<span class="inter-semibold"> \u20B9${ssrInterpolate(item.serviceAmount)}</span>`);
          }
          _push(`</p><button class="w-4 h-4 rounded-full bg-gray-700 flex items-center justify-center hover:bg-gray-800 transition-colors cursor-pointer"><svg class="w-3.5 h-3.5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12"></path></svg></button></div></div>`);
        });
        _push(`<!--]--></div>`);
      } else {
        _push(`<p class="text-sm text-gray-600">No services or add-ons selected.</p>`);
      }
      _push(`</div><div class="border-t border-gray-200 pt-6">`);
      if (_ctx.showOrderSummary) {
        _push(`<div><h4 class="text-base font-semibold text-gray-900 mb-4">Order Summary</h4><div class="space-y-3 bg-[rgba(245,249,254,1)] py-3 sm:py-3 px-3 sm:px-6"><div class="flex justify-between items-center text-[15px]"><span class="text-gray-600">Order Amount</span><span class="font-[500] inter-semibold text-gray-900">\u20B9${ssrInterpolate(orderAmount.value.toFixed(2))}</span></div>`);
        if (_ctx.otherCharges) {
          _push(`<div class="flex justify-between items-center text-[15px]"><span class="text-gray-600">Other Charges</span><span class="font-[500] inter-semibold text-gray-900">\u20B9${ssrInterpolate(_ctx.otherCharges)}</span></div>`);
        } else {
          _push(`<!---->`);
        }
        _push(`<!--[-->`);
        ssrRenderList(_ctx.discounts, (discount) => {
          _push(`<div class="flex justify-between items-center text-[15px]"><span class="text-gray-600">${ssrInterpolate(discount.name)}</span><span class="font-500 text-green-600 inter-semibold">-\u20B9${ssrInterpolate(discount.amount.toFixed(2))}</span></div>`);
        });
        _push(`<!--]--><div class="flex justify-between items-center"><span class="text-[18px] dm-sanssemibold font-[500] text-gray-900">Total</span><span class="text-[18px] inter-semibold font-[500] text-gray-900">\u20B9${ssrInterpolate(totalAmount.value || 0)}</span></div></div></div>`);
      } else {
        _push(`<div class="flex justify-between items-center"><span class="text-[18px] dm-sanssemibold font-[500] text-gray-900">Total</span><span class="text-[18px] inter-semibold font-[500] text-gray-900">\u20B9${ssrInterpolate(totalAmount.value || 0)}</span></div>`);
      }
      _push(`</div><button${ssrIncludeBooleanAttr(!_ctx.canContinue) ? " disabled" : ""} class="${ssrRenderClass([_ctx.canContinue ? "bg-purple-600 hover:bg-purple-700" : "bg-gray-300 cursor-not-allowed", "w-full py-4 rounded-full text-white font-medium text-base transition-colors cursor-pointer"])}"> Continue </button></div></div>`);
    };
  }
});
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("components/BookingSummary.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};

export { _sfc_main as _ };
//# sourceMappingURL=BookingSummary-C7u1ob1e.mjs.map
