import { defineComponent, computed, ref, mergeProps, unref, useSSRContext } from 'vue';
import { ssrRenderAttrs, ssrRenderComponent, ssrRenderClass, ssrRenderList, ssrInterpolate, ssrRenderAttr, ssrRenderStyle } from 'vue/server-renderer';
import { u as useBookingStore } from './booking-D2Du41fq.mjs';
import { useRoute, useRouter } from 'vue-router';
import { u as useAppSeoMeta } from './useAppSeoMeta-HWukB6FN.mjs';
import { _ as _sfc_main$2 } from './BookingSummary-C7u1ob1e.mjs';
import { c as useRuntimeConfig, d as useState, L as Loader, S as Snackbar, e as _sfc_main$1, i as useCookie } from './server.mjs';
import 'pinia';
import '../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:url';
import '@iconify/utils';
import 'node:crypto';
import 'consola';
import 'node:path';
import 'better-sqlite3';
import 'ipx';
import 'cookie';
import '@iconify/vue';
import 'tailwindcss/colors';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import 'unhead/server';
import 'devalue';
import 'unhead/plugins';
import 'unhead/utils';

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "professionals",
  __ssrInlineRender: true,
  setup(__props) {
    useAppSeoMeta({
      title: computed(() => `Select Professional | Bookslotz`),
      meta: [
        {
          name: "description",
          content: computed(() => `Choose your preferred professional for your salon services at ${salon.value.name}. Select from our experienced stylists and specialists.`)
        },
        {
          name: "keywords",
          content: "salon professional, hair stylist, beauty specialist"
        },
        {
          name: "robots",
          content: "index, follow"
        },
        {
          property: "og:title",
          content: computed(() => `Select Your Professional | ${salon.value.name}`)
        },
        {
          property: "og:description",
          content: computed(() => `Choose from our experienced professionals for your salon appointment at ${salon.value.name}.`)
        },
        {
          property: "og:type",
          content: "website"
        },
        {
          property: "og:url",
          content: computed(() => `https://www.Bookslotz.com${route.fullPath}`)
        },
        {
          property: "og:image",
          content: computed(() => salon.value.image)
        },
        {
          name: "twitter:card",
          content: "summary_large_image"
        },
        {
          name: "twitter:title",
          content: computed(() => `Select Professional at ${salon.value.name}`)
        },
        {
          name: "twitter:description",
          content: computed(() => `Choose your preferred professional for your salon services at ${salon.value.name}.`)
        },
        {
          name: "twitter:image",
          content: computed(() => salon.value.image)
        }
      ],
      link: [
        {
          rel: "canonical",
          href: computed(() => `https://www.Bookslotz.com${route.fullPath}`)
        }
      ]
    });
    const bookingStore = useBookingStore();
    const router = useRouter();
    const route = useRoute();
    const { public: { BASE_URL } } = useRuntimeConfig();
    const bookingSummaryRef = ref(null);
    const isLoading = useState("globalLoader", () => false);
    const hasError = ref(false);
    const snackbarRef = ref(null);
    const refreshTrigger = ref(0);
    const selectedProfessionalType = ref("any");
    const selectedProfessionalsPerService = ref({});
    const showPerServiceDetails = ref(false);
    const showProfessionalModal = ref(false);
    const currentServiceId = ref(null);
    const salonSlug = computed(() => decodeURIComponent(route.query.slug || ""));
    const serviceProfessionals = ref([]);
    const cartData = ref(null);
    const serviceStaffData = ref({});
    const salon = ref({
      name: "Loading...",
      location: "MG Road, Mumbai",
      image: "/images/saloon-small-image.png"
    });
    const servicesWithType = computed(() => {
      return bookingStore.selectedServices.map((service) => ({
        ...service,
        type: "service"
      }));
    });
    const filteredProfessionals = computed(() => {
      const result = {};
      for (const [serviceId, professional] of Object.entries(selectedProfessionalsPerService.value)) {
        if (professional !== "any" && professional.name) {
          result[serviceId] = { name: professional.name };
        }
      }
      return result;
    });
    const canContinue = computed(() => {
      return bookingStore.isReadyForNextStep;
    });
    function getSelectedStaffForService(serviceId) {
      return serviceStaffData.value[serviceId] || null;
    }
    function getSelectedStaffName(serviceId) {
      const staff = getSelectedStaffForService(serviceId);
      return (staff == null ? void 0 : staff.name) || "Any Professional";
    }
    function getSelectedStaffImage(serviceId) {
      var _a;
      const staff = getSelectedStaffForService(serviceId);
      if ((_a = staff == null ? void 0 : staff.loginId) == null ? void 0 : _a.profileImage) {
        return staff.loginId.profileImage.startsWith("http") ? staff.loginId.profileImage : `${BASE_URL}/wp/${staff.loginId.profileImage}`;
      }
      return "/images/expert1.png";
    }
    async function fetchCartData() {
      var _a, _b, _c, _d, _e, _f, _g;
      isLoading.value = true;
      const authToken = useCookie("authToken");
      const guestId = useCookie("guestId");
      try {
        const headers = {
          "Content-Type": "application/json"
        };
        if (authToken.value) headers["token"] = authToken.value;
        if (guestId.value) headers["guestid"] = guestId.value;
        const response = await fetch(`${BASE_URL}/v1/client/cart/get-cart`, {
          method: "GET",
          headers
        });
        const data = await response.json();
        if (data.status_code === 401) {
          (_a = snackbarRef.value) == null ? void 0 : _a.showSnackbar(data.msg || "Unauthorized. Logging out...");
          return;
        }
        if (data.status_code === 400) {
          (_b = snackbarRef.value) == null ? void 0 : _b.showSnackbar(data.msg || "Error fetching data");
          return;
        }
        if (data.status && data.status_code === 200) {
          cartData.value = data.data.cart;
          const services = ((_e = (_d = (_c = data.data) == null ? void 0 : _c.cart) == null ? void 0 : _d.bookingId) == null ? void 0 : _e.services) || [];
          bookingStore.resetServices();
          serviceStaffData.value = {};
          selectedProfessionalsPerService.value = {};
          let hasStaffAssigned = false;
          services.forEach((s) => {
            const subService = s.businesssubserviceId;
            if (subService) {
              bookingStore.addService({
                id: subService._id,
                name: subService.name,
                duration: subService.duration,
                price: subService.price,
                category: "General"
              });
              if (s.staffId) {
                hasStaffAssigned = true;
                serviceStaffData.value[subService._id] = {
                  _id: s.staffId._id,
                  name: s.staffId.name,
                  image: s.staffId.image
                };
                selectedProfessionalsPerService.value[subService._id] = {
                  _id: s.staffId._id,
                  name: s.staffId.name,
                  image: s.staffId.image
                };
              }
            }
          });
          if (hasStaffAssigned) {
            selectedProfessionalType.value = "per-service";
            showPerServiceDetails.value = true;
            bookingStore.setProfessionalSelection("per-service", selectedProfessionalsPerService.value);
          } else {
            selectedProfessionalType.value = "any";
            showPerServiceDetails.value = false;
            bookingStore.setProfessionalSelection("any", null);
          }
          refreshTrigger.value += 1;
          await ((_f = bookingSummaryRef.value) == null ? void 0 : _f.fetchCartData());
        } else {
          throw new _sfc_main$1(data.msg || "Failed to load cart data");
        }
      } catch (error) {
        console.error("Error fetching cart data:", error);
        (_g = snackbarRef.value) == null ? void 0 : _g.showSnackbar("Failed to load cart data.", 3e3);
        hasError.value = true;
      } finally {
        isLoading.value = false;
      }
    }
    function isSelectedInModal(professional) {
      if (!currentServiceId.value) return false;
      const selected = selectedProfessionalsPerService.value[currentServiceId.value];
      if (professional === "any") {
        return selected === "any";
      }
      return selected && selected._id === professional._id;
    }
    function getModalSelectionClass(professional) {
      return isSelectedInModal(professional) ? "border-purple-600 bg-purple-50" : "border-gray-200 hover:border-gray-300";
    }
    function handleRemoveService(serviceId) {
      bookingStore.removeService(Number(serviceId));
      if (selectedProfessionalsPerService.value[serviceId]) {
        delete selectedProfessionalsPerService.value[serviceId];
      }
      fetchCartData();
    }
    function getSelectedProfessionalText(serviceId) {
      const selected = selectedProfessionalsPerService.value[serviceId];
      if (!selected || selected === "any") {
        return "Any Professional";
      }
      return selected.name;
    }
    function goToNextStep() {
      if (bookingStore.isReadyForNextStep) {
        if (selectedProfessionalType.value === "any") {
          bookingStore.setProfessionalSelection("any", null);
        }
        router.push({ path: "/booking/schedule", query: { slug: salonSlug.value } });
      }
    }
    return (_ctx, _push, _parent, _attrs) => {
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "min-h-screen bg-white relative" }, _attrs))}>`);
      if (unref(isLoading)) {
        _push(ssrRenderComponent(Loader, null, null, _parent));
      } else {
        _push(`<!---->`);
      }
      _push(ssrRenderComponent(Snackbar, {
        ref_key: "snackbarRef",
        ref: snackbarRef
      }, null, _parent));
      if (hasError.value) {
        _push(ssrRenderComponent(_sfc_main$1, { error: { statusCode: 500, message: "Something went wrong. Please try again later." } }, null, _parent));
      } else {
        _push(`<!---->`);
      }
      if (!hasError.value) {
        _push(`<div class="flex flex-col xl:flex-row max-w-8xl mx-auto px-1 sm:px-16 md:px-12 justify-between"><div class="flex-1 lg:flex-[0.6] px-6 lg:px-8 py-6 lg:py-8"><div class="flex items-center space-x-3 mb-6"><button class="w-10 h-10 rounded-full border border-gray-300 flex items-center justify-center group cursor-pointer"><svg xmlns="http://www.w3.org/2000/svg" class="w-7 h-7 text-purple-600 group-hover:text-purple-700 transition-colors" viewBox="0 0 24 24" fill="currentColor"><path d="m6.523 12.5l3.735 3.735q.146.146.153.344q.006.198-.153.363q-.166.166-.357.168t-.357-.162l-4.382-4.383q-.243-.242-.243-.565t.243-.566l4.382-4.382q.147-.146.347-.153q.201-.007.367.159q.16.165.162.353q.003.189-.162.354L6.523 11.5h12.38q.214 0 .358.143t.143.357t-.143.357t-.357.143z"></path></svg></button><h1 class="text-[24px] font-semibold text-[700]">Select Professional</h1></div>`);
        if (!showPerServiceDetails.value) {
          _push(`<div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8"><div class="${ssrRenderClass([selectedProfessionalType.value === "any" ? "border-purple-600 bg-white" : "border-gray-200 hover:border-gray-300", "border-2 rounded-xl p-6 cursor-pointer transition-all duration-200 hover:shadow-md"])}"><div class="flex flex-col items-center text-center space-y-3"><div class="w-16 h-16 rounded-full flex items-center justify-center bg-[#7B27E745]"><svg xmlns="http://www.w3.org/2000/svg" class="${ssrRenderClass([selectedProfessionalType.value === "any" ? "text-purple-600" : "text-black", "w-8 h-8"])}" viewBox="0 0 24 24" fill="currentColor"><path d="M12 11.385q-1.237 0-2.119-.882T9 8.385t.881-2.12T12 5.386t2.119.88t.881 2.12t-.881 2.118t-2.119.882m-7 7.23V16.97q0-.619.36-1.158q.361-.54.97-.838q1.416-.679 2.834-1.018q1.417-.34 2.836-.34t2.837.34t2.832 1.018q.61.298.97.838q.361.539.361 1.158v1.646zm1-1h12v-.646q0-.332-.215-.625q-.214-.292-.593-.494q-1.234-.598-2.546-.916T12 14.616t-2.646.318t-2.546.916q-.38.202-.593.494Q6 16.637 6 16.97zm6-7.23q.825 0 1.413-.588T14 8.384t-.587-1.412T12 6.384t-1.412.588T10 8.384t.588 1.413t1.412.587m0 7.232"></path></svg></div><div><h3 class="text-[16px] font-semibold mb-1">Select any professional</h3><p class="text-[14px] text-gray-500">We&#39;ll assign the best available professional</p></div></div></div><div class="${ssrRenderClass([selectedProfessionalType.value === "per-service" ? "border-purple-600 bg-white" : "border-gray-200 hover:border-gray-300", "border-2 rounded-xl p-6 cursor-pointer transition-all duration-200 hover:shadow-md"])}"><div class="flex flex-col items-center text-center space-y-3"><div class="${ssrRenderClass([selectedProfessionalType.value === "per-service" ? "bg-purple-100" : "bg-[#7B27E745]", "w-16 h-16 rounded-full flex items-center justify-center"])}"><svg xmlns="http://www.w3.org/2000/svg" class="${ssrRenderClass([selectedProfessionalType.value === "per-service" ? "text-purple-600" : "text-black", "w-8 h-8"])}" viewBox="0 0 24 24" fill="currentColor"><path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4zm6-6h2v2h2v2h-2v2h-2v-2h-2v-2h2v-2z"></path></svg></div><div><h3 class="text-[16px] font-semibold mb-1">Select professional per service</h3><p class="text-[14px] text-gray-500">Choose specific professionals for each service</p></div></div></div></div>`);
        } else {
          _push(`<!---->`);
        }
        if (showPerServiceDetails.value) {
          _push(`<div class="space-y-6"><!--[-->`);
          ssrRenderList(unref(bookingStore).selectedServices, (service) => {
            _push(`<div class="bg-white border border-gray-200 rounded-lg p-4"><div class="flex items-center justify-between"><div class="flex-1"><h3 class="text-[18px] font-semibold mb-1">${ssrInterpolate(service.name)}</h3><div class="flex items-center gap-1 text-[16px] text-gray-500"><svg class="w-3 h-3 text-black flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24"><circle cx="12" cy="12" r="10"></circle><polyline points="12,6 12,12 16,14"></polyline></svg><span>${ssrInterpolate(service.duration)} mins</span></div></div><div class="relative"><button class="flex items-center gap-3 px-4 py-2 border border-gray-300 rounded-full bg-white hover:bg-gray-50 transition-colors cursor-pointer">`);
            if (getSelectedStaffForService(service.id)) {
              _push(`<div class="w-6 h-6 rounded-full overflow-hidden bg-gray-200 flex-shrink-0"><img${ssrRenderAttr("src", getSelectedStaffImage(service.id))}${ssrRenderAttr("alt", getSelectedStaffName(service.id))} class="w-full h-full object-cover"></div>`);
            } else {
              _push(`<div class="w-6 h-6 rounded-full flex items-center justify-center" style="${ssrRenderStyle({ "background": "rgba(123, 39, 231, 0.27)" })}"><svg class="w-4 h-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M12 6c1.1 0 2 .9 2 2s-.9 2-2 2s-2-.9-2-2s.9-2 2-2m0 10c2.7 0 5.8 1.29 6 2H6c.23-.72 3.31-2 6-2m0-12C9.79 4 8 5.79 8 8s1.79 4 4 4s4-1.79 4-4s-1.79-4-4-4m0 10c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4"></path></svg></div>`);
            }
            _push(`<span class="text-[16px] font-semibold">${ssrInterpolate(getSelectedProfessionalText(service.id))}</span><svg class="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path></svg></button></div></div></div>`);
          });
          _push(`<!--]--></div>`);
        } else {
          _push(`<!---->`);
        }
        _push(`</div>`);
        _push(ssrRenderComponent(_sfc_main$2, {
          ref_key: "bookingSummaryRef",
          ref: bookingSummaryRef,
          salon: salon.value,
          "selected-services": servicesWithType.value,
          total: unref(bookingStore).total,
          "can-continue": canContinue.value,
          "show-professional-names": selectedProfessionalType.value === "per-service",
          "professional-names": filteredProfessionals.value,
          "show-order-summary": false,
          "refresh-trigger": refreshTrigger.value,
          onContinue: goToNextStep,
          onRemoveService: handleRemoveService
        }, null, _parent));
        _push(`</div>`);
      } else {
        _push(`<!---->`);
      }
      if (showProfessionalModal.value) {
        _push(`<div class="fixed inset-0 bg-black/50 bg-opacity-60 z-80 flex items-center justify-center p-4"><div class="bg-white rounded-2xl w-full max-w-md mx-auto"><div class="flex items-center justify-between p-6 border-b border-gray-100"><h2 class="text-lg font-semibold">Select Professional</h2><button class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center hover:bg-gray-200 transition-colors"><svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg></button></div><div class="p-6 relative min-h-[200px] max-h-[70vh] overflow-y-auto">`);
        if (unref(isLoading)) {
          _push(ssrRenderComponent(Loader, { class: "absolute inset-0 flex items-center justify-center" }, null, _parent));
        } else {
          _push(`<div class="grid grid-cols-2 gap-4"><div class="${ssrRenderClass([getModalSelectionClass("any"), "border-2 rounded-xl p-4 cursor-pointer transition-all duration-200 hover:shadow-sm text-center"])}"><div class="${ssrRenderClass([isSelectedInModal("any") ? "bg-purple-100" : "bg-gray-100", "w-16 h-16 rounded-full mx-auto mb-3 flex items-center justify-center"])}"><svg class="${ssrRenderClass([isSelectedInModal("any") ? "text-purple-600" : "text-gray-400", "w-8 h-8"])}" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path></svg></div><h3 class="text-[16px] font-semibold mb-1">Select any</h3><p class="text-[16px] font-semibold">professional</p></div><!--[-->`);
          ssrRenderList(serviceProfessionals.value, (professional) => {
            _push(`<div class="${ssrRenderClass([getModalSelectionClass(professional), "border-2 rounded-xl p-4 cursor-pointer transition-all duration-200 hover:shadow-sm text-center"])}"><div class="w-16 h-16 rounded-full mx-auto mb-3 overflow-hidden bg-gray-200"><img${ssrRenderAttr("src", professional.image)}${ssrRenderAttr("alt", professional.name)} class="w-full h-full object-cover"></div><h3 class="text-sm font-semibold mb-1">${ssrInterpolate(professional.name)}</h3><p class="text-xs text-gray-500 mb-2">${ssrInterpolate(professional.specialty)}</p><div class="flex items-center justify-center gap-1"><svg class="w-3 h-3 text-yellow-400 fill-current" viewBox="0 0 20 20"><path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"></path></svg><span class="text-xs font-medium">${ssrInterpolate(professional.rating)} (${ssrInterpolate(professional.reviews)})</span></div></div>`);
          });
          _push(`<!--]--></div>`);
        }
        _push(`</div></div></div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div>`);
    };
  }
});
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/booking/professionals.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};

export { _sfc_main as default };
//# sourceMappingURL=professionals-D-5gbCMI.mjs.map
