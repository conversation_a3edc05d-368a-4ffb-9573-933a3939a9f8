const index_vue_vue_type_style_index_0_scoped_43160719_lang = '.sidebar-text[data-v-43160719]{font-size:20px;font-weight:500}nav a.bg-purple-100[data-v-43160719]:before{background-color:#9333ea;border-bottom-left-radius:.5rem;border-top-left-radius:.5rem;bottom:0;content:"";left:0;position:absolute;top:0;width:6px}nav a svg[data-v-43160719]{color:inherit}';

export { index_vue_vue_type_style_index_0_scoped_43160719_lang as i };
//# sourceMappingURL=index-styles-1.mjs-D3vHSUxq.mjs.map
