import { defineComponent, mergeProps, useSSRContext } from 'vue';
import { ssrRenderAttrs, ssrInterpolate } from 'vue/server-renderer';

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "Modal",
  __ssrInlineRender: true,
  props: {
    isOpen: { type: Boolean },
    title: {},
    message: {}
  },
  emits: ["cancel", "confirm"],
  setup(__props) {
    return (_ctx, _push, _parent, _attrs) => {
      if (_ctx.isOpen) {
        _push(`<div${ssrRenderAttrs(mergeProps({ class: "fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4" }, _attrs))}><div class="bg-white rounded-3xl shadow-xl max-w-sm w-full mx-4 p-8 text-center"><div class="flex items-center justify-center w-20 h-20 mx-auto mb-6 bg-purple-100 rounded-full"><div class="flex items-center justify-center w-12 h-12 bg-purple-600 rounded-xl"><svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l-1 12H6L5 9z"></path></svg><div class="absolute w-3 h-3 bg-white rounded-full flex items-center justify-center -mt-2 ml-4"><span class="text-purple-600 text-xs font-bold">!</span></div></div></div><h2 class="text-2xl font-bold text-gray-900 mb-4">${ssrInterpolate(_ctx.title)}</h2><div class="mb-6"><p class="text-gray-700 mb-2">${ssrInterpolate(_ctx.message)}</p><p class="text-gray-600">Continuing will replace it</p></div><div class="flex gap-4"><button class="flex-1 px-6 py-3 rounded-full border-0 bg-transparent text-purple-600 hover:bg-purple-50 transition-colors duration-200 font-semibold text-lg cursor-pointer"> Cancel </button><button class="flex-1 px-6 py-3 rounded-full bg-purple-600 text-white hover:bg-purple-700 transition-colors duration-200 font-semibold text-lg shadow-lg cursor-pointer"> Replace </button></div></div></div>`);
      } else {
        _push(`<!---->`);
      }
    };
  }
});
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("components/Modal.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};

export { _sfc_main as _ };
//# sourceMappingURL=Modal-BOTd-eEZ.mjs.map
