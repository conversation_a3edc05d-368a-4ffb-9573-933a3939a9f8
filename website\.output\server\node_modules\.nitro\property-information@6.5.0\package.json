{"name": "property-information", "version": "6.5.0", "description": "Info on the properties and attributes of the web platform", "license": "MIT", "keywords": ["html", "svg", "aria", "property", "attribute", "information", "info"], "repository": "wooorm/property-information", "bugs": "https://github.com/wooorm/property-information/issues", "funding": {"type": "github", "url": "https://github.com/sponsors/wooorm"}, "author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)", "<PERSON> <<EMAIL>> (http://starptech.de)", "<PERSON> <<EMAIL>>"], "sideEffects": false, "type": "module", "main": "index.js", "types": "index.d.ts", "files": ["lib/", "index.d.ts", "index.js"], "devDependencies": {"@types/mdast": "^4.0.0", "@types/node": "^20.0.0", "alpha-sort": "^5.0.0", "c8": "^9.0.0", "html-element-attributes": "^3.0.0", "html-event-attributes": "^2.0.0", "mdast-zone": "^6.0.0", "prettier": "^3.0.0", "remark-cli": "^11.0.0", "remark-preset-wooorm": "^9.0.0", "svg-element-attributes": "^2.0.0", "svg-event-attributes": "^2.0.0", "type-coverage": "^2.0.0", "typescript": "^5.0.0", "undici": "^6.0.0", "unist-builder": "^4.0.0", "xo": "^0.58.0"}, "scripts": {"prepack": "npm run build && npm run format", "generate": "node --conditions development script/generate-react.js && node --conditions development script/generate-exceptions.js", "build": "tsc --build --clean && tsc --build && type-coverage", "format": "remark . -qfo && prettier . -w --log-level warn && xo --fix", "test-api": "node --conditions development test.js", "test-coverage": "c8 --check-coverage --100 --reporter lcov npm run test-api", "test": "npm run generate && npm run build && npm run format && npm run test-coverage"}, "prettier": {"tabWidth": 2, "useTabs": false, "singleQuote": true, "bracketSpacing": false, "semi": false, "trailingComma": "none"}, "xo": {"prettier": true, "rules": {"logical-assignment-operators": "off", "no-bitwise": "off", "unicorn/prefer-string-replace-all": "off", "unicorn/prevent-abbreviations": "off"}}, "remarkConfig": {"plugins": ["preset-wooorm", "./script/list.js"]}, "typeCoverage": {"atLeast": 100, "detail": true, "strict": true, "ignoreCatch": true}}