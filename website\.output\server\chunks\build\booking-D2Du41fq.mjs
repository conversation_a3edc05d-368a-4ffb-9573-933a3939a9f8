import { defineStore } from 'pinia';

const useBookingStore = defineStore("booking", {
  state: () => ({
    selectedServices: [],
    professionalSelectionType: null,
    selectedProfessionals: null,
    schedule: null,
    selectedAddons: [],
    selectedMembership: null,
    bookingType: "service"
  }),
  getters: {
    total(state) {
      if (state.bookingType === "membership" && state.selectedMembership) {
        return state.selectedMembership.price;
      }
      if (state.bookingType === "giftcard" && state.selectedMembership) {
        return state.selectedMembership.price;
      }
      const servicesTotal = state.selectedServices.reduce((sum, s) => sum + s.price, 0);
      const addonsTotal = state.selectedAddons.reduce((sum, a) => sum + a.price, 0);
      return servicesTotal + addonsTotal;
    },
    hasProfessionalSelection(state) {
      return state.professionalSelectionType !== null;
    },
    isReadyForNextStep(state) {
      if (state.bookingType === "membership") {
        return state.selectedMembership !== null;
      }
      if (state.bookingType === "giftcard") {
        return state.selectedMembership !== null;
      }
      if (!state.selectedServices.length) return false;
      if (!state.professionalSelectionType) return false;
      if (state.professionalSelectionType === "any") return true;
      return state.selectedServices.length > 0;
    }
  },
  actions: {
    addService(service) {
      if (!this.selectedServices.find((s) => s.id === service.id)) {
        this.selectedServices.push(service);
      }
      this.bookingType = "service";
    },
    removeService(id) {
      this.selectedServices = this.selectedServices.filter((s) => s.id !== id);
      if (this.selectedProfessionals && this.selectedProfessionals[id]) {
        delete this.selectedProfessionals[id];
      }
    },
    resetServices() {
      this.selectedServices = [];
      this.resetProfessionalSelection();
    },
    setProfessionalSelection(type, professionals = null) {
      this.professionalSelectionType = type;
      this.selectedProfessionals = professionals ? { ...professionals } : {};
    },
    setProfessionalForService(serviceId, professional) {
      if (!this.selectedProfessionals) {
        this.selectedProfessionals = {};
      }
      this.selectedProfessionals[serviceId] = professional;
    },
    removeProfessionalForService(serviceId) {
      if (this.selectedProfessionals && this.selectedProfessionals[serviceId]) {
        delete this.selectedProfessionals[serviceId];
      }
    },
    resetProfessionalSelection() {
      this.professionalSelectionType = null;
      this.selectedProfessionals = null;
    },
    setSchedule(schedule) {
      this.schedule = schedule;
    },
    setSelectedAddons(addons) {
      this.selectedAddons = addons;
    },
    setMembership(membership) {
      this.selectedMembership = membership;
      this.bookingType = "membership";
      this.resetServices();
      this.selectedAddons = [];
    },
    setGiftCard(giftCard) {
      this.selectedMembership = giftCard;
      this.bookingType = "giftcard";
      this.resetServices();
      this.selectedAddons = [];
    },
    removeMembership() {
      this.selectedMembership = null;
      this.bookingType = "service";
    },
    removeGiftCard() {
      this.selectedMembership = null;
      this.bookingType = "service";
    },
    resetBooking() {
      this.selectedServices = [];
      this.resetProfessionalSelection();
      this.schedule = null;
      this.selectedAddons = [];
      this.selectedMembership = null;
      this.bookingType = "service";
    },
    switchToServiceBooking() {
      this.bookingType = "service";
      this.selectedMembership = null;
    },
    switchToMembershipBooking() {
      this.bookingType = "membership";
      this.resetServices();
      this.selectedAddons = [];
    },
    switchToGiftCardPurchase() {
      this.bookingType = "giftcard";
      this.resetServices();
      this.selectedAddons = [];
    }
  }
});

export { useBookingStore as u };
//# sourceMappingURL=booking-D2Du41fq.mjs.map
