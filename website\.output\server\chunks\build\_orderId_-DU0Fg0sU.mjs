import { defineComponent, ref, mergeProps, unref, useSSRContext } from 'vue';
import { ssrRenderAttrs, ssrRenderList, ssrInterpolate, ssrRenderAttr } from 'vue/server-renderer';
import { _ as _export_sfc, g as useHead } from './server.mjs';
import '../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:url';
import '@iconify/utils';
import 'node:crypto';
import 'consola';
import 'node:path';
import 'better-sqlite3';
import 'ipx';
import 'pinia';
import 'vue-router';
import 'cookie';
import '@iconify/vue';
import 'tailwindcss/colors';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import 'unhead/server';
import 'devalue';
import 'unhead/plugins';
import 'unhead/utils';

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "[orderId]",
  __ssrInlineRender: true,
  setup(__props) {
    const overallRating = ref(4);
    const reviewText = ref("");
    const experienceCategories = [
      { key: "cleanliness", label: "Cleanliness" },
      { key: "serviceQuality", label: "Service Quality" },
      { key: "staffBehavior", label: "Staff Behavior" },
      { key: "valueForMoney", label: "Value for Money" }
    ];
    const services = [
      {
        type: "Haircut by",
        name: "Wade Warren",
        avatar: "/images/expert1.png"
      },
      {
        type: "Haircut by",
        name: "Wade Warren",
        avatar: "/images/expert2.png"
      }
    ];
    ref({
      cleanliness: 2,
      serviceQuality: 2,
      staffBehavior: 2,
      valueForMoney: 2
    });
    const serviceRatings = ref([2, 2]);
    useHead({
      title: "Rate Your Experience - Salon Review",
      meta: [
        { name: "description", content: "Rate and review your salon experience" },
        { name: "viewport", content: "width=device-width, initial-scale=1" }
      ]
    });
    return (_ctx, _push, _parent, _attrs) => {
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "max-w-3xl mx-auto p-4 sm:p-6 bg-white mt-3 sm:mt-5" }, _attrs))} data-v-7d319dea><h1 class="text-xl sm:text-[25px] font-bold text-[700] text-left mb-4 sm:mb-0" data-v-7d319dea> Rate Your Experience </h1><div class="border border-gray-200 rounded-lg p-4 sm:p-6 space-y-6 sm:space-y-8" data-v-7d319dea><div class="text-center" data-v-7d319dea><h2 class="text-lg sm:text-[20px] font-semibold text-[700] mb-3 sm:mb-2" data-v-7d319dea>Rate Saloon Name</h2><div class="flex justify-center space-x-1 sm:space-x-2" data-v-7d319dea><!--[-->`);
      ssrRenderList(5, (star) => {
        _push(`<button class="transition-colors duration-200 cursor-pointer hover:scale-110" data-v-7d319dea>`);
        if (star <= unref(overallRating)) {
          _push(`<svg class="w-6 h-6 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" data-v-7d319dea><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" data-v-7d319dea></path></svg>`);
        } else {
          _push(`<svg class="w-6 h-6 text-gray-300" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" data-v-7d319dea><polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21 12 17.77 5.82 21 7 14.14 2 9.27 8.91 8.26 12 2" data-v-7d319dea></polygon></svg>`);
        }
        _push(`</button>`);
      });
      _push(`<!--]--></div></div><div class="border border-gray-200 rounded-lg p-3 sm:p-4" data-v-7d319dea><h3 class="text-base sm:text-[18px] font-semibold mb-3 sm:mb-4" data-v-7d319dea>Salon Experience Rating</h3><div class="space-y-3 sm:space-y-4" data-v-7d319dea><!--[-->`);
      ssrRenderList(experienceCategories, (category) => {
        _push(`<div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0" data-v-7d319dea><span class="font-medium text-sm sm:text-[16px]" data-v-7d319dea>${ssrInterpolate(category.label)}</span><div class="flex space-x-1 justify-center sm:justify-end" data-v-7d319dea><!--[-->`);
        ssrRenderList(5, (star) => {
          _push(`<button class="transition-colors duration-200 hover:scale-110 p-1" data-v-7d319dea>`);
          if (star <= unref(overallRating)) {
            _push(`<svg class="w-6 h-6 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" data-v-7d319dea><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" data-v-7d319dea></path></svg>`);
          } else {
            _push(`<svg class="w-6 h-6 text-gray-300" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" data-v-7d319dea><polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21 12 17.77 5.82 21 7 14.14 2 9.27 8.91 8.26 12 2" data-v-7d319dea></polygon></svg>`);
          }
          _push(`</button>`);
        });
        _push(`<!--]--></div></div>`);
      });
      _push(`<!--]--></div></div><div class="border border-gray-200 rounded-lg p-3 sm:p-4" data-v-7d319dea><h3 class="text-base sm:text-[18px] font-semibold mb-3 sm:mb-4" data-v-7d319dea>Rate Services &amp; Staff</h3><div class="space-y-4 sm:space-y-4" data-v-7d319dea><!--[-->`);
      ssrRenderList(services, (service, index) => {
        _push(`<div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0" data-v-7d319dea><div class="flex items-center space-x-3" data-v-7d319dea><div class="flex flex-col items-center" data-v-7d319dea><p class="text-md sm:text-[16px] medium mb-1 text-center" data-v-7d319dea>${ssrInterpolate(service.type)}</p><img${ssrRenderAttr("src", service.avatar)}${ssrRenderAttr("alt", service.name)} class="w-8 h-8 sm:w-10 sm:h-10 rounded-full bg-gray-300 flex-shrink-0" data-v-7d319dea></div><div class="flex items-center h-full mt-5" data-v-7d319dea><p class="text-sm sm:text-base font-medium text-gray-900" data-v-7d319dea>${ssrInterpolate(service.name)}</p></div></div><div class="flex space-x-1 justify-center sm:justify-end sm:mt-5" data-v-7d319dea><!--[-->`);
        ssrRenderList(5, (star) => {
          _push(`<button class="transition-colors duration-200 hover:scale-110 p-1" data-v-7d319dea>`);
          if (star <= unref(serviceRatings)[index]) {
            _push(`<svg class="w-6 h-6 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" data-v-7d319dea><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" data-v-7d319dea></path></svg>`);
          } else {
            _push(`<svg class="w-6 h-6 text-gray-300" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" data-v-7d319dea><polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21 12 17.77 5.82 21 7 14.14 2 9.27 8.91 8.26 12 2" data-v-7d319dea></polygon></svg>`);
          }
          _push(`</button>`);
        });
        _push(`<!--]--></div></div>`);
      });
      _push(`<!--]--></div></div><div class="border border-gray-200 rounded-lg p-3 sm:p-4" data-v-7d319dea><h3 class="text-base sm:text-[18px] font-semibold mb-3 sm:mb-4" data-v-7d319dea>Review</h3><textarea placeholder="Any comments?" rows="4" class="w-full px-3 py-2 text-sm sm:text-base focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent resize-none bg-[#B0D4FC1F] rounded-md min-h-[100px] sm:min-h-[120px]" data-v-7d319dea>${ssrInterpolate(unref(reviewText))}</textarea></div><div class="flex justify-center sm:justify-end pt-2" data-v-7d319dea><button class="w-full sm:w-auto text-sm sm:text-[16px] px-6 sm:px-8 py-3 sm:py-2 bg-[#7B27E7] text-white rounded-full hover:bg-purple-700 transition-colors duration-200 font-medium cursor-pointer" data-v-7d319dea> Submit Review </button></div></div></div>`);
    };
  }
});
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/reviews/[orderId].vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const _orderId_ = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-7d319dea"]]);

export { _orderId_ as default };
//# sourceMappingURL=_orderId_-DU0Fg0sU.mjs.map
