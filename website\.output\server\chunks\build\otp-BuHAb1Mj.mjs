import { f as useRoute, b as useUserStore, a as __nuxt_component_0$2 } from './server.mjs';
import { defineComponent, ref, inject, mergeProps, withCtx, createTextVNode, useSSRContext } from 'vue';
import { ssrRenderAttrs, ssrRenderAttr, ssrInterpolate, ssrRenderList, ssrRenderClass, ssrRenderComponent } from 'vue/server-renderer';
import { p as publicAssetsURL } from '../_/nitro.mjs';
import { _ as _imports_2 } from './virtual_public-Cy4CASs_.mjs';
import { u as useLoader, _ as _imports_1 } from './useLoader-2w2M3dXZ.mjs';
import { u as useAppSeoMeta } from './useAppSeoMeta-HWukB6FN.mjs';
import 'pinia';
import 'vue-router';
import 'cookie';
import '@iconify/vue';
import 'tailwindcss/colors';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import 'unhead/server';
import 'devalue';
import 'unhead/plugins';
import 'unhead/utils';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:url';
import '@iconify/utils';
import 'node:crypto';
import 'consola';
import 'node:path';
import 'better-sqlite3';
import 'ipx';

const _imports_0 = publicAssetsURL("/icon/back-arrow.png");
const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "otp",
  __ssrInlineRender: true,
  setup(__props) {
    useAppSeoMeta({
      title: "OTP | Bookslotz",
      description: "Verify your OTP to complete your registration with Bookslotz.",
      image: "/images/auth-image.png"
    });
    const route = useRoute();
    const email = ref(route.query.email || "");
    const otpValue = ref("");
    const visualOtp = ref([]);
    ref(null);
    const isFocused = ref(false);
    useLoader();
    inject("showSnackbar");
    useUserStore();
    return (_ctx, _push, _parent, _attrs) => {
      const _component_NuxtLink = __nuxt_component_0$2;
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "grid grid-cols-12 w-full min-h-screen" }, _attrs))}><div class="col-span-12 md:col-span-7 flex flex-col justify-between py-6"><div class="pl-12 pb-10 md:pb-0 md:pt-12"><button><img${ssrRenderAttr("src", _imports_0)} alt="back-arrow" class="h-10 w-10 cursor-pointer"></button></div><div class="flex-1 flex flex-col justify-center items-center"><div class="w-full max-w-md"><div class="flex items-center justify-center mb-5"><div class="mr-2"><img${ssrRenderAttr("src", _imports_2)} alt="Bookslotz Logo" class="h-20 w-auto"></div></div><div class="text-center mb-6"><h2 class="font-extrabold text-2xl text-[#263238] mb-1"> OTP Verification </h2><p class="font-normal text-sm text-[#303030B2] pt-1"> Enter the OTP sent to <span class="font-semibold">${ssrInterpolate(email.value)}</span></p></div><div class="space-y-4 px-8"><form><div class="flex justify-center gap-4 mb-6"><input type="text" maxlength="4" inputmode="numeric" pattern="[0-9]*" class="opacity-0 absolute"${ssrRenderAttr("value", otpValue.value)} autocomplete="one-time-code"><!--[-->`);
      ssrRenderList(4, (digit, index) => {
        _push(`<div class="${ssrRenderClass([[
          visualOtp.value[index] ? "border-[#7B3FF2]" : "border-gray-400",
          index === visualOtp.value.length && isFocused.value ? "border-[#7B3FF2]" : ""
        ], "w-10 h-10 flex items-center justify-center border-b-2 text-center text-xl cursor-pointer"])}">${ssrInterpolate(visualOtp.value[index] || "")}</div>`);
      });
      _push(`<!--]--></div><button type="submit" class="w-full py-3 px-8 rounded-full bg-[#7B27E7] text-white font-semibold text-[16px] shadow hover:bg-[#6a2eea] transition duration-200"> Verify </button></form><div class="text-center mt-2 pt-4"><p class="text-sm text-[#000000]"> Didn&#39;t receive the OTP? <button class="font-semibold text-[#000000] hover:underline ml-1 cursor-pointer"> Resend OTP </button></p></div></div></div></div><div class="hidden md:flex justify-start pl-12 w-full mb-5 md:pt-8 lg:pt-12 text-xs text-black gap-4">`);
      _push(ssrRenderComponent(_component_NuxtLink, { class: "hover:text-gray-700" }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`Terms and conditions`);
          } else {
            return [
              createTextVNode("Terms and conditions")
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(ssrRenderComponent(_component_NuxtLink, { class: "hover:text-gray-700" }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`Privacy Policy`);
          } else {
            return [
              createTextVNode("Privacy Policy")
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`</div></div><div class="hidden md:flex col-span-12 md:col-span-5 lg:col-span-5 md:p-8 relative"><div class="relative h-full w-full rounded-tr-2xl rounded-br-2xl overflow-hidden shadow-lg"><img${ssrRenderAttr("src", _imports_1)} alt="Salon" class="h-full w-full object-cover"><div class="absolute inset-0 bg-gradient-to-t from-[#7B3FF2]/30 to-transparent"></div><div class="absolute bottom-14 right-12 text-xs text-gray-700 opacity-80"> \xA9 2025 All rights reserved </div></div></div></div>`);
    };
  }
});
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/auth/otp.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};

export { _sfc_main as default };
//# sourceMappingURL=otp-BuHAb1Mj.mjs.map
