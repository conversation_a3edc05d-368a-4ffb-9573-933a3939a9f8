{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "start": "node .output/server/index.mjs", "clean": "rimraf .nuxt .output"}, "dependencies": {"@googlemaps/js-api-loader": "^1.16.8", "@nuxt/content": "^3.5.1", "@nuxt/icon": "^1.13.0", "@nuxt/image": "^1.10.0", "@nuxt/ui": "^3.1.2", "@nuxtjs/sitemap": "^7.3.0", "@pinia/nuxt": "^0.11.0", "@tailwindcss/vite": "^4.1.7", "aos": "^2.3.4", "cookie": "^1.0.2", "cropperjs": "^2.0.0", "firebase": "^11.9.1", "leaflet": "^1.9.4", "lucide-vue-next": "^0.511.0", "nuxt": "^3.17.3", "tailwindcss": "^4.1.7", "typescript": "^5.8.3", "vue": "^3.5.14", "vue-advanced-cropper": "^2.8.9", "vue-cropperjs": "^5.0.0", "vue-router": "^4.5.1"}, "devDependencies": {"rimraf": "^6.0.1"}}