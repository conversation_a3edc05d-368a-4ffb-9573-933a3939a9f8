import { _ as _export_sfc, h as useNuxtApp, b as useUserStore, a as __nuxt_component_0$2 } from './server.mjs';
import { defineComponent, ref, inject, mergeProps, withCtx, createVNode, unref, createTextVNode, useSSRContext } from 'vue';
import { ssrRenderAttrs, ssrRenderComponent, ssrRenderAttr, ssrRenderClass, ssrInterpolate, ssrIncludeBooleanAttr } from 'vue/server-renderer';
import { _ as _imports_2 } from './virtual_public-Cy4CASs_.mjs';
import { u as useLoader, _ as _imports_1 } from './useLoader-2w2M3dXZ.mjs';
import { u as useAppSeoMeta } from './useAppSeoMeta-HWukB6FN.mjs';
import '../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:url';
import '@iconify/utils';
import 'node:crypto';
import 'consola';
import 'node:path';
import 'better-sqlite3';
import 'ipx';
import 'pinia';
import 'vue-router';
import 'cookie';
import '@iconify/vue';
import 'tailwindcss/colors';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import 'unhead/server';
import 'devalue';
import 'unhead/plugins';
import 'unhead/utils';

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "login",
  __ssrInlineRender: true,
  setup(__props) {
    useAppSeoMeta({
      title: "Login | Bookslotz",
      description: "Login to Bookslotz to manage your appointments and profile.",
      image: "/images/auth-image.png"
    });
    const { $firebaseAuth, $googleProvider, $appleProvider } = useNuxtApp();
    const email = ref("");
    const emailError = ref("");
    const isLoading = ref(false);
    useUserStore();
    useLoader();
    inject("showSnackbar");
    return (_ctx, _push, _parent, _attrs) => {
      const _component_NuxtLink = __nuxt_component_0$2;
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "h-screen overflow-y-auto" }, _attrs))} data-v-a1bc815d><div class="grid grid-cols-12 w-full h-full" data-v-a1bc815d><div class="col-span-12 md:col-span-7 flex flex-col justify-between py-6" data-v-a1bc815d><div class="flex-1 flex flex-col justify-center items-center" data-v-a1bc815d><div class="w-full max-w-md" data-v-a1bc815d><div class="flex items-center justify-center mb-5" data-v-a1bc815d><div class="mr-2" data-v-a1bc815d>`);
      _push(ssrRenderComponent(_component_NuxtLink, { to: "/" }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`<img class="h-20 w-auto"${ssrRenderAttr("src", _imports_2)} alt="logo" data-v-a1bc815d${_scopeId}>`);
          } else {
            return [
              createVNode("img", {
                class: "h-20 w-auto",
                src: _imports_2,
                alt: "logo"
              })
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`</div></div><div class="text-center mb-6" data-v-a1bc815d><h2 class="text-2xl text-[#263238] font-extrabold mb-1" data-v-a1bc815d>Hi Welcome back !</h2><p class="text-md font-normal text-[#303030B2] pt-1" data-v-a1bc815d> Enter your email to sign in to <br data-v-a1bc815d> your account </p></div><div class="space-y-4 px-8 pt-3" data-v-a1bc815d><form data-v-a1bc815d><div class="pb-4" data-v-a1bc815d><label class="block text-sm dm-sansmedium font-medium text-[#272727] mb-1" data-v-a1bc815d>Email</label><input type="email"${ssrRenderAttr("value", unref(email))} autocomplete="email" placeholder="<EMAIL>" class="${ssrRenderClass([[
        unref(emailError) ? "border-red-500 focus:ring-red-500" : "border-gray-200 focus:ring-[#7B3FF2]"
      ], "w-full px-4 font-light py-2 bg-gray-100 text-[#333333] border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#7B3FF2] transition"])}" data-v-a1bc815d>`);
      if (unref(emailError)) {
        _push(`<div class="text-red-500 text-xs mt-1" data-v-a1bc815d>${ssrInterpolate(unref(emailError))}</div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div><button type="submit"${ssrIncludeBooleanAttr(unref(isLoading)) ? " disabled" : ""} class="w-full bg-[#7B27E7] text-white py-2.5 rounded-full font-medium hover:bg-purple-700 transition duration-200 disabled:opacity-70 disabled:cursor-not-allowed" data-v-a1bc815d> Login </button></form><div class="relative flex items-center justify-center my-2 pt-5 pb-2" data-v-a1bc815d><div class="border-t border-gray-200 w-full" data-v-a1bc815d></div><div class="absolute bg-white px-4 text-xs font-normal text-gray-400" data-v-a1bc815d>or</div></div><button${ssrIncludeBooleanAttr(unref(isLoading)) ? " disabled" : ""} class="w-full flex items-center justify-center font-medium text-gray-700 gap-2 py-2 border bg-white border-gray-200 rounded-full shadow-sm hover:bg-gray-50 transition duration-200 cursor-pointer disabled:opacity-70 disabled:cursor-not-allowed" data-v-a1bc815d><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48" width="20px" height="20px" data-v-a1bc815d><path fill="#FFC107" d="M43.611,20.083H42V20H24v8h11.303c-1.649,4.657-6.08,8-11.303,8c-6.627,0-12-5.373-12-12c0-6.627,5.373-12,12-12c3.059,0,5.842,1.154,7.961,3.039l5.657-5.657C34.046,6.053,29.268,4,24,4C12.955,4,4,12.955,4,24c0,11.045,8.955,20,20,20c11.045,0,20-8.955,20-20C44,22.659,43.862,21.35,43.611,20.083z" data-v-a1bc815d></path><path fill="#FF3D00" d="M6.306,14.691l6.571,4.819C14.655,15.108,18.961,12,24,12c3.059,0,5.842,1.154,7.961,3.039l5.657-5.657C34.046,6.053,29.268,4,24,4C16.318,4,9.656,8.337,6.306,14.691z" data-v-a1bc815d></path><path fill="#4CAF50" d="M24,44c5.166,0,9.86-1.977,13.409-5.192l-6.19-5.238C29.211,35.091,26.715,36,24,36c-5.202,0-9.619-3.317-11.283-7.946l-6.522,5.025C9.505,39.556,16.227,44,24,44z" data-v-a1bc815d></path><path fill="#1976D2" d="M43.611,20.083H42V20H24v8h11.303c-0.792,2.237-2.231,4.166-4.087,5.571c0.001-0.001,0.002-0.001,0.003-0.002l6.19,5.238C36.971,39.205,44,34,44,24C44,22.659,43.862,21.35,43.611,20.083z" data-v-a1bc815d></path></svg> Continue with Google </button><button${ssrIncludeBooleanAttr(unref(isLoading)) ? " disabled" : ""} class="w-full flex items-center justify-center font-medium gap-2 py-2 bg-black text-white rounded-full hover:bg-gray-800 transition duration-200 cursor-pointer disabled:opacity-70 disabled:cursor-not-allowed" data-v-a1bc815d><svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor" data-v-a1bc815d><path d="M18.71 19.5C17.88 20.74 17 21.95 15.66 21.97C14.32 22 13.89 21.18 12.37 21.18C10.84 21.18 10.37 21.95 9.09997 22C7.78997 22.05 6.79997 20.68 5.95997 19.47C4.24997 17 2.93997 12.45 4.69997 9.39C5.56997 7.87 7.13997 6.91 8.85997 6.88C10.15 6.86 11.36 7.75 12.11 7.75C12.85 7.75 14.28 6.68 15.85 6.84C16.48 6.87 18.02 7.12 19.05 8.55C18.96 8.61 17.15 9.74 17.18 11.93C17.22 14.69 19.68 15.64 19.73 15.67C19.68 15.8 19.31 17.04 18.71 19.5ZM13 3.5C13.73 2.67 14.94 2.04 15.94 2C16.07 3.17 15.6 4.35 14.9 5.19C14.21 6.04 13.07 6.7 11.95 6.61C11.8 5.46 12.36 4.26 13 3.5Z" data-v-a1bc815d></path></svg> Continue with Apple </button><div class="text-center mt-2 pt-4 font-normal" data-v-a1bc815d><p class="text-sm" data-v-a1bc815d> Don&#39;t have an account? `);
      _push(ssrRenderComponent(_component_NuxtLink, {
        to: "/auth/signup",
        class: "text-[#1976D2] font-medium hover:underline"
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(` Signup `);
          } else {
            return [
              createTextVNode(" Signup ")
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`</p></div></div></div></div><div class="hidden md:flex justify-start pl-12 w-full font-normal mb-5 md:pt-8 lg:pt-0 text-xs gap-4" data-v-a1bc815d>`);
      _push(ssrRenderComponent(_component_NuxtLink, {
        to: "/terms-and-condition",
        class: "hover:text-gray-700"
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`Terms and conditions`);
          } else {
            return [
              createTextVNode("Terms and conditions")
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(ssrRenderComponent(_component_NuxtLink, {
        to: "/privacy-policy",
        class: "hover:text-gray-700"
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`Privacy Policy`);
          } else {
            return [
              createTextVNode("Privacy Policy")
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`</div></div><div class="hidden md:flex col-span-12 md:col-span-5 lg:col-span-5 md:p-8 font-normal relative" data-v-a1bc815d><img${ssrRenderAttr("src", _imports_1)} alt="login" class="h-full w-full object-cover rounded-tr-2xl rounded-br-2xl" data-v-a1bc815d><div class="absolute bottom-14 right-12 text-xs text-gray-700 opacity-80" data-v-a1bc815d> \xA9 2025 All rights reserved </div></div></div></div>`);
    };
  }
});
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/auth/login.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const login = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-a1bc815d"]]);

export { login as default };
//# sourceMappingURL=login-Cbxo4N33.mjs.map
