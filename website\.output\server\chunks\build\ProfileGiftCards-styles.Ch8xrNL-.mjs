import { P as ProfileGiftCards_vue_vue_type_style_index_0_scoped_8c74baac_lang } from './ProfileGiftCards-styles-1.mjs-BUCGLTmD.mjs';
import '../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:url';
import '@iconify/utils';
import 'node:crypto';
import 'consola';
import 'node:path';
import 'better-sqlite3';
import 'ipx';

const ProfileGiftCardsStyles_Ch8xrNL_ = [ProfileGiftCards_vue_vue_type_style_index_0_scoped_8c74baac_lang, ProfileGiftCards_vue_vue_type_style_index_0_scoped_8c74baac_lang];

export { ProfileGiftCardsStyles_Ch8xrNL_ as default };
//# sourceMappingURL=ProfileGiftCards-styles.Ch8xrNL-.mjs.map
