import { defineComponent, ref, mergeProps, unref, useSSRContext } from 'vue';
import { ssrRenderAttrs, ssrRenderComponent, ssrRenderList, ssrRenderStyle, ssrInterpolate, ssrRenderAttr } from 'vue/server-renderer';
import { useRouter } from 'vue-router';
import { _ as _export_sfc, b as useUserStore, c as useRuntimeConfig, d as useState, u as useSeoMeta, L as Loader, S as Snackbar, e as _sfc_main$1 } from './server.mjs';
import '../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:url';
import '@iconify/utils';
import 'node:crypto';
import 'consola';
import 'node:path';
import 'better-sqlite3';
import 'ipx';
import 'pinia';
import 'cookie';
import '@iconify/vue';
import 'tailwindcss/colors';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import 'unhead/server';
import 'devalue';
import 'unhead/plugins';
import 'unhead/utils';

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "search",
  __ssrInlineRender: true,
  setup(__props) {
    useUserStore();
    const { public: { BASE_URL } } = useRuntimeConfig();
    useRouter();
    const isLoading = useState("globalLoader", () => false);
    const hasError = ref(false);
    const fetchingCategories = ref(false);
    const isLoadingNearby = ref(false);
    const isLoadingTrending = ref(false);
    const isLoadingRecommended = ref(false);
    const categories = ref([]);
    const snackbarRef = ref(null);
    ref([
      {
        title: "Nearby Salons",
        route: "nearby",
        items: [],
        isLoading: false
      },
      {
        title: "Trending",
        route: "trending",
        items: [],
        isLoading: false
      },
      {
        title: "Recommended",
        route: "recommended",
        items: [],
        isLoading: false
      }
    ]);
    const getImageUrl = (image) => {
      if (!image) return "/images/saloon-image.jpg";
      if (image.startsWith("http")) return image;
      return `${BASE_URL}/wp/${image}`;
    };
    useSeoMeta({
      title: "Search Salons & Categories | Bookslotz",
      ogTitle: "Search Salons & Categories | Bookslotz",
      description: "Discover and book the best salons near you. Browse categories like Hair, Nails, Facials, and Massages on Bookslotz.",
      ogDescription: "Discover and book the best salons near you. Browse categories like Hair, Nails, Facials, and Massages on Bookslotz.",
      ogImage: "/og-image.jpg",
      twitterCard: "summary_large_image"
    });
    return (_ctx, _push, _parent, _attrs) => {
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "min-h-screen bg-white relative" }, _attrs))} data-v-430997f3>`);
      if (unref(isLoading) || fetchingCategories.value || isLoadingNearby.value || isLoadingTrending.value || isLoadingRecommended.value) {
        _push(ssrRenderComponent(Loader, null, null, _parent));
      } else {
        _push(`<!---->`);
      }
      _push(ssrRenderComponent(Snackbar, {
        ref_key: "snackbarRef",
        ref: snackbarRef
      }, null, _parent));
      if (hasError.value) {
        _push(ssrRenderComponent(_sfc_main$1, { error: { statusCode: 500, message: "Something went wrong. Please try again later." } }, null, _parent));
      } else {
        _push(`<!---->`);
      }
      if (!hasError.value) {
        _push(`<section class="py-8 bg-gray-50 min-h-screen" data-v-430997f3><div class="max-w-8xl mx-auto px-6 sm:px-16 md:px-20 space-y-12" data-v-430997f3><div data-v-430997f3><h2 class="text-2xl font-bold text-gray-900 mb-6" data-v-430997f3>Categories</h2><div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-4 gap-4" data-v-430997f3><!--[-->`);
        ssrRenderList(categories.value, (category, index) => {
          _push(`<div class="bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl overflow-hidden cursor-pointer hover:shadow-lg transition-all duration-300 group flex items-center" style="${ssrRenderStyle({ "background": "linear-gradient(270deg, #F6F7FF 0%, #E1E9FF 100%)" })}" data-v-430997f3><div class="flex-1 p-6" data-v-430997f3><h3 class="font-semibold text-gray-900 text-[16px] text-[500] leading-tight" data-v-430997f3>${ssrInterpolate(category.name)}</h3></div><div class="relative w-24 h-20 overflow-hidden" data-v-430997f3><img${ssrRenderAttr("src", getImageUrl(category.image))}${ssrRenderAttr("alt", category.name)} class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300" data-v-430997f3></div></div>`);
        });
        _push(`<!--]--></div>`);
        if (!fetchingCategories.value && categories.value.length === 0) {
          _push(`<div class="text-center py-12" data-v-430997f3><div class="text-gray-400 mb-4" data-v-430997f3><svg class="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-430997f3><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" data-v-430997f3></path></svg></div><p class="text-gray-500 text-lg" data-v-430997f3>No categories available</p><button class="mt-4 text-blue-600 hover:text-blue-700 font-medium" data-v-430997f3> Try again </button></div>`);
        } else {
          _push(`<!---->`);
        }
        _push(`</div></div></section>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div>`);
    };
  }
});
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/search.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const search = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-430997f3"]]);

export { search as default };
//# sourceMappingURL=search-8uUQBBTh.mjs.map
