import { defineComponent, ref, mergeProps, useSSRContext } from 'vue';
import { ssrRenderAttrs, ssrRenderStyle, ssrInterpolate, ssrRenderAttr, ssrRenderClass, ssrIncludeBooleanAttr } from 'vue/server-renderer';
import { useRouter } from 'vue-router';
import { u as useBookingStore } from './booking-D2Du41fq.mjs';
import { _ as _export_sfc, g as useHead } from './server.mjs';
import 'pinia';
import '../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:url';
import '@iconify/utils';
import 'node:crypto';
import 'consola';
import 'node:path';
import 'better-sqlite3';
import 'ipx';
import 'cookie';
import '@iconify/vue';
import 'tailwindcss/colors';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import 'unhead/server';
import 'devalue';
import 'unhead/plugins';
import 'unhead/utils';

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "payment",
  __ssrInlineRender: true,
  setup(__props) {
    const showSuccessModal = ref(false);
    useRouter();
    useBookingStore();
    const selectedMembership = ref(null);
    function getExpiryDate() {
      var _a;
      if (!((_a = selectedMembership.value) == null ? void 0 : _a.expiry)) return "";
      const now = /* @__PURE__ */ new Date();
      const expiryText = selectedMembership.value.expiry;
      const monthsMatch = expiryText.match(/(\d+)\s*Month/i);
      const expiryMonths = monthsMatch ? parseInt(monthsMatch[1]) : 1;
      const expiryDate = new Date(now.getFullYear(), now.getMonth() + expiryMonths, now.getDate());
      return expiryDate.toLocaleDateString("en-GB", {
        day: "2-digit",
        month: "2-digit",
        year: "numeric"
      });
    }
    useHead({
      title: "Payment | Buy Membership | Glow & Shine Salon",
      meta: [
        {
          name: "description",
          content: "Complete your membership purchase at Glow & Shine Salon. Review your selected membership plan and proceed to payment."
        },
        {
          name: "keywords",
          content: "salon membership payment, premium membership, beauty salon payment, Glow & Shine Salon, Mumbai salon"
        }
      ]
    });
    return (_ctx, _push, _parent, _attrs) => {
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "min-h-screen bg-white" }, _attrs))} data-v-71d29216><div class="flex flex-col xl:flex-row max-w-8xl mx-auto px-1 sm:px-12 xl:justify-center xl:min-h-screen" data-v-71d29216><div class="flex-1 lg:flex-[0.4] px-6 lg:px-8 py-6 lg:py-8" data-v-71d29216><div class="flex items-center space-x-3 mb-3" data-v-71d29216><button class="w-10 h-10 rounded-full border border-gray-300 flex items-center justify-center group cursor-pointer" data-v-71d29216><svg xmlns="http://www.w3.org/2000/svg" class="w-7 h-7 text-purple-600 group-hover:text-purple-700 transition-colors" viewBox="0 0 24 24" fill="currentColor" data-v-71d29216><path d="m6.523 12.5l3.735 3.735q.146.146.153.344q.006.198-.153.363q-.166.166-.357.168t-.357-.162l-4.382-4.383q-.243-.242-.243-.565t.243-.566l4.382-4.382q.147-.146.347-.153q.201-.007.367.159q.16.165.162.353q.003.189-.162.354L6.523 11.5h12.38q.214 0 .358.143t.143.357t-.143.357t-.357.143z" data-v-71d29216></path></svg></button><h1 class="text-[24px] font-semibold text-[700]" data-v-71d29216>Buy Membership</h1></div>`);
      if (selectedMembership.value) {
        _push(`<div class="border border-gray-200 rounded-2xl p-6" data-v-71d29216><div class="p-5 rounded-2xl" style="${ssrRenderStyle({ background: selectedMembership.value.gradient })}" data-v-71d29216><div class="text-white flex flex-col items-center sm:items-start text-center sm:text-left" data-v-71d29216><h3 class="text-[20px] font-semibold mb-2" data-v-71d29216>${ssrInterpolate(selectedMembership.value.name)}</h3><p class="text-[16px] mb-1 opacity-90" data-v-71d29216> Membership Plan: ${ssrInterpolate(selectedMembership.value.type)}</p><p class="text-[16px] mb-1 opacity-90" data-v-71d29216> Total Use count: ${ssrInterpolate(selectedMembership.value.useCount)}</p><p class="text-[16px] mb-1 opacity-90" data-v-71d29216> Expiry: ${ssrInterpolate(selectedMembership.value.expiry)}</p><div class="flex flex-col sm:flex-row sm:items-center sm:justify-between text-[16px] mb-4 opacity-90 w-full mt-2" data-v-71d29216><p data-v-71d29216>Services: 5 Services (Hair Spa, Cleanup, Waxing, Nail, Facial)</p><span class="text-[20px] font-semibold text-white sm:ml-auto mt-2 sm:mt-0 sm:pl-5" data-v-71d29216> \u20B9${ssrInterpolate(selectedMembership.value.price)}</span></div></div></div></div>`);
      } else {
        _push(`<div class="text-center py-12" data-v-71d29216><div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4" data-v-71d29216><svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-71d29216><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" data-v-71d29216></path></svg></div><p class="text-gray-500" data-v-71d29216>No membership selected</p><button class="mt-4 text-purple-600 hover:text-purple-700 font-medium" data-v-71d29216> Select Membership </button></div>`);
      }
      _push(`</div><div class="w-full lg:flex-[0.4] xl:w-[400px] bg-white p-6 lg:p-8 xl:p-0 mt-0 xl:mt-20" data-v-71d29216><div class="bg-white rounded-2xl shadow-sm border border-gray-100 p-6 space-y-6" data-v-71d29216>`);
      if (selectedMembership.value) {
        _push(`<div data-v-71d29216><div class="flex items-center gap-3 p-3 rounded-lg bg-white border border-gray-100" data-v-71d29216><div class="w-12 h-12 rounded-lg bg-gray-200 flex items-center justify-center overflow-hidden flex-shrink-0" data-v-71d29216><img${ssrRenderAttr("src", selectedMembership.value.salon.image)} alt="salon" class="w-full h-full object-cover" data-v-71d29216></div><div class="flex-1 min-w-0" data-v-71d29216><h3 class="text-[18px] font-semibold text-[700] truncate" data-v-71d29216>${ssrInterpolate(selectedMembership.value.salon.name)}</h3><div class="flex items-center gap-1 text-[14px] text-[400] text-gray-500" data-v-71d29216><svg class="w-3 h-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-71d29216><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" data-v-71d29216></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" data-v-71d29216></path></svg><span class="truncate" data-v-71d29216>${ssrInterpolate(selectedMembership.value.salon.location)}</span></div></div></div><div class="mt-4 space-y-1" data-v-71d29216><div class="text-[16px] font-semibold" data-v-71d29216> 5 Services (Hair Spa, Cleanup, Waxing, Nail, Facial) </div><div class="flex items-center gap-2 text-[14px] text-gray-500" data-v-71d29216><svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-71d29216><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" data-v-71d29216></path></svg><span class="text-[15px]" data-v-71d29216>Valid for ${ssrInterpolate(selectedMembership.value.expiry)}</span></div><div class="text-[14px] text-gray-400" data-v-71d29216> Expires on ${ssrInterpolate(getExpiryDate())}</div></div></div>`);
      } else {
        _push(`<!---->`);
      }
      if (selectedMembership.value) {
        _push(`<hr class="border-gray-200" data-v-71d29216>`);
      } else {
        _push(`<!---->`);
      }
      if (selectedMembership.value) {
        _push(`<div class="flex justify-between items-center" data-v-71d29216><span class="text-[18px] font-semibold text-gray-900" data-v-71d29216>Total</span><span class="text-[18px] font-semibold text-gray-900" data-v-71d29216> \u20B9${ssrInterpolate(selectedMembership.value.price)}</span></div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`<button${ssrIncludeBooleanAttr(!selectedMembership.value) ? " disabled" : ""} class="${ssrRenderClass([selectedMembership.value ? "bg-purple-600 hover:bg-purple-700" : "bg-gray-300 cursor-not-allowed", "w-full py-3 rounded-full text-white font-semibold text-[16px] transition-colors cursor-pointer"])}" data-v-71d29216> Pay Now </button></div></div></div>`);
      if (showSuccessModal.value) {
        _push(`<div class="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-3 sm:p-4" data-v-71d29216><div class="bg-white rounded-2xl sm:rounded-3xl w-full max-w-2xl sm:max-w-3xl mx-auto text-center relative overflow-hidden" style="${ssrRenderStyle({ "max-height": "90vh" })}" data-v-71d29216><div class="p-6 sm:p-8" data-v-71d29216><div class="relative mb-6 sm:mb-8 flex justify-center" data-v-71d29216><div class="absolute -top-3 sm:-top-4 -left-6 sm:-left-8 w-2 sm:w-3 h-2 sm:h-3 bg-red-400 rounded-full animate-float-1" data-v-71d29216></div><div class="absolute -top-4 sm:-top-6 left-8 sm:left-12 w-1.5 sm:w-2 h-1.5 sm:h-2 bg-orange-400 rounded-full animate-float-2" data-v-71d29216></div><div class="absolute top-1 sm:top-2 -right-6 sm:-right-8 w-3 sm:w-4 h-3 sm:h-4 bg-pink-400 rounded-full animate-float-3" data-v-71d29216></div><div class="absolute top-8 sm:top-12 -left-4 sm:-left-6 w-1.5 sm:w-2 h-1.5 sm:h-2 bg-blue-400 rounded-full animate-float-4" data-v-71d29216></div><div class="absolute -bottom-1 sm:-bottom-2 -right-3 sm:-right-4 w-2 sm:w-3 h-2 sm:h-3 bg-green-400 rounded-full animate-float-5" data-v-71d29216></div><div class="absolute -bottom-3 sm:-bottom-4 left-6 sm:left-8 w-1.5 sm:w-2 h-1.5 sm:h-2 bg-purple-400 rounded-full animate-float-6" data-v-71d29216></div><div class="relative" data-v-71d29216><div class="absolute inset-0 w-16 sm:w-20 h-16 sm:h-20 border-2 border-purple-200 rounded-full animate-pulse-ring opacity-30" data-v-71d29216></div><div class="absolute inset-0.5 sm:inset-1 w-15 sm:w-18 h-15 sm:h-18 border-2 border-purple-300 rounded-full animate-pulse-ring-delayed opacity-20" data-v-71d29216></div><div class="relative w-16 sm:w-20 h-16 sm:h-20 bg-gradient-to-br from-purple-500 to-purple-700 rounded-full flex items-center justify-center shadow-lg animate-scale-in" data-v-71d29216><svg class="w-8 sm:w-10 h-8 sm:h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="3" data-v-71d29216><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7" data-v-71d29216></path></svg></div></div></div><h2 class="text-xl sm:text-2xl font-bold text-gray-900 mb-2 sm:mb-3 animate-fade-in-up leading-tight" data-v-71d29216> Membership Activated Successfully! </h2><p class="text-gray-600 text-sm sm:text-base mb-6 sm:mb-8 leading-relaxed animate-fade-in-up-delayed px-2 sm:px-0" data-v-71d29216> You&#39;re now a member of Glow &amp; Shine Salon under the Premium Glow plan. </p><button class="px-10 py-3 sm:py-4 bg-gradient-to-r from-purple-600 to-purple-700 text-white font-semibold text-sm sm:text-base rounded-full hover:from-purple-700 hover:to-purple-800 transition-all duration-200 transform hover:scale-105 shadow-lg animate-fade-in-up-delayed cursor-pointer" data-v-71d29216> View My Memberships </button></div></div></div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`</div>`);
    };
  }
});
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/membership/payment.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const payment = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-71d29216"]]);

export { payment as default };
//# sourceMappingURL=payment-XiCP3V3k.mjs.map
